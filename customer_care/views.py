#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Imported by <PERSON>"""
import os
import gc
import requests
import json
import logging
from passlib.hash import pbkdf2_sha256
from functools import reduce
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from datetime import datetime
from datetime import date as date_module
from rest_framework import status as http_status
from django.utils import timezone
from bson.objectid import ObjectId
import sys
import validictory
from customer_care.validation_schema import validation_model
from customer_care import user_define_methods
from administrator.models import (
    UserManagement,
    LocationManagement,
    HubManagement,
    ExpanceEmployee,
)
from administrator.serializers import (
    LocationGettingSerializerShorteningIndent,
    UserManagementSerializer,
    OrderApprovedSerializer,
    MyDevice,
    ContractSerializerIndent,
)
from rest_framework.decorators import api_view
from django.db.models import Q
from mongoengine.queryset.visitor import Q as Qm
from django.views.decorators.csrf import csrf_protect, ensure_csrf_cookie
from registration.models import Driver
from trip.auto_data_creation import create_trip, change_contract_update_trip
from django.contrib.auth import logout
from django.contrib.auth.models import User
from django.http import HttpResponseRedirect
from django.views.generic import View
from rest_framework import parsers
from rest_framework import renderers
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.serializers import AuthTokenSerializer
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from customer_care.models import (
    Customer,
    Favorite,
    Indent,
    Order,
    Contract,
    ExpenseManagement,
    IndentOrderLogs,
    TollDetails,
    AdHocIndent,
    Bid,
    PaymentMultiple,
    TimeEfficiency,
    PlacementEfficiencyTime,
    BrokerRateMatrix,
    HourCheck,
    GoboltBusinessApp,
    GoboltBusinessVehicles,
    GoboltBusinessRejectedOrders,
    GoboltBusinessTripDocuments
)
from customer_care.serializers import (
    CustomerDataSerializer,
    LocationSerializer,
    IndentSerializer,
    VehicleTypeSerializer,
    OrderSerializer,
    OrderSerializerUnassign,
    FavoriteIndentSerializer,
    OrderAssignSerializer,
    ExpenseSerializer,
    ExpenseAdvanceSerializer,
    IndentBasedOrderSerializer,
    TollSerializer,
    CustomerSerializer,
    MarketOrderSerializer,
)
from registration.views import user_login_required
from trip.models import Trip, TripStatusMaster
from trip import global_objects
from django.db import transaction
from django.db.models import Prefetch

# import itertools
# Ashish
import gc
import re
import csv
# from underscore import *
# from underscore import _
from bcrypt import hashpw
from geopy.distance import vincenty
from registration.models import Entity
from vehicle.models import (
    EntityPayments,
    Assets,
    Vehicles,
    VehicleType,
    VehicleBrokerRel,
)
from employee.models import EmployeeAccount
from administrator.payment_operations import operations_p
from trip.external_methods import (
    return_user_type,
    gmaps,
    own_h_data_admin,
    get_status_master,
    broker_rate_return_data,
    return_contract_broker_data,
    vahicle_placement_check
)
from trip.network_api import network_voda, network_airtel
from administrator import status_code, otp_generation, paginator_file, send_sms
from broker.models import Broker, BrokerCityDetails
from gobolt_app.external_methods import send_noti
from es import views
import timeit
from trip.models import MasterDelayFilter
from trip.py_mongo_service import pymongo_crud
import io
from google_pub_sub.pub import Pub
from revproxy.views import ProxyView
from django.conf import settings
from third_party_auth.jwt_auth import GetJWTToken
from collections import deque
from vehicle.constants.constants import VehicleSourcing
from registration.constants import DriverActiveStatus
from payments_moudle.payments_module_client import PaymentsModules
from fuel_pump_module.fuel_pump import FuelPump
from maintenance_module.ops_maintenance import OpsMaintenance
from trip.utils import round_decimals_up
from django.core.exceptions import PermissionDenied

LOGGER = logging.getLogger(__name__)


def cc_logout(request):
    logout(request)
    return HttpResponseRedirect("/gobolt/customer_care-login/")


class CCLogin(ObtainAuthToken):
    throttle_classes = ()
    permission_classes = (AllowAny,)
    parser_classes = (
        parsers.FormParser,
        parsers.MultiPartParser,
        parsers.JSONParser,
    )
    renderer_classes = (renderers.JSONRenderer,)
    serializer_class = AuthTokenSerializer

    def post(self, request, **kwargs):
        status = False
        camp_details = 1
        _data = request.data
        if not _data:
            return Response({"code": 400, "message": "User data is required"})
        username = request.data.get("username")
        if (username is None) or (_data["username"].strip() == ""):
            return Response({"code": 400, "message": "Username is required"})
        my_password = request.data.get("password")
        if (my_password is None) or (_data["password"].strip() == ""):
            return Response({"code": 400, "message": "Password is required"})
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            return Response(
                {"code": 400, "message": "Something went wrong try again later"}
            )
        user = serializer.validated_data["user"]
        if user is None:
            return Response(
                {"code": 401, "message": "user email or password does not match"}
            )
        token, created = Token.objects.get_or_create(user=user)
        return Response(
            {"code": 200, "token": token.key, "message": "User login Successful"}
        )


#################################################################################################
# Name- CustomerRegistration                                                                    #
# operation - Customer Registration0                                                            #
#################################################################################################


class CustomerRegistration(APIView):
    def get(self, request):
        pass

    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        _data = request.data
        data = request.data
        try:
            if validictory.validate(data, validation_model.schema_customer) is None:
                # Ashish
                customer_code = str(data["customer_code"]).upper()
                if user_define_methods.check_username_data(_data["email"]) is not True:
                    basis = user_define_methods.check_company_code_name(
                        _data["company_name"], customer_code
                    )
                    if not len(basis):
                        if 3 <= len(customer_code) <= 4:
                            try:
                                user = User.objects.create(username=_data["email"])
                                password = "123456"
                                user.set_password(password)
                                user._data = _data.copy()
                                first_name = _data["name_first"]
                                user.first_name = first_name
                                user.last_name = _data["name_last"]
                                user.email = _data["email"]
                                user.save()
                                customer_name = first_name + _data["name_last"]
                                try:
                                    customer = Customer.objects.create(
                                        user_id=user.id,
                                        customer_name=customer_name,
                                        company_name=data["company_name"],
                                        address=data["address"],
                                        customer_code=customer_code,
                                        phone_number=data["phone_number"],
                                        account_manager_id=data["accManager_id"]["id"],
                                        bilty_boy_id=data["bilty_id"]["id"],
                                        sourcing_agent_id=data["agent"]["id"],
                                        sac=data["sac"],
                                        created_by = request.auth.user.username,
                                        created_at = timezone.now(),
                                    )
                                except Exception as e:
                                    print(e)
                                    user.delete()
                                    return Response({"code": status_code.code_501})
                                try:
                                    user_manage = UserManagement(
                                        user_id=user.id, is_customer=True
                                    )
                                    user_manage.save()
                                except Exception as e:
                                    customer.delete()
                                    print(
                                        "Exception is :- \n~",
                                        e.args[0],
                                        "\noccurred at line number:",
                                        sys.exc_info()[2].tb_lineno,
                                    )
                                    return Response({"code": status_code.code_501})
                            except Exception as e:
                                print(
                                    "Exception is :- \n~",
                                    e.args[0],
                                    "\noccurred at line number:",
                                    sys.exc_info()[2].tb_lineno,
                                )
                                return Response({"code": status_code.code_501})
                        else:
                            return Response(
                                {
                                    "code": {
                                        "status": 300,
                                        "message": "Please Check code length",
                                    }
                                }
                            )
                    else:
                        return Response(
                            {
                                "code": {
                                    "status": 202,
                                    "message": basis + " already registered",
                                }
                            }
                        )
                else:
                    return Response({"code": status_code.code_202})
                # Now insert in elastic search
                # 'customer_name', 'id', 'user_id', 'customer_code', 'phone_number',
                #                      'company_name'
                try:
                    views.single_data_insertion(
                        name="customer",
                        data=[
                            {
                                "id": customer.id,
                                "user_id": customer.user_id,
                                "customer_code": customer.customer_code,
                                "phone_number": customer.phone_number,
                                "company_name": customer.company_name,
                                "customer_name": customer.customer_name,
                            }
                        ],
                    )
                except Exception as e:
                    print("Elastic error", e)
                    pass
                return Response({"code": status_code.code_200})
        except Exception as e:
            return Response({"code": status_code.code_300})

    @method_decorator(login_required(login_url="/"))
    def put(self, request):
        data = request.data
        try:
            if (
                validictory.validate(data, validation_model.schema_customer_edit)
                is None
            ):
                user_data = data["user"]
                (
                    user_obj_data,
                    customer_data,
                    user_filter_data,
                ) = user_define_methods.edit_data(
                    Customer, data["id"], user_data["email"]
                )

                if user_filter_data is not None:
                    first_name = user_data["first_name"]
                    last_name = user_data["last_name"]
                    email = user_data["email"]
                    phone_number = data["phone_number"]
                    company_name = data["company_name"]
                    # Ashish
                    basis = user_define_methods.check_company_code_name_update(
                        company_name, data["customer_code"], data["id"]
                    )
                    if len(basis):
                        try:
                            user_obj_data.username = email
                            user_obj_data.first_name = first_name
                            user_obj_data.last_name = last_name
                            user_obj_data.email = email
                            user_obj_data.save()
                            customer_name = first_name + last_name
                            customer_data.update(
                                company_name=company_name,
                                customer_name=customer_name,
                                phone_number=phone_number,
                                address=data["address"],
                                account_manager_id=data["accManager_id"]["id"],
                                bilty_boy_id=data["bilty_id"]["id"],
                                sourcing_agent_id=data["agent"]["id"],
                                sac=data["sac"],
                                updated_at = timezone.now(),
                                updated_by = request.auth.user.username,
                            )
                            # send Email
                        except Exception as e:
                            print(e)
                            return Response({"code": status_code.code_501})
                        # Now update elastic data
                        try:
                            views.data_updation(
                                name="customer",
                                data=[
                                    {
                                        "id": customer_data[0].id,
                                        "phone_number": phone_number,
                                        "company_name": company_name,
                                        "customer_name": customer_name,
                                    }
                                ],
                            )
                        except Exception as e:
                            pass
                        return Response({"code": status_code.code_200})
                    else:
                        return Response(
                            {
                                "code": {
                                    "status": 202,
                                    "message": basis + " already registered",
                                }
                            }
                        )
            else:
                return Response({"code": status_code.code_203})

        except Exception as e:
            print(
                "Exception is :- \n~",
                e.args[0],
                "\noccurred at line number:",
                sys.exc_info()[2].tb_lineno,
            )
            return Response({"code": status_code.code_300})


#####################################################################################################################
# Name - CustomerListing                                                                                            #
# Operation - Getting Customer Listing                                                                              #
####################################################################################################################
class CustomerListing(APIView):
    def get(self, request):
        customer = Customer.objects.filter().order_by("-id")
        try:
            customer = CustomerDataSerializer(customer, many=True).data
            y = iter(customer)
            customer = None
            del customer
            gc.collect()
        except Exception as e:
            print(e)
        return Response({"code": status_code.code_200, "customer": y})

    def post(self, request):
        pass


class CustomerList(APIView):
    filter_fields = ("id", "customer_code")
    queryset = Customer.objects.filter(is_delete=False).order_by("-id")

    def get_kwargs_for_filtering(self):
        filtering_kwargs = {}
        for field in self.filter_fields:
            field_value = self.request.query_params.getlist(field)
            if field_value:
                filtering_kwargs[field + "__in"] = field_value
        return filtering_kwargs

    def get(self, request):
        filtering_kwargs = self.get_kwargs_for_filtering()
        customers = self.queryset.filter(**filtering_kwargs)
        code, message, status = 1, "OK", http_status.HTTP_200_OK
        data_list = CustomerSerializer(customers, many=True).data
        return Response(
            {"code": code, "message": message, "details": data_list}, status=status
        )


class CustomerEditing(APIView):
    def get(self, request, *args, **kwargs):
        customer = Customer.objects.filter(id=kwargs["pk"]).first()
        customer = CustomerDataSerializer(customer, many=False).data
        return Response({"code": status_code.code_200, "customer": customer})

    def post(self, request, *args, **kwargs):
        pass


class CustomerEs(APIView):
    def get(self, request):
        es_flag = False
        try:
            # This is elastic data

            customer = views.get_customer_all()
            # LOGGER.info(f"customer_list - {customer}")
            total_cus = customer["hits"]["total"]
            customer = customer["hits"]["hits"]
            vehicle_type = views.get_vehicle_type_all()
            es_flag = True
            # print(customer)

        except:
            customer = Customer.objects.all().iterator()
            customer = CustomerDataSerializer(customer, many=True).data
            vehicle_type = VehicleTypeSerializer(
                VehicleType.objects.filter(is_delete=False).iterator(), many=True
            ).data

        c, v = iter(customer), iter(vehicle_type)
        del customer, vehicle_type
        gc.collect()

        if es_flag:
            return Response(
                {"code": status_code.code_100, "customer": c, "vehicle_type": v}
            )
        else:

            return Response(
                {"code": status_code.code_200, "customer": c, "vehicle_type": v}
            )


class CustomerAppEs(APIView):
    def get(self, request):
        es_flag = False
        try:
            # This is elastic data
            customer = views.get_customer_all()
            total_cus = customer["hits"]["total"]
            customer = customer["hits"]["hits"]
            vehicle_type = views.get_vehicle_type_all()
            es_flag = True
            # print(customer)

        except:
            customer = Customer.objects.all().iterator()
            customer = CustomerDataSerializer(customer, many=True).data
            vehicle_type = VehicleTypeSerializer(
                VehicleType.objects.filter(is_delete=False).iterator(), many=True
            ).data

        ndf_objs = (
            {"name": i.name, "value": i.delay_fields}
            for i in MasterDelayFilter.objects.filter(status=True)
            .order_by("id")
            .iterator()
        )
        c, v = iter(customer), iter(vehicle_type)
        del customer, vehicle_type
        gc.collect()

        if es_flag:
            return Response(
                {
                    "code": status_code.code_100,
                    "customer": c,
                    "vehicle_type": v,
                    "delay_filter": ndf_objs,
                }
            )
        else:

            return Response(
                {
                    "code": status_code.code_200,
                    "customer": c,
                    "vehicle_type": v,
                    "delay_filter": ndf_objs,
                }
            )


class UpdateRefId(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        resp_status = http_status.HTTP_200_OK
        response_data = {"message": "Ok", "code": resp_status, "details": None}
        try:
            FAILED_REF_UPDATED = []
            valid_update_status = [
                "New",
                "Planned",
                "Vehicle Placed",
                "Placement Initiated",
                "Trip Start Requested",
                "MKT Vehicle Assigned",
            ]
            data = request.data
            if (
                validictory.validate(data, validation_model.schema_update_ref_id)
                is None
            ):
                for order_data in data["orders"]:
                    order_code = order_data.get("order_id")
                    demand_reference_id = order_data.get("demand_reference_id")
                    order_obj = Order.objects.get(order_code=order_code)
                    try:
                        trip = Trip.objects.filter(
                            trip_status__status_name__in=valid_update_status,
                            order_id=order_obj.id,
                        ).first()
                        if not trip:
                            raise ValueError(
                                f"Can not update demand reference "
                                f"id as trip is already started for order {order_code}"
                            )

                        order_obj.demand_reference_id = demand_reference_id
                        order_obj.save(update_fields=["demand_reference_id"])
                        response_data["message"] = "Data has been updated succesfully"
                    except ValueError as exc:
                        FAILED_REF_UPDATED.append(str(exc))
                if FAILED_REF_UPDATED:
                    response_data["details"] = "\n".join(FAILED_REF_UPDATED)
                    response_data[
                        "message"
                    ] = "Demand Ref ID updated partially, for trip which is not started yet!"
                    return Response(response_data)

            else:
                response_data["message"] = "Invalid Data"
                response_data["code"] = 400
                return Response(response_data)
        except Exception:
            LOGGER.error("Some unknown error occured!", exc_info=True)
            response_data["message"] = "Internal Server Error"
            response_data["code"] = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            return Response(response_data)
        return Response(response_data)


class CreateIndent(APIView):
    @method_decorator(login_required(login_url="/"))
    def get(self, request):
        es_flag = False
        try:
            if not request.auth.user.is_superuser and not request.auth.user.is_staff:
                customer = Customer.objects.all().iterator()
                customer = CustomerDataSerializer(customer, many=True).data
                vehicle_type = VehicleTypeSerializer(
                    VehicleType.objects.filter(is_delete=False).iterator(), many=True
                ).data
            elif request.auth.user.is_superuser or request.auth.user.is_staff:
                customer = Customer.objects.all().iterator()
                customer = CustomerDataSerializer(customer, many=True).data
                vehicle_type = VehicleTypeSerializer(
                    VehicleType.objects.filter(is_delete=False).iterator(), many=True
                ).data
            else:
                return Response({"code": {"status": 404, "message": "Access Denied"}})

            contract_ids = (
                Contract.objects.all()
                .values_list("contract_id", flat=True)
                .exclude(contract_id="")
            )
            c, v, c_ids = iter(customer), iter(vehicle_type), iter(contract_ids)
            customer, contract_ids, vehicle_type = None, None, None
            del customer, vehicle_type, contract_ids
            gc.collect()
            if es_flag:
                return Response(
                    {
                        "code": status_code.code_100,
                        "customer": c,
                        "vehicle_type": v,
                        "contract_ids": c_ids,
                    }
                )
            else:

                return Response(
                    {
                        "code": status_code.code_200,
                        "customer": c,
                        "vehicle_type": v,
                        "contract_ids": c_ids,
                    }
                )
        except Exception as e:
            return Response(
                {"code": status_code.code_201, "customer": [], "vehicle_type": []}
            )

    @method_decorator(login_required(login_url="/"))
    @method_decorator(transaction.atomic)
    def post(self, request):
        data = request.data
        try:
            s = []
            if data.get("demand_reference_id"):
                s = set(data.get("demand_reference_id"))
            demand_ref_queue = deque(s)

            if validictory.validate(data, validation_model.schema_indent) is None:
                date = datetime.strptime(data["date_vehicle_required"], "%Y-%m-%d")
                time = data["time_vehicle_required"]
                time = datetime.strptime(time, "%H:%M")
                contract_code = data["contract_id"]
                manifest_number = data.get("manifest_number")
                tat = data.get("tat")
                d = date_module.today()
                if  data.get("is_repetitive"):
                    if date_module(date.year, date.month, date.day)<d:
                        return Response({"code": {'status': 405, 'message': 'Repetitive indents can not be created for past dates'}})

                if manifest_number:
                    indent_objs = Indent.objects.filter(
                        customer_id=data["customer"]["id"],
                        manifest_number=manifest_number,
                    )

                    if indent_objs.exists():
                        return Response(
                            {
                                "code": {
                                    "status": 502,
                                    "message": "Indent already created using this manifest no.",
                                }
                            }
                        )

                vehicle_req_datetime = datetime.combine(date, time.time())
                checked = user_define_methods.check_hour_validation(
                    required_datetime=vehicle_req_datetime
                )

                if not checked:
                    return Response({"code": status_code.code_406})

                user_obj = UserManagement.objects.filter(user_id=request.user.id)
                user_ser = UserManagementSerializer(user_obj, many=True).data
                user_code = user_ser[0]["user_code"]
                user_first_name = user_ser[0]["user_manage_data"]["first_name"]

                coming_d = list()
                if len(data["destination_point"]) > 1:
                    coming_d = [
                        j["destination_point"]["id"]
                        for j in data["destination_point"][:-1]
                    ]

                last_destination = data["destination_point"][-1]
                last_des_id = last_destination["destination_point"]["id"]

                start_point_obj = LocationManagement.objects.get(
                    id=data["start_point"]["id"]
                )
                destination_point_obj = LocationManagement.objects.get(id=last_des_id)

                data["start_point"]["location_city"] = start_point_obj["location_city"]
                data["start_point"]["location_point"] = start_point_obj[
                    "location_point"
                ]
                start_city = data["start_point"]["location_city"]["name"]

                last_destination["destination_point"][
                    "location_city"
                ] = destination_point_obj["location_city"]
                last_destination["destination_point"][
                    "location_point"
                ] = destination_point_obj["location_point"]
                end_city = last_destination["destination_point"]["location_city"][
                    "name"
                ]

                cus_obj = Customer.objects.get(id=int(data["customer"]["id"]))
                try:
                    bilty_boy = user_define_methods.creating_user_dict(
                        cus_obj.bilty_boy.user.username, cus_obj.bilty_boy.user.id
                    )
                    account_manager = user_define_methods.creating_user_dict(
                        cus_obj.account_manager.user.username,
                        cus_obj.account_manager.user.id,
                    )
                except Exception as e:
                    return Response(
                        {
                            "code": {
                                "status": 300,
                                "message": "No Bilty Boy and Account Manager Found.",
                            }
                        }
                    )

                if len(data["destination_point"]) == 1:
                    contract_obj = Contract.objects.filter(
                        Q(customer_id=int(data["customer"]["id"]))
                        & Q(vehicle_type_id=data["vehicle_type"]["id"])
                        & Q(start_point__contains={"id": data["start_point"]["id"]})
                        & Q(
                            destination_point__contains={
                                "id": last_destination["destination_point"]["id"]
                            }
                        )
                        & Q(contract_id=contract_code)
                    )
                else:
                    contract_obj = list()
                    c_obj = Contract.objects.filter(
                        Q(customer_id=int(data["customer"]["id"]))
                        & Q(vehicle_type_id=data["vehicle_type"]["id"])
                        & Q(start_point__contains={"id": data["start_point"]["id"]})
                        & Q(
                            destination_point__contains={
                                "id": last_destination["destination_point"]["id"]
                            }
                        )
                        & Q(contract_id=contract_code)
                    )

                    for i in c_obj:
                        try:
                            exits_d = [
                                j["s_location"]["id"] for j in i.multiple_stopage
                            ]
                            if coming_d == exits_d:
                                contract_obj = Contract.objects.filter(id=i.id)
                                break
                        except:
                            continue

                if len(contract_obj):
                    indent_code = user_define_methods.creating_indent_code(
                        date, (cus_obj.customer_code)
                    )
                    repetitive = data.get("is_repetitive", False)
                    manifest_number = data.get("manifest_number")
                    indent_create_data = dict(
                        start_point={
                            "location_code": data["start_point"]["location_code"],
                            "location_name": data["start_point"]["location_name"],
                            "location_point": data["start_point"]["location_point"],
                            "location_city": data["start_point"]["location_city"],
                            "id": data["start_point"]["id"],
                        },
                        destination_point={
                            "location_code": last_destination["destination_point"][
                                "location_code"
                            ],
                            "location_name": last_destination["destination_point"][
                                "location_name"
                            ],
                            "location_point": last_destination["destination_point"][
                                "location_point"
                            ],
                            "location_city": last_destination["destination_point"][
                                "location_city"
                            ],
                            "id": last_destination["destination_point"]["id"],
                        },
                        date_vehicle_required=date,
                        time_vehicle_required=time,
                        vehicle_type_id=data["vehicle_type"]["id"],
                        number_of_vehicle=data["no_of_vehicle"],
                        customer_id=int(data["customer"]["id"]),
                        contract_id=contract_obj[0].id,
                        indent_code=indent_code,
                        is_repetitive=repetitive,
                        multi_stopage=None,
                        manifest_number=manifest_number,
                        created_at=timezone.now(),
                        created_by=request.auth.user.username,
                        is_separate_indent=False
                    )

                    if len(data["destination_point"]) > 1:
                        indent_create_data.update(
                            **{"multi_stopage": data["destination_point"][:-1]}
                        )

                    if data.get("separate_indent", False) is False:
                        indent = Indent.objects.create(**indent_create_data)
                        views.single_location_customer_index(indent=indent)

                        if data["is_Ad_hoc"]:
                            ad_hoc = AdHocIndent.objects.create(
                                contract_id=contract_obj[0].id,
                                indent_id=indent.id,
                                hoc_contract=data["ad_hoc_contract"],
                            )
                            indent.is_ad_hoc = True
                            indent.hoc_contract = ad_hoc.hoc_contract
                            indent.created_at = timezone.now()
                            indent.created_by = request.auth.user.username
                            indent.save()

                        if indent:
                            try:
                                order_data = user_define_methods.create_order_data(indent)
                            except Exception as e:
                                indent.delete()
                                return Response({"code": status_code.code_400})

                            i = 1
                            try:
                                while i <= int(indent.number_of_vehicle):
                                    current_demand_ref_id = None
                                    if demand_ref_queue:
                                        current_demand_ref_id = demand_ref_queue.pop()

                                    order_id = user_define_methods.creating_order_code(
                                        date, (cus_obj.customer_code)
                                    )
                                    order_obj = Order.objects.create(
                                        order_id=order_id,
                                        indent_id=indent.id,
                                        start_city=start_city,
                                        order_code=order_id,
                                        end_city=end_city,
                                        current_status="New",
                                        indent_type=data["placement_type"],
                                        bilty_boy=bilty_boy,
                                        account_manager=account_manager,
                                        demand_reference_id=current_demand_ref_id,
                                        created_at = timezone.now(),
                                        created_by = request.auth.user.username,
                                    )
                                    if not order_obj:
                                        indent.delete()
                                        return Response({"code": status_code.code_400})
                                    # create trip
                                    tat = tat or order_obj.indent.contract.tat
                                    create_trip(
                                        order_obj.id,
                                        order_data,
                                        bilty_boy,
                                        account_manager,
                                        tat,
                                        vehicle_req_datetime,
                                    )

                                    i += 1

                                    try:
                                        action_data = {
                                            "no_of_vehicle": data["no_of_vehicle"],
                                            "customer": data["customer"]["company_name"],
                                            "source": data["start_point"]["location_name"],
                                            "destination": (
                                                data["destination_point"][-1][
                                                    "destination_point"
                                                ]["location_name"]
                                            ),
                                            "vehicle_type": data["vehicle_type"]["name"],
                                            "edited_order": "false",
                                        }
                                        IndentOrderLogs.objects.create(
                                            action="order_created",
                                            user={
                                                "name": user_first_name,
                                                "code": user_code,
                                            },
                                            timestamp=timezone.now(),
                                            indent_id=indent.id,
                                            order_id_id=order_obj.id,
                                            action_data=action_data,
                                            created_at = timezone.now(),
                                            created_by = request.auth.user.username,
                                        )
                                    except Exception as e:
                                        pass
                            except Exception as e:
                                indent.delete()
                                return Response({"code": status_code.code_400})
                            return Response({"code": status_code.code_200})
                        else:
                            return Response({"code": status_code.code_400})
                    else:
                        for i in range(0, data["no_of_vehicle"]):
                            indent_code = user_define_methods.creating_indent_code(
                                date, (cus_obj.customer_code)
                            )
                            indent_create_data["indent_code"] = indent_code
                            indent_create_data["number_of_vehicle"] = 1
                            indent_create_data["is_separate_indent"] = True

                            indent = Indent.objects.create(**indent_create_data)
                            views.single_location_customer_index(indent=indent)

                            if data["is_Ad_hoc"]:
                                ad_hoc = AdHocIndent.objects.create(
                                    contract_id=contract_obj[0].id,
                                    indent_id=indent.id,
                                    hoc_contract=data["ad_hoc_contract"],
                                )
                                indent.is_ad_hoc = True
                                indent.hoc_contract = ad_hoc.hoc_contract
                                indent.created_at = timezone.now()
                                indent.created_by = request.auth.user.username
                                indent.save()

                            if indent:
                                try:
                                    order_data = user_define_methods.create_order_data(indent)
                                except Exception as e:
                                    indent.delete()
                                    return Response({"code": status_code.code_400})

                                try:
                                    current_demand_ref_id = None
                                    if demand_ref_queue:
                                        current_demand_ref_id = demand_ref_queue.pop()

                                    order_id = user_define_methods.creating_order_code(
                                        date, (cus_obj.customer_code)
                                    )
                                    order_obj = Order.objects.create(
                                        order_id=order_id,
                                        indent_id=indent.id,
                                        start_city=start_city,
                                        order_code=order_id,
                                        end_city=end_city,
                                        current_status="New",
                                        indent_type=data["placement_type"],
                                        bilty_boy=bilty_boy,
                                        account_manager=account_manager,
                                        demand_reference_id=current_demand_ref_id,
                                        created_at=timezone.now(),
                                        created_by=request.auth.user.username,
                                    )
                                    if not order_obj:
                                        indent.delete()
                                        return Response({"code": status_code.code_400})
                                    # create trip
                                    tat = tat or order_obj.indent.contract.tat
                                    create_trip(
                                        order_obj.id,
                                        order_data,
                                        bilty_boy,
                                        account_manager,
                                        tat,
                                        vehicle_req_datetime,
                                    )

                                    try:
                                        action_data = {
                                            "no_of_vehicle": data["no_of_vehicle"],
                                            "customer": data["customer"]["company_name"],
                                            "source": data["start_point"]["location_name"],
                                            "destination": (
                                                data["destination_point"][-1][
                                                    "destination_point"
                                                ]["location_name"]
                                            ),
                                            "vehicle_type": data["vehicle_type"]["name"],
                                            "edited_order": "false",
                                        }
                                        IndentOrderLogs.objects.create(
                                            action="order_created",
                                            user={
                                                "name": user_first_name,
                                                "code": user_code,
                                            },
                                            timestamp=timezone.now(),
                                            indent_id=indent.id,
                                            order_id_id=order_obj.id,
                                            action_data=action_data,
                                            created_at=timezone.now(),
                                            created_by=request.auth.user.username,
                                        )
                                    except Exception as e:
                                        pass
                                except Exception as e:
                                    indent.delete()
                                    return Response({"code": status_code.code_400})
                            else:
                                return Response({"code": status_code.code_400})
                    return Response({"code": status_code.code_200})
                else:
                    return Response({"code": status_code.code_204})
            else:
                return Response({"code": status_code.code_204})
        except Exception as e:
            try:
                indent.delete()
            except:
                pass
            LOGGER.error(f"Error in indent creation {str(e)}", exc_info=True)
            return Response({"code": {"status": 300, "message": str(e)}})


class IndentUpdate(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        try:
            data = request.data
            return Response(
                {
                    "code": {
                        "status": 404,
                        "message": "This Functionality has been removed.",
                    }
                }
            )
            # Ashish
            order_to_cancel = Order.objects.filter(indent_id=data["id"])
            order_data_obj = OrderSerializer(order_to_cancel, many=True).data
            for order in order_data_obj:
                if order["current_status"] != "New":
                    print("order['current_status']:", order["current_status"])
                    return Response(
                        {
                            "code": {
                                "status": 404,
                                "message": "Can't edit, few orders are Planned",
                            }
                        }
                    )

            # creating new indent
            user_obj = UserManagement.objects.filter(user_id=request.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]
            # date = data['date_vehicle_required'].split('T')[0]
            date = datetime.strptime(data["date_vehicle_required"], "%Y-%m-%d")
            # print(data['time_vehicle_required'])
            time = data["time_vehicle_required"]
            if len(time.split(":")) == 3:
                time = time.rsplit(":", 1)[0]
            else:
                pass
            time = datetime.strptime(time, "%H:%M")
            vehicle_req_datetime = datetime.combine(date, time.time())
            start_city = data["start_point"]["location_city"]["name"]
            end_city = data["destination_point"]["location_city"]["name"]
            # If Exception than no bilty boy and account Manager selected with this
            cus_obj = Customer.objects.get(id=int(data["customer"]["id"]))
            try:
                bilty_boy = user_define_methods.creating_user_dict(
                    cus_obj.bilty_boy.user.username, cus_obj.bilty_boy.user.id
                )
                # Create data for bilty boy
                account_manager = user_define_methods.creating_user_dict(
                    cus_obj.account_manager.user.username,
                    cus_obj.account_manager.user.id,
                )
            except Exception as e:
                print(
                    "Exception is :- \n~",
                    e.args[0],
                    "\noccurred at line number:",
                    sys.exc_info()[2].tb_lineno,
                )
                return Response(
                    {
                        "code": {
                            "status": 300,
                            "message": "No Bilty Boy and Account Manager",
                        }
                    }
                )
            # Check Rate Card For Particular Customer To Origin and Destination
            contract_obj = Contract.objects.filter(
                Q(customer_id=int(data["customer"]["id"]))
                & Q(vehicle_type_id=data["vehicle_type"]["id"])
                & Q(start_point__contains={"id": data["start_point"]["id"]})
                & Q(destination_point__contains={"id": data["destination_point"]["id"]})
            )
            if len(contract_obj):
                order_to_cancel.update(current_status="Edited")
                Indent.objects.filter(id=data["id"]).update(
                    is_disable=True, cancellation_type="Edited"
                )
                indent_code = user_define_methods.creating_indent_code(
                    date, (cus_obj.customer_code)
                )
                indent = Indent.objects.create(
                    start_point={
                        "location_code": data["start_point"]["location_code"],
                        "location_name": data["start_point"]["location_name"],
                        "location_point": data["start_point"]["location_point"],
                        "location_city": data["start_point"]["location_city"],
                        "id": data["start_point"]["id"],
                    },
                    destination_point={
                        "location_code": data["destination_point"]["location_code"],
                        "location_name": data["destination_point"]["location_name"],
                        "location_point": data["destination_point"]["location_point"],
                        "location_city": data["destination_point"]["location_city"],
                        "id": data["destination_point"]["id"],
                    },
                    date_vehicle_required=date,
                    time_vehicle_required=time,
                    vehicle_type_id=data["vehicle_type"]["id"],
                    number_of_vehicle=data["number_of_vehicle"],
                    customer_id=int(data["customer"]["id"]),
                    contract_id=contract_obj[0].id,
                    indent_code=indent_code,
                )
                # Create Data for account manager
                order_data = user_define_methods.create_order_data(indent)
                i = 1
                while i <= int(data["number_of_vehicle"]):
                    order_id = user_define_methods.creating_order_code(
                        date, (cus_obj.customer_code)
                    )
                    order_obj = Order.objects.create(
                        order_id=order_id,
                        indent_id=indent.id,
                        start_city=start_city,
                        end_city=end_city,
                        current_status="New",
                        order_code=order_id,
                        bilty_boy=bilty_boy,
                        account_manager=account_manager,
                    )
                    create_trip(
                        order_obj.id,
                        order_data,
                        bilty_boy,
                        account_manager,
                        order_obj.indent.contract.tat,
                        vehicle_req_datetime,
                    )
                    i += 1
                    # Ashish
                    try:
                        action_data = {
                            "no_of_vehicle": data["number_of_vehicle"],
                            "customer": data["customer"]["company_name"],
                            "source": data["start_point"]["location_name"],
                            "destination": data["destination_point"]["location_name"],
                            "vehicle_type": data["vehicle_type"]["name"],
                            "edited_order": "true",
                        }
                        IndentOrderLogs.objects.create(
                            action="order_created",
                            user={"name": user_first_name, "code": user_code},
                            timestamp=timezone.now(),
                            indent_id=indent.id,
                            order_id_id=order_obj.id,
                            action_data=action_data,
                            created_at = timezone.now(),
                        )
                    except Exception as e:
                        print(
                            "Exception is :- \n~",
                            e.args[0],
                            "\noccurred at line number:",
                            sys.exc_info()[2].tb_lineno,
                        )
                # indent_listing_reset.delay(c_name='IndentUpdate', type_user='', user_id='')
                return Response(
                    {
                        "code": status_code.code_200,
                        "indentCode": indent.indent_code,
                        "indentId": indent.id,
                    }
                )
            else:
                return Response({"code": status_code.code_204})
                # indent_obj = Indent.objects.filter(id=data['id'])
                # if indent_obj:
                #     indent_obj.update(vehicle_type_id=data['vehicle_type']['id'], start_point=data['start_point'],
                #                       destination_point=data['destination_point'],
                #                       date_vehicle_required=data['date_vehicle_required'],
                #                       time_vehicle_required=data['time_vehicle_required'])
                #     return Response({'code': status_code.code_200})
                # else:
                #     return Response({'code': status_code.code_203})
        except Exception as e:
            print(
                "Exception is :- \n~",
                e.args[0],
                "\noccurred at line number:",
                sys.exc_info()[2].tb_lineno,
            )
            return Response({"code": {"status": 300, "message": e.args[0]}})


class IndentListing(APIView):
    @method_decorator(login_required(login_url="/"))
    def get(self, request):
        indent = list()
        if not request.auth.user.is_superuser and not request.auth.user.is_staff:
            user_data = return_user_type(request.auth.user.id)
            if user_data.is_account_manager:
                customer = Customer.objects.filter(
                    account_manager_id=user_data.id
                ).values("id")
                indent = Indent.objects.filter(
                    customer_id__in=customer, is_disable=False
                ).order_by("-date_vehicle_required", "-time_vehicle_required")
                code = status_code.code_200
            else:
                code = {"status": 404, "message": "Access Denied"}
        elif request.auth.user.is_superuser or request.auth.user.is_staff:

            indent = Indent.objects.filter(is_disable=False).order_by(
                "-date_vehicle_required", "-time_vehicle_required"
            )
            code = status_code.code_200
        else:
            code = {"status": 404, "message": "Access Denied"}

        indent, paginator = paginator_file.paginator_file_f(indent, request)
        indent_data = IndentSerializer(indent, many=True).data
        indent_ids = [e["id"] for e in indent_data]
        orders = Order.objects.filter(indent__in=indent_ids).values(
            "indent_id", "demand_reference_id"
        )
        order_data = {e["indent_id"]: e["demand_reference_id"] for e in orders}
        for data in indent_data:
            data.update(demand_reference_id=order_data.get(data["id"]))

        y = iter(indent_data)
        return paginator.get_paginated_response({"indent": y, "code": code, "wf": 0})

    def post(self, request):
        pass

@login_required(login_url="/")
@api_view(["GET"])
def repetitive_indent_listing(request):
    """
    :param request: filters for customer, origin, destination etc.
    :return: paginated indent response is returned
    """
    filter_data = request.GET
    LOGGER.info(f"Request to fetch repetitive indent - {filter_data}")
    indent_obj = None
    #Filters
    customer_id = filter_data.get("customer")
    origin = filter_data.get("origin")
    destination = filter_data.get("destination")
    vehicle_type = filter_data.get("vehicle_type")
    from_date = filter_data.get("from_date")
    to_date = filter_data.get("to_date")

    try:        
        if not request.auth.user.is_superuser and not request.auth.user.is_staff:
            user_data = return_user_type(request.auth.user.id)
            if user_data.is_account_manager:
                customer = Customer.objects.filter(
                    account_manager_id=user_data.id
                ).values("id")
                indent_obj = Indent.objects.filter(
                    customer_id__in=customer, is_disable=False, is_repetitive=True, date_vehicle_required__gte=date_module.today()
                ).order_by("-date_vehicle_required", "-time_vehicle_required")
                code = status_code.code_200
            else:
                return Response({"code":{"status": 404, "message": "Access Denied"}})
        elif request.auth.user.is_superuser or request.auth.user.is_staff:
            indent_obj = Indent.objects.filter(is_disable=False, is_repetitive=True, date_vehicle_required__gte=date_module.today()).order_by(
                "-date_vehicle_required", "-time_vehicle_required"
            )
            code = status_code.code_200
        else:
            return Response({"code":{"status": 404, "message": "Access Denied"}})

        #Applying filters
        if customer_id:
            indent_obj = indent_obj.filter(
                customer_id=customer_id
            )
        if origin:
            indent_obj = indent_obj.filter(
                start_point__location_name__contains=origin
            )
        if destination:
            indent_obj = indent_obj.filter(
                destination_point__location_name__contains=destination
            )
        if vehicle_type:
            indent_obj = indent_obj.filter(
                vehicle_type_id=vehicle_type
            )
        if from_date:
            indent_obj = indent_obj.filter(
                date_vehicle_required__gte=datetime.strptime(from_date, "%Y-%m-%d"),
            )
        if to_date:
            indent_obj = indent_obj.filter(
                date_vehicle_required__lte=datetime.strptime(to_date, "%Y-%m-%d"),
            )

        #paginate data
        indent_data, paginator = paginator_file.paginator_file_f(indent_obj,request)
        LOGGER.info(f"Response: repetitive indent data - {indent_data}")
        indent = IndentSerializer(indent_data, many=True).data
        
        indent_ids = [e["id"] for e in indent]
        orders = Order.objects.filter(indent__in=indent_ids).values(
            "indent_id", "demand_reference_id"
        )
        order_data = {e["indent_id"]: e["demand_reference_id"] for e in orders}
        for data in indent:
            data.update(demand_reference_id=order_data.get(data["id"]))
        y = iter(indent)
            
        return paginator.get_paginated_response({"indent": y, "code": code, "wf": 0})

    except Exception as e:
        print(
            "Exception is :- \n~",
            e,
            "\noccurred at line number:",
            sys.exc_info()[2].tb_lineno,
        )
        return Response({"code": {"status": 301, "message": e}})

@login_required(login_url="/")
@api_view(["POST"])
def disable_repetitive_indent(request):
    """
    :param request: indent_id
    :return: success: if indent is disabled successfully
    """
    try:
        LOGGER.info(f"Request to disable: repetitive indent - {request.data}")
        indent_id = request.data['id']
        indent_obj = Indent.objects.filter(id=indent_id, is_repetitive=True)
        if not indent_obj.count():
            return Response({
                "code":
                {
                    "status": 203, 
                    "message": "No Repetitive Indent Found"
                },
                "request_data": request.data
            })
        indent_obj.update(is_repetitive=False)
        return Response({
            "code":
            {
                "status": 200, 
                "message": "Indent Has Been Disabled"
            },
            "request_data": request.data
        })
    except Exception as e:
        return Response({
            "code": {"status": 501, "message": "Error: " + str(e)}, 
            "request_data": request.data
        })

# Ashish
class CancelledIndentListing(APIView):
    @method_decorator(login_required(login_url="/"))
    def get(self, request):
        if not request.auth.user.is_superuser and not request.auth.user.is_staff:
            user_data = return_user_type(request.auth.user.id)
            if user_data.is_account_manager:
                customer = Customer.objects.filter(
                    account_manager_id=user_data.id
                ).values("id")
                indent = Indent.objects.filter(
                    Q(customer_id__in=customer)
                    & Q(is_disable=True)
                    & ~Q(cancellation_type="Edited")
                ).order_by("-id")
            else:
                indent = []
        elif request.auth.user.is_superuser or request.auth.user.is_staff:
            indent = Indent.objects.filter(
                Q(is_disable=True) & ~Q(cancellation_type="Edited")
            ).order_by("-id")
        else:
            return Response({"code": {"status": 404, "message": "Access Denied"}})
        indent, paginator = paginator_file.paginator_file_f(indent, request)
        indent = IndentSerializer(indent, many=True).data
        y = iter(indent)
        indent, user_data, customer = None, None, None
        del indent, user_data, customer
        gc.collect()
        return paginator.get_paginated_response(
            {"indent": y, "code": status_code.code_200, "wf": 0}
        )


class FavoriteDetails(View):
    @user_login_required
    def get(self, request):
        data = request.GET
        fav_data = Favorite.objects.filter(customer_id=data["parameter"])
        return list(fav_data)

    @user_login_required
    def post(self, request):
        data = request.POST
        Favorite.objects.create(
            start_point=int(data["start_location_id"]),
            destination_point=int(data["destination_location_id"]),
            vehicle_type_id=data["vehicle_type_id"],
            number_of_vehicle=data["no_of_vehicle"],
            customer_id=int(data["customer_id"]),
        )
        return True


######################################################################################
#  Function Name- get_vehicle_type_data                                              #
#  Operation - getting vehicle Type Data                                             #
#  By - Vishnu Badal                                                                  #
#  Date - 22 Aug 2016                                                                #
######################################################################################


@login_required(login_url="/")
@api_view(
    ["GET",]
)
def get_vehicle_type_data(request):
    vehicle_data = VehicleType.objects.filter(is_delete=False)
    vehicle_data = VehicleTypeSerializer(vehicle_data, many=True).data
    y = iter(vehicle_data)
    vehicle_data = None
    del vehicle_data
    gc.collect()
    return Response({"vehicle_type": y, "code": status_code.code_200})


# Ashish
@login_required(login_url="/")
@api_view(
    ["GET",]
)
def get_address_of_latlng(request):
    try:
        latlng = request.GET.get("latlng")
        address = {
            "street": "",
            "city": "",
            "pin": "",
            "state": "",
            "formatted_address": "",
        }
        try:
            lat, lng = re.split(r"[,; \*_\-\/\\]+", latlng)
            float(lat), float(lng)
        except Exception as e:
            return Response(
                {"code": {"status": 300, "message": "Please enter lat long with space"}}
            )
        location = gmaps.reverse_geocode((lat, lng))
        if not location:
            return Response(
                {"code": {"status": 300, "message": "Please enter valid lat long"}}
            )
        a_c_length = len(location[0]["address_components"])
        try:
            address["street"] = reduce(
                (lambda x, y: x + ", " + y),
                [
                    z["long_name"]
                    for z in location[0]["address_components"][: a_c_length - 4]
                ],
            )
        except:
            pass
        # address['city'] = location[0]['address_components'][-4]['long_name']
        address["pin"] = location[0]["address_components"][-1]["long_name"]
        address["state"] = location[0]["address_components"][-3]["long_name"]
        address["formatted_address"] = location[0]["formatted_address"]
        return Response(
            {"address": address, "code": status_code.code_200, "lat": lat, "lng": lng}
        )
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["GET",]
)
def indent_form_details(request):
    try:
        contract_id = request.GET.get("contract_id")
        c_ob = Contract.objects.filter(contract_id=contract_id)
        if c_ob:
            c_d = ContractSerializerIndent(c_ob, many=True).data
            return Response(
                {
                    "veh_type": c_d[0]["vehicle_type"],
                    "disable": True,
                    "waypoints": c_ob[0].multiple_stopage,
                    "tat": c_ob[0].tat,
                    "code": status_code.code_200,
                }
            )
        else:
            return Response({"code": status_code.code_203})
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


@login_required(login_url="/")
@api_view(
    ["GET",]
)
def indent_form_details_v2(request):
    try:
        contract_id = request.GET.get("contract_id")
        c_ob = Contract.objects.filter(contract_id=contract_id, is_active=True)
        if c_ob:
            waypoints = c_ob[0].multiple_stopage
            if waypoints:
                for waypoint in waypoints:
                    location_cursor = pymongo_crud.db['location_management']
                    query = {"_id": ObjectId(waypoint.get("s_location", {}).get("id"))}
                    loc_obj = location_cursor.find_one(query, {'is_active': 1})
                    waypoint["is_active"] = loc_obj.get("is_active")

            c_d = ContractSerializerIndent(c_ob, many=True).data
            return Response(
                {
                    "veh_type": c_d[0]["vehicle_type"],
                    "disable": True,
                    "waypoints": c_ob[0].multiple_stopage,
                    "tat": c_ob[0].tat,
                    "code": status_code.code_200,
                }
            )
        else:
            return Response({"code": status_code.code_203})
    except Exception as e:
        return Response({"code": {"status": 501, "message": str(e)}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["GET",]
)
def fetch_contract_ids(request):
    try:
        code = (
            request.GET.get("cust")
            + "-"
            + request.GET.get("orig")
            + "-"
            + request.GET.get("dest")
        )
        c_ids = Contract.objects.filter(contract_id__contains=code, is_active=True).values_list(
            "contract_id", flat=True
        )
        if not c_ids:
            c_ids = ["No Contract Found"]
        return Response({"c_ids": c_ids, "code": status_code.code_200})
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


@login_required(login_url="/")
@api_view(
    ["GET",]
)
def fetch_contract_ids_v2(request):
    try:
        code = (
            request.GET.get("cust")
            + "-"
            + request.GET.get("orig")
            + "-"
            + request.GET.get("dest")
        )
        records = Contract.objects.filter(contract_id__contains=code, is_active=True).values(
            "contract_id", 'multiple_stopage')
        if records:
            c_ids = []
            for record in records:
                cont_dict = dict(
                    contract_id=record.get("contract_id"),
                    waypoints=[]
                )
                if record.get('multiple_stopage'):
                    waypoints = [x.get("s_location", {}).get("location_name") for x in record.get("multiple_stopage")]
                    cont_dict["waypoints"] = waypoints
                c_ids.append(cont_dict)
        else:
            c_ids = ["No Contract Found"]
        return Response({"c_ids": c_ids, "code": status_code.code_200})
    except Exception as e:
        LOGGER.info(f"fetch_contract_ids_v2 exception - {str(e)}", exc_info=True)
        return Response({"code": {"status": 501, "message": str(e)}})


@api_view(
    ["GET",]
)
def fetch_contract_ids_v3(request):
    try:
        contract_id = request.GET.get("contract_id")
        if not contract_id:
            raise Exception("Contract id required")
        contract_obj = Contract.objects.filter(contract_id=contract_id).first()
        c_ids = []
        if contract_obj:
            code = (
                    contract_obj.customer.customer_code
                    + "-"
                    + contract_obj.start_point.get("location_code")
                    + "-"
                    + contract_obj.destination_point.get("location_code")
                    + "-"
                    + contract_obj.vehicle_type.code
            )
            LOGGER.info(f"fetch_contract_ids_v3 code - {code}")
            records = Contract.objects.filter(contract_id__contains=code, is_active=True)
            if records:
                for record in records:
                    cont_dict = dict(
                        contract_id=record.contract_id,
                        tat=record.tat,
                        vehicle_type=record.vehicle_type.name,
                        broker_rate=record.broker_rate,
                        waypoints=[]
                    )
                    if record.multiple_stopage:
                        waypoints = [x.get("s_location", {}).get("location_name") for x in record.multiple_stopage]
                        cont_dict["waypoints"] = waypoints
                    c_ids.append(cont_dict)
        return Response({"c_ids": c_ids, "code": status_code.code_200})
    except Exception as e:
        LOGGER.info(f"fetch_contract_ids_v2 exception - {str(e)}", exc_info=True)
        return Response({"code": {"status": 501, "message": str(e)}})


@api_view(["PUT"])
def change_contract(request):
    try:
        INVALID_PERMISSION = "customer_care.can_change_contract"
        if not request.auth.user.has_perm(INVALID_PERMISSION):
            raise PermissionDenied("You do not have permission to perform this action.")
        input_data = request.data
        LOGGER.info(f"change_contract input_data - {input_data}")
        order_code = input_data.get("order_code")
        contract_id = input_data.get("contract_id")
        if not order_code:
            raise Exception("Order code is mandatory")
        if not contract_id:
            raise Exception("Contract id is mandatory")
        order_obj = Order.objects.filter(order_code=order_code).first()
        if not order_obj:
            raise Exception("Order data not found")
        contract_obj = Contract.objects.filter(contract_id=contract_id).first()
        if not contract_obj:
            raise Exception("Selected contract id is not available")
        indent_obj = Indent.objects.get(id=order_obj.indent_id)
        if not indent_obj:
            raise Exception("Indent not found")
        if indent_obj:
            waypoints = contract_obj.multiple_stopage
            multiple_stopage = None
            if waypoints:
                multiple_stopage = []
                for waypoint in waypoints:
                    location_cursor = pymongo_crud.db['location_management']
                    query = {"_id": ObjectId(waypoint.get("s_location", {}).get("id"))}
                    loc_obj = location_cursor.find_one(query, {'is_active': 1})
                    temp_dict = dict(
                        is_active=loc_obj.get("is_active"),
                        destination_point=waypoint.get("s_location")
                    )
                    multiple_stopage.append(temp_dict)
            indent_obj.contract_id = contract_obj.id
            indent_obj.multi_stopage = multiple_stopage
            indent_obj.save()
            orders = Order.objects.filter(indent_id=indent_obj.id)
            if len(orders) > 1:
                raise Exception(f"Contract ID cannot be changed as multiple orders are already "
                                f"linked to the Indent - {indent_obj.indent_code}")
            _order = orders[0]
            change_contract_update_trip(_order.id, indent_obj, contract_obj)
            try:
                IndentOrderLogs.objects.create(
                    action="change_contract",
                    user={"user_name": request.auth.user.username},
                    timestamp=timezone.now(),
                    indent_id=indent_obj.id,
                    order_id_id=order_obj.id,
                    action_data=input_data,
                    created_at=timezone.now(),
                    created_by=request.auth.user.username,
                )
            except Exception as e:
                pass
            return Response(
                {"code": status_code.code_200}
            )
    except Exception as e:
        LOGGER.error(f"Error while change contract - {str(e)}", exc_info=True)
        return Response({"code": {"status": 300, "message": str(e)}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["POST",]
)
def add_voda_asset(request):
    try:
        status_master = TripStatusMaster.objects
        check_trip_status = get_status_master(status_master, "Vehicle Placed")
        trip_id = request.data.get("trip_id")
        if trip_id is not None:
            trip_obj = Trip.objects.filter(
                Qm(id=trip_id) & Qm(trip_status__id=check_trip_status["id"])
            )
            if trip_obj:
                n = request.data.get("num").strip()
                if n is not None and len(n) == 10:
                    consent_s = network_voda.VltAssetManagement(n).add_new_asset()
                    veh_no = request.data.get("veh_no")
                    if consent_s != "":
                        try:
                            if consent_s.split(":")[1] in [
                                "The provided Mobile number already exist.",
                                "srcMsisdn is already subscribed",
                            ]:
                                a_ob = Assets.objects.filter(
                                    device_no=n, is_voda=True, is_disable=False
                                )
                                if a_ob:
                                    a_ob.update(veh_num=veh_no)
                                else:
                                    Assets.objects.create(
                                        device_no=n, veh_num=veh_no, is_voda=True
                                    )
                                return Response(
                                    {
                                        "consent_s": "Added Successfully",
                                        "code": status_code.code_200,
                                    }
                                )
                            else:
                                a_ob = Assets.objects.filter(
                                    device_no=n, is_voda=True, is_disable=False
                                )
                                if a_ob:
                                    a_ob.update(veh_num=veh_no)
                                else:
                                    Assets.objects.create(
                                        device_no=n, veh_num=veh_no, is_voda=True
                                    )
                                return Response(
                                    {
                                        "consent_s": consent_s,
                                        "code": status_code.code_200,
                                    }
                                )
                        except IndexError:
                            return Response(
                                {"code": {"status": 400, "message": consent_s}}
                            )
                    else:
                        return Response({"code": status_code.code_400})
                else:
                    return Response(
                        {
                            "code": {
                                "status": 400,
                                "message": "Please enter legit number.",
                            }
                        }
                    )
            else:
                return Response(
                    {"code": {"status": 400, "message": "Please place vehicle first."}}
                )
        else:
            return Response({"code": status_code.code_400})
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["POST",]
)
def add_airtel_asset(request):
    try:
        status_master = TripStatusMaster.objects
        check_trip_status = get_status_master(status_master, "Vehicle Placed")
        trip_id = request.data.get("trip_id")
        if trip_id is not None:
            trip_obj = Trip.objects.filter(
                Qm(id=trip_id) & Qm(trip_status__id=check_trip_status["id"])
            )
            if trip_obj:
                n = request.data.get("num").strip()
                if n is not None and len(n) == 10:
                    n = "91" + n
                    consent_s = network_airtel.AirtelAssetManagement(n).add_new_asset()
                    if consent_s != "":
                        if "hlr" in consent_s.lower():
                            return Response(
                                {
                                    "code": {
                                        "status": 400,
                                        "message": "Please Enter Airtel MSISDN!",
                                    }
                                }
                            )
                        elif "SmTrailDB" in consent_s:
                            veh_no = request.data.get("veh_no")
                            a_ob = Assets.objects.filter(
                                device_no=n, is_airtel=True, is_disable=False
                            )
                            if a_ob:
                                a_ob.update(veh_num=veh_no)
                            else:
                                Assets.objects.create(
                                    device_no=n, veh_num=veh_no, is_airtel=True
                                )
                            return Response(
                                {
                                    "consent_s": "Added Successfully",
                                    "code": status_code.code_200,
                                }
                            )
                        elif "successfully" in consent_s:
                            veh_no = request.data.get("veh_no")
                            Assets.objects.create(
                                device_no=n, veh_num=veh_no, is_airtel=True
                            )
                            return Response(
                                {"consent_s": consent_s, "code": status_code.code_200}
                            )
                        else:
                            return Response(
                                {"code": {"status": 400, "message": consent_s}}
                            )
                    else:
                        return Response({"code": status_code.code_400})
                else:
                    return Response(
                        {
                            "code": {
                                "status": 400,
                                "message": "Please enter legit number.",
                            }
                        }
                    )
            else:
                return Response(
                    {"code": {"status": 400, "message": "Please place vehicle first."}}
                )
        else:
            return Response({"code": status_code.code_400})
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["POST",]
)
def check_consent_status(request):
    try:
        asset_type = request.data.get("type").strip()
        consent_s = ""
        n = request.data.get("num").strip()
        if asset_type == "Vodafone":
            pass
        elif asset_type == "Airtel":
            n = "91" + n
            consent_s = network_airtel.AirtelAssetManagement(n).check_consent()
        if consent_s == "ALLOWED":
            return Response({"consent_s": consent_s, "code": status_code.code_200})
        else:
            return Response({"code": {"status": 400, "message": consent_s}})
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.message}})


@login_required(login_url="/")
@api_view(
    ["POST",]
)
def create_bulk_indent(request):
    indent_headers = [
        "cus_code",
        "veh_type",
        "s_point_code",
        "d_point_code",
        "touch_points",
        "date",
        "time",
        "no_of_veh",
        "plac_type",
        "adhoc_contract_rate",
        "contract_id",
    ]
    if request.method == "POST":
        try:
            file_data = request.FILES["file"].read().decode("utf-8")
            reader = csv.reader(io.StringIO(file_data))
            count = 0
            user_obj = UserManagement.objects.filter(user_id=request.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]
            indent_ids = list()
            order_ids = list()
            for row in reader:
                count += 1
                if count == 1:
                    if row == indent_headers:
                        continue
                    else:
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "Headers are incorrect",
                                }
                            }
                        )
                else:
                    try:
                        contract_code = row[10]
                        start_point_obj = LocationManagement.objects.filter(
                            location_code=row[2].replace(" ", ""),
                            customer_data__customer_code=row[0].replace(" ", ""),
                        )
                        start_point = LocationGettingSerializerShorteningIndent(
                            start_point_obj, many=True
                        ).data
                        destination_point_obj = LocationManagement.objects.filter(
                            location_code=row[3].replace(" ", ""),
                            customer_data__customer_code=row[0].replace(" ", ""),
                        )
                        destination_point = LocationGettingSerializerShorteningIndent(
                            destination_point_obj, many=True
                        ).data
                        cus_obj = Customer.objects.get(customer_code=row[0])
                        v_type_obj = VehicleType.objects.get(name=row[1])
                        touch_points = row[4]
                        if touch_points != "":
                            touch_points = list(
                                map(lambda x: x.replace(" ", ""), row[4].split(","))
                            )
                        else:
                            touch_points = list()
                        date = datetime.strptime(row[5], "%Y-%m-%d")
                        time = datetime.strptime(row[6], "%H:%M:%S")
                        checked, hour = user_define_methods.check_hour_validation(
                            required_datetime=datetime.combine(date, time.time())
                        )
                        vehicle_req_datetime = datetime.combine(date, time.time())
                        if not checked:
                            Indent.objects.filter(id__in=indent_ids).delete()
                            Order.objects.filter(id__in=order_ids).delete()
                            return Response(
                                {
                                    "code": {
                                        "status": 300,
                                        "message": "Date and time cannot be changed from beyond the restricted limit "
                                        + "for record number "
                                        + str(count - 1),
                                    }
                                }
                            )
                        coming_d = list()
                        touch_points_list = list()
                        if len(touch_points):
                            for i in touch_points:
                                touch_point_obj = LocationManagement.objects.filter(
                                    location_code=i, customer_data__customer_code=row[0]
                                )
                                if len(touch_point_obj):
                                    coming_d.append(str(touch_point_obj[0]["id"]))
                                    touch_points_list.append(
                                        {
                                            "destination_point": {
                                                "location_code": touch_point_obj[0][
                                                    "location_code"
                                                ],
                                                "location_name": touch_point_obj[0][
                                                    "location_name"
                                                ],
                                                "location_point": touch_point_obj[0][
                                                    "location_point"
                                                ],
                                                "location_city": touch_point_obj[0][
                                                    "location_city"
                                                ],
                                                "id": str(touch_point_obj[0]["id"]),
                                            }
                                        }
                                    )
                                else:
                                    Indent.objects.filter(id__in=indent_ids).delete()
                                    Order.objects.filter(id__in=order_ids).delete()
                                    return Response(
                                        {
                                            "code": {
                                                "status": 300,
                                                "message": "Touch point "
                                                + i
                                                + "for record number"
                                                + str(count - 1),
                                            }
                                        }
                                    )
                        else:
                            coming_d = list()
                            touch_points_list = list()

                        f_obj = Favorite.objects.filter(
                            customer_id=cus_obj.id,
                            start_point__contains={"id": str(start_point[0]["id"])},
                            destination_point__contains={
                                "id": str(destination_point[0]["id"])
                            },
                            vehicle_type_id=v_type_obj.id,
                        )

                        start_point_dict = {
                            "location_code": start_point[0]["location_code"],
                            "location_name": start_point[0]["location_name"],
                            "location_point": start_point[0]["location_point"],
                            "location_city": start_point[0]["location_city"],
                            "id": str(start_point[0]["id"]),
                        }

                        destination_point_dict = {
                            "location_code": destination_point[0]["location_code"],
                            "location_name": destination_point[0]["location_name"],
                            "location_point": destination_point[0]["location_point"],
                            "location_city": destination_point[0]["location_city"],
                            "id": str(destination_point[0]["id"]),
                        }
                        for i in f_obj:
                            try:
                                exits_d = [
                                    j["destination_point"]["id"]
                                    for j in i.multi_stopage
                                ]
                            except:
                                continue
                            # Now compare data
                            if coming_d == exits_d:
                                i.start_point = start_point
                                i.destination_point = destination_point
                                i.save()
                                break
                                # return Response({'code': status_code.code_401})
                            else:
                                continue

                        try:
                            bilty_boy = user_define_methods.creating_user_dict(
                                cus_obj.bilty_boy.user.username,
                                cus_obj.bilty_boy.user.id,
                            )
                            account_manager = user_define_methods.creating_user_dict(
                                cus_obj.account_manager.user.username,
                                cus_obj.account_manager.user.id,
                            )
                        except:
                            Indent.objects.filter(id__in=indent_ids).delete()
                            Order.objects.filter(id__in=order_ids).delete()
                            return Response(
                                {
                                    "code": {
                                        "status": 300,
                                        "message": "No Bilty Boy and Account Manager for record no."
                                        + str(count - 1),
                                    }
                                }
                            )

                        if len(touch_points):
                            contract_obj = list()
                            c_obj = Contract.objects.filter(
                                Q(customer_id=cus_obj.id)
                                & Q(vehicle_type_id=v_type_obj.id)
                                & Q(
                                    start_point__contains={
                                        "id": str(start_point[0]["id"])
                                    }
                                )
                                & Q(
                                    destination_point__contains={
                                        "id": str(destination_point[0]["id"])
                                    }
                                )
                                & Q(contract_id=contract_code)
                            )
                            for i in c_obj:
                                try:
                                    exits_d = [
                                        j["s_location"]["id"]
                                        for j in i.multiple_stopage
                                    ]
                                except:
                                    continue
                                # Now compare data
                                if coming_d == exits_d:
                                    contract_obj = Contract.objects.filter(id=i.id)
                                    break
                                    # return Response({'code': status_code.code_401})
                                else:
                                    continue
                        else:
                            contract_obj = Contract.objects.filter(
                                Q(customer_id=cus_obj.id)
                                & Q(vehicle_type_id=v_type_obj.id)
                                & Q(
                                    start_point__contains={
                                        "id": str(start_point[0]["id"])
                                    }
                                )
                                & Q(
                                    destination_point__contains={
                                        "id": str(destination_point[0]["id"])
                                    }
                                )
                                & Q(contract_id=contract_code),
                                Q(multiple_stopage__isnull=True)
                                | Q(multiple_stopage__in=[None, []]),
                            )

                        if contract_obj:
                            indent_code = user_define_methods.creating_indent_code(
                                date, cus_obj.customer_code
                            )
                            placement_type = row[8]
                            if (
                                placement_type == "Scheduled"
                                or placement_type == "Planned Adhoc"
                                or placement_type == "Unplanned Adhoc"
                            ):
                                if placement_type == "Scheduled":
                                    is_adhoc = False
                                else:
                                    is_adhoc = True
                                    if (
                                        row[9] != ""
                                        and row[9] is not None
                                        and row[9] != 0
                                    ):
                                        ad_hoc_rate = int(row[9])
                                    else:
                                        Indent.objects.filter(
                                            id__in=indent_ids
                                        ).delete()
                                        Order.objects.filter(id__in=order_ids).delete()
                                        return Response(
                                            {
                                                "code": {
                                                    "status": 203,
                                                    "message": "Please provide adhoc rate.",
                                                }
                                            }
                                        )
                                if len(touch_points):
                                    indent = Indent.objects.create(
                                        start_point=start_point_dict,
                                        destination_point=destination_point_dict,
                                        date_vehicle_required=date,
                                        time_vehicle_required=time,
                                        vehicle_type_id=v_type_obj.id,
                                        number_of_vehicle=row[7],
                                        customer_id=cus_obj.id,
                                        contract_id=contract_obj[0].id,
                                        indent_code=indent_code,
                                        multi_stopage=touch_points_list,
                                        created_at=timezone.now(),
                                        created_by=request.auth.user.username,
                                    )
                                    indent_ids.append(indent.id)
                                else:
                                    indent = Indent.objects.create(
                                        start_point={
                                            "location_code": start_point[0][
                                                "location_code"
                                            ],
                                            "location_name": start_point[0][
                                                "location_name"
                                            ],
                                            "location_point": start_point[0][
                                                "location_point"
                                            ],
                                            "location_city": start_point[0][
                                                "location_city"
                                            ],
                                            "id": str(start_point[0]["id"]),
                                        },
                                        destination_point={
                                            "location_code": destination_point[0][
                                                "location_code"
                                            ],
                                            "location_name": destination_point[0][
                                                "location_name"
                                            ],
                                            "location_point": destination_point[0][
                                                "location_point"
                                            ],
                                            "location_city": destination_point[0][
                                                "location_city"
                                            ],
                                            "id": str(destination_point[0]["id"]),
                                        },
                                        date_vehicle_required=date,
                                        time_vehicle_required=time,
                                        vehicle_type_id=v_type_obj.id,
                                        number_of_vehicle=row[7],
                                        customer_id=cus_obj.id,
                                        contract_id=contract_obj[0].id,
                                        indent_code=indent_code,
                                        multi_stopage=None,
                                        created_at=timezone.now(),
                                        created_by=request.auth.user.username,
                                    )
                                    indent_ids.append(indent.id)
                                if indent:
                                    try:
                                        if is_adhoc:
                                            ad_hoc = AdHocIndent.objects.create(
                                                contract_id=contract_obj[0].id,
                                                indent_id=indent.id,
                                                hoc_contract=ad_hoc_rate,
                                            )
                                            indent.is_ad_hoc = True
                                            indent.hoc_contract = ad_hoc.hoc_contract
                                            indent.created_at=timezone.now(),
                                            indent.created_by=request.auth.user.username,
                                            indent.save()
                                        order_data = user_define_methods.create_order_data(
                                            indent
                                        )
                                    except Exception as e:
                                        indent.delete()
                                        Indent.objects.filter(
                                            id__in=indent_ids
                                        ).delete()
                                        Order.objects.filter(id__in=order_ids).delete()
                                        return Response({"code": status_code.code_400})
                                    i = 1
                                    try:
                                        while i <= int(indent.number_of_vehicle):
                                            order_id = user_define_methods.creating_order_code(
                                                date, cus_obj.customer_code
                                            )
                                            order_obj = Order.objects.create(
                                                order_id=order_id,
                                                indent_id=indent.id,
                                                start_city=start_point[0][
                                                    "location_name"
                                                ],
                                                end_city=destination_point[0][
                                                    "location_name"
                                                ],
                                                current_status="New",
                                                order_code=order_id,
                                                bilty_boy=bilty_boy,
                                                account_manager=account_manager,
                                                indent_type=placement_type,
                                            )
                                            order_ids.append(order_obj.id)
                                            if not order_obj:
                                                indent.delete()
                                                return Response(
                                                    {"code": status_code.code_400}
                                                )
                                            create_trip(
                                                order_obj.id,
                                                order_data,
                                                bilty_boy,
                                                account_manager,
                                                order_obj.indent.contract.tat,
                                                vehicle_req_datetime,
                                            )
                                            i += 1
                                            try:
                                                action_data = {
                                                    "no_of_vehicle": row[7],
                                                    "customer": cus_obj.company_name,
                                                    "source": start_point[0][
                                                        "location_name"
                                                    ],
                                                    "destination": destination_point[0][
                                                        "location_name"
                                                    ],
                                                    "vehicle_type": row[1],
                                                    "edited_order": "false",
                                                }
                                                IndentOrderLogs.objects.create(
                                                    action="order_created",
                                                    user={
                                                        "name": user_first_name,
                                                        "code": user_code,
                                                    },
                                                    timestamp=timezone.now(),
                                                    indent_id=indent.id,
                                                    order_id_id=order_obj.id,
                                                    action_data=action_data,
                                                    created_at = timezone.now(),
                                                    created_by = request.auth.user.username,
                                                )
                                            except Exception as e:
                                                pass
                                    except Exception as e:
                                        indent.delete()
                                        Indent.objects.filter(
                                            id__in=indent_ids
                                        ).delete()
                                        Order.objects.filter(id__in=order_ids).delete()
                                        return Response({"code": status_code.code_400})
                            else:
                                Indent.objects.filter(id__in=indent_ids).delete()
                                Order.objects.filter(id__in=order_ids).delete()
                                return Response(
                                    {
                                        "code": {
                                            "status": 203,
                                            "message": "Select valid placement type !",
                                        }
                                    }
                                )

                        else:
                            Indent.objects.filter(id__in=indent_ids).delete()
                            Order.objects.filter(id__in=order_ids).delete()
                            return Response(
                                {
                                    "code": {
                                        "status": 300,
                                        "message": "Rate Card is not found for record no."
                                        + str(count - 1),
                                    }
                                }
                            )
                    except Exception as e:
                        Indent.objects.filter(id__in=indent_ids).delete()
                        Order.objects.filter(id__in=order_ids).delete()
                        print(
                            "Exception is :- \n~",
                            e.args[0],
                            "\noccurred at line number:",
                            sys.exc_info()[2].tb_lineno,
                        )
                        LOGGER.error(
                            f"Bulk IndentCreate exception - {e}", exc_info=True
                        )
                        return Response({"code": status_code.code_400})
        except Exception as e:
            LOGGER.error(f"Bulk Indent exception - {e}", exc_info=True)
            return Response({"code": status_code.code_400})

        return Response({"code": status_code.code_200})


# Ashish
@login_required(login_url="/")
@api_view(
    ["POST",]
)
def contract_edit_check(request):
    try:
        contract_data = request.data
        customer_id = contract_data["customer"]["id"]
        location_obj = LocationManagement.objects.filter(
            customer_id=customer_id, c_location=True
        )
        locations = LocationGettingSerializerShorteningIndent(
            location_obj, many=True
        ).data
        if Indent.objects.filter(contract_id=contract_data["id"]):
            return Response(
                {
                    "status": False,
                    "code": status_code.code_200,
                    "locations": locations,
                    "vehicle_type": contract_data["vehicle_type"]["name"],
                    "start_location": contract_data["start_point"]["location_name"],
                    "destination_location": contract_data["destination_point"][
                        "location_name"
                    ],
                    "cus_deten3": contract_data["cus_deten3"],
                    "cus_deten10": contract_data["cus_deten10"],
                    "cus_detenN": contract_data["cus_detenN"],
                    "broker_rate": contract_data["broker_rate"],
                    "is_hazardous": contract_data["is_hazardous"],
                    "cus_deten_first": contract_data["cus_deten_first"],
                    "cus_deten_second": contract_data["cus_deten_second"],
                    "cus_deten_third": contract_data["cus_deten_third"],
                    "multiple_stopage": contract_data["multiple_stopage"],
                    "lane_distance": contract_data["lane_distance"]
                }
            )
        else:
            return Response(
                {"status": True, "code": status_code.code_200, "locations": locations}
            )
    except Exception as e:
        return Response({"code": {"status": 501, "message": e.args[0]}})


# Ashish
# @login_required(login_url='/')
@api_view(
    ["GET",]
)
def get_pending_approval(request):
    try:
        final_data = list()
        trip = pymongo_crud.db["trip"]
        query = {
            "$and": [
                {"is_disable": False, "is_market": False, "h_values": {"$in": [1]}}
            ]
        }
        trip = trip.find(query)
        trip = tuple(trip)
        order_obj = Order.objects.filter(id__in=map(lambda y: y["order_id"], trip))
        # create iterator
        x = iter(trip)
        while 1:
            try:
                z = next(x)
                approval_data = list()
                try:
                    if z["last_hub"]["hub_list"]:
                        for i in z["last_hub"]["hub_list"]:
                            try:
                                if i["advance_data"]["admin_approval"] == 0:
                                    if not approval_data:
                                        approval_data.append(
                                            own_h_data_admin(
                                                order_obj.get(id=z["order_id"]), z
                                            )
                                        )
                                    f_data = {}
                                    f_data = approval_data[0]
                                    f_data["hub_data"] = i
                                    # print'---> last', i['hub']['hub_name']
                                    final_data.append(f_data.copy())
                                    try:
                                        del f_data
                                    except:
                                        pass

                            except:
                                continue
                except:
                    pass
                try:
                    if z["next_hub"]["hub_list"]:
                        if (
                            z.next_hub["hub_list"][0]["advance_data"]["admin_approval"]
                            == 0
                        ):
                            if not approval_data:
                                approval_data.append(
                                    own_h_data_admin(order_obj.get(id=z["order_id"]), z)
                                )
                            f_data = {}
                            f_data = approval_data[0]
                            f_data["hub_data"] = z["next_hub"]["hub_list"][0]
                            # print '--->First', z.next_hub['hub_list'][0]['hub']['hub_name']
                            final_data.append(f_data.copy())
                            try:
                                del f_data
                            except:
                                pass

                except:
                    pass

            except StopIteration:
                break
        y = iter(final_data)
        final_data, approval_data, f_data, trip_obj_queryset, order_obj, x = (
            None,
            None,
            None,
            None,
            None,
            None,
        )
        del final_data, approval_data, f_data, trip_obj_queryset, order_obj, x
        gc.collect()
        return Response({"code": status_code.code_200, "admin_approval_data": y})
    except Exception as e:
        print(
            "Exception is :- \n~",
            e,
            "\noccurred at line number:",
            sys.exc_info()[2].tb_lineno,
        )
        return Response({"code": {"status": 501, "message": e.args[0]}})


# Ashish
@login_required(login_url="/")
@api_view(
    ["POST",]
)
def payment_approved(request):
    try:
        data = request.data
        updated = False
        hub_id = data.get("hub_id")
        trip_id = data.get("trip_id")
        request_id = data.get("request_id")
        trip_obj = Trip.objects.get(id=trip_id)
        try:
            hub_next_data = trip_obj.next_hub["hub_list"]
        except:
            hub_next_data = None
        try:
            hub_last_data = trip_obj.last_hub["hub_list"]
        except:
            hub_last_data = None
        if hub_next_data is not None:
            for index, value in enumerate(hub_next_data):
                # if _.where([value], {"request_id": request_id}):
                if value.get("request_id") == request_id:
                    updated = True
                    up_data = value
                    up_data["advance_data"]["admin_approval"] = 1
                    hub_next_data[index] = up_data
                    break
                continue
            if updated:
                trip_obj.next_hub["hub_list"] = hub_next_data
                trip_obj.save()
        if hub_last_data is not None and updated is False:
            for index, value in enumerate(hub_last_data):
                # if _.where([value], {"request_id": request_id}):
                if value.get("request_id") == request_id:
                    updated = True
                    up_data = value
                    up_data["advance_data"]["admin_approval"] = 1
                    hub_last_data[index] = up_data
                    break
                continue
            if updated:
                trip_obj.last_hub["hub_list"] = hub_last_data
                trip_obj.save()
        return Response({"code": status_code.code_200})
    except Exception as e:
        print(
            "Exception is :- \n~",
            e.args[0],
            "\noccurred at line number:",
            sys.exc_info()[2].tb_lineno,
        )
        return Response({"code": {"status": 501, "message": e.args[0]}})


# Ashish
class EntityPaymentOtpApproved(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        _data = request.data
        salary_auth_data = _data["salary_auth_data"]
        approval_payment_object = _data["approval_payment_object"]
        otp = salary_auth_data["otp"]
        rawpassword = salary_auth_data["pwd"]
        entity_ids = approval_payment_object["entity_ids"]
        amounts = approval_payment_object["amount"]
        vehicle_nos = approval_payment_object["vehicle_no"]
        hashed = UserManagement.objects.get(
            user_id=request.auth.user.id
        ).transaction_password
        if pbkdf2_sha256.verify(rawpassword.strip(), hashed):
            pass
        else:
            return Response({"code": status_code.code_404})

        bool_var, sus_otp, sus_dict = otp_generation.matched_otp_amount(
            otp=otp, txn=request.data["txn"], amount=float(sum(amounts))
        )
        if sus_otp:
            message = "Suspicious OTP send by user"
            send_sms.send_sms(
                sus_dict["mob_nums"],
                message
                + "\nReq By: %s \nAmount: %s"
                % (sus_dict["otp_by"], sus_dict["amount"]),
            )
            return Response({"code": {"status": 300, "message": message}})
        if bool_var:
            pass
        else:
            message = "OTP is incorrect."
            return Response({"code": {"status": 300, "message": message}})
        try:
            for i in entity_ids:
                entity_pay_obj = EntityPayments.objects.filter(id=i)
                if len(EntityPayments.objects.filter(entity_id=i, admin_approval=1)):
                    pass
                if (
                    entity_pay_obj[0].entity.entity_type == "Reimbursement"
                    or entity_pay_obj[0].entity.entity_type == "Advance Paid"
                ):
                    account = {
                        "account_no": entity_pay_obj[0].employee_acc_id.bank_acc_no,
                        "ifsc": entity_pay_obj[0].employee_acc_id.ifsc_code,
                    }

                else:
                    entity_obj = Entity.objects.get(id=entity_pay_obj[0].entity_id)
                    account = {
                        "account_no": entity_obj.bank_acc_no,
                        "ifsc": entity_obj.ifsc_code,
                    }
                operations_p.PaymentMultipleOperations(
                    entity_pay_id=i,
                    entity=True,
                    r_time=timezone.now(),
                    txn=PaymentMultiple.generate_rand(),
                ).create_txn()
                entity_pay_obj.update(admin_approval=1, entity_account=account, 
                                        updated_at=timezone.now(), updated_by = request.auth.user.username )

            # Logs
            user_obj = UserManagement.objects.filter(user_id=request.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]
            try:
                for entity_id, amount, vehicle_no in zip(
                    entity_ids, amounts, vehicle_nos
                ):
                    action_data = {
                        "entity_id": entity_id,
                        "amount": amount,
                        "vehicle_no": vehicle_no,
                    }
                    IndentOrderLogs.objects.create(
                        action="entity_payment_approval",
                        user={"name": user_first_name, "code": user_code},
                        action_data=action_data,
                        timestamp=timezone.now(),
                        created_at = timezone.now(),
                        created_by = request.auth.user.username,
                    )
                    # action_data = {}
            except Exception as e:
                print(
                    "Exception is :- \n~",
                    e.args[0],
                    "\noccurred at line number:",
                    sys.exc_info()[2].tb_lineno,
                )
            return Response(
                {
                    "code": {
                        "status": 200,
                        "message": "Pending Payment has been successfully approved",
                    }
                }
            )
        except Exception as e:
            print(
                "Exception is :- \n~",
                e.args[0],
                "\noccurred at line number:",
                sys.exc_info()[2].tb_lineno,
            )
            return Response({"code": status_code.code_400})


# Ashish
class PaymentOtpApproved(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        _data = request.data
        request_ids = _data["approval_payment_object"]["request_ids"]
        trip_ids = _data["approval_payment_object"]["trip_ids"]
        salary_auth_data = _data["salary_auth_data"]
        otp = salary_auth_data["otp"]
        rawpassword = salary_auth_data["pwd"]
        hashed = UserManagement.objects.get(
            user_id=request.auth.user.id
        ).transaction_password
        try:
            if hashpw(rawpassword.encode("utf-8"), hashed.encode("utf-8")) == hashed:
                print("It matches")
            else:
                return Response({"code": status_code.code_404})
        except Exception as e:
            print(e)
            return Response({"code": status_code.code_509})
        if otp_generation.matched_otp(otp=otp, txn=request.data["txn"]):
            pass
        else:
            message = "OTP is incorrect."
            return Response({"code": {"status": 300, "message": message}})
        try:

            # Ashish
            user_obj = UserManagement.objects.filter(user_id=request.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]

            for trip_id, request_id in zip(trip_ids, request_ids):
                updated = False
                up_data = {}
                trip_obj = Trip.objects.get(id=trip_id)
                try:
                    hub_next_data = trip_obj.next_hub["hub_list"]
                except:
                    hub_next_data = None
                try:
                    hub_last_data = trip_obj.last_hub["hub_list"]
                except:
                    hub_last_data = None
                if hub_next_data is not None:
                    for index, value in enumerate(hub_next_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_data"]["admin_approval"] = 1
                            hub_next_data[index] = up_data
                            break
                        continue
                    if updated:
                        trip_obj.next_hub["hub_list"] = hub_next_data
                        trip_obj.save()
                if hub_last_data is not None and updated is False:
                    for index, value in enumerate(hub_last_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_data"]["admin_approval"] = 1
                            hub_last_data[index] = up_data
                            break
                        continue
                    if updated:
                        trip_obj.last_hub["hub_list"] = hub_last_data
                        trip_obj.save()

                # Ashish
                try:
                    action_data = {"advance_data": up_data}
                    IndentOrderLogs.objects.create(
                        action="own_advance_approval",
                        user={"name": user_first_name, "code": user_code},
                        action_data=action_data,
                        timestamp=timezone.now(),
                        order_id_id=trip_obj.order_id,
                        indent_id=trip_obj.order_data.indent_id,
                        created_at = timezone.now(),
                        created_by = request.auth.user.username,
                    )
                except Exception as e:
                    print(
                        "Exception is :- \n~",
                        e.args[0],
                        "\noccurred at line number:",
                        sys.exc_info()[2].tb_lineno,
                    )

            return Response(
                {
                    "code": {
                        "status": 200,
                        "message": "Pending Payment has been successfully approved",
                    }
                }
            )
        except Exception as e:
            return Response({"code": status_code.code_400})


######################################################################################
#  Class Name- PaymentUpdateAndApprove                                               #
#  Operation - edit the advance and approve                                          #
# By - Prafull                                                                       #
######################################################################################
class PaymentUpdateAndApprove(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        try:
            _data = request.data
            # print _data
            request_id = _data["request_id"]
            trip_id = _data["trip_id"]
            otp = _data["salary_auth_data"]["otp"]
            rawpassword = _data["salary_auth_data"]["pwd"]
            advance_data = _data["advance_data"]
            advance_payments = _data["advance_payments"]
            hub_id = None
            try:  # Always get one account no and one card respected to one employee
                if advance_payments["other_advance"]["payment_type"] == "account":
                    emp_obj = EmployeeAccount.objects.get(
                        employee_id=advance_payments["other_advance"]["id"]
                    )
                    advance_payments["other_advance"]["accounts_d"] = {
                        "account": emp_obj.bank_acc_no,
                        "ifsc_code": emp_obj.ifsc_code,
                    }

                elif advance_payments["other_advance"]["payment_type"] == "card":
                    # emp_obj = EmployeeAccount.objects.get(
                    #     employee_id=advance_payments['other_advance']['id'])
                    # advance_payments['other_advance']['card_d'] = {'expense_card_acc_no': emp_obj.expense_card_acc_no,
                    #                                                'expense_card_no': emp_obj.expense_card_no}
                    try:
                        emp_obj = ExpanceEmployee.objects.filter(
                            employee_id=advance_payments["other_advance"]["id"],
                            dattached_flag=False,
                        )[0]
                        advance_payments["other_advance"]["card_d"] = {
                            "expense_card_acc_no": emp_obj.expense_card.acc_no,
                            "expense_card_no": emp_obj.expense_card.card_no,
                        }
                    except:
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "Expense card details not found. Please update expense card details first.",
                                }
                            }
                        )

                elif advance_payments["other_advance"]["payment_type"] == "entity":
                    entity_obj = Entity.objects.get(
                        id=advance_payments["other_advance"]["id"]
                    )
                    advance_payments["other_advance"]["entity_d"] = {
                        "account": entity_obj.bank_acc_no,
                        "ifsc_code": entity_obj.ifsc_code,
                    }

            except:
                pass
            try:
                if advance_payments["fuel_advance"]["payment_type"] == "account":
                    emp_obj = EmployeeAccount.objects.get(
                        employee_id=advance_payments["fuel_advance"]["id"]
                    )
                    advance_payments["fuel_advance"]["accounts_d"] = {
                        "account": emp_obj.bank_acc_no,
                        "ifsc_code": emp_obj.ifsc_code,
                    }

                elif advance_payments["fuel_advance"]["payment_type"] == "card":
                    # emp_obj = EmployeeAccount.objects.get(
                    #     employee_id=advance_payments['fuel_advance']['id'])
                    # advance_payments['fuel_advance']['card_d'] = {'expense_card_acc_no': emp_obj.expense_card_acc_no,
                    #                                               'expense_card_no': emp_obj.expense_card_no}
                    try:
                        emp_obj = ExpanceEmployee.objects.filter(
                            employee_id=advance_payments["fuel_advance"]["id"],
                            dattached_flag=False,
                        )[0]
                        advance_payments["fuel_advance"]["card_d"] = {
                            "expense_card_acc_no": emp_obj.expense_card.acc_no,
                            "expense_card_no": emp_obj.expense_card.card_no,
                        }
                    except:
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "Expense card details not found. Please update expense card details first.",
                                }
                            }
                        )

                elif advance_payments["fuel_advance"]["payment_type"] == "entity":
                    entity_obj = Entity.objects.get(
                        id=advance_payments["fuel_advance"]["id"]
                    )
                    advance_payments["fuel_advance"]["entity_d"] = {
                        "account": entity_obj.bank_acc_no,
                        "ifsc_code": entity_obj.ifsc_code,
                    }

            except:
                pass

            hashed = UserManagement.objects.get(
                user_id=request.auth.user.id
            ).transaction_password
            try:
                if pbkdf2_sha256.verify(rawpassword.strip(), hashed):
                    print("It matches")
                else:
                    return Response({"code": status_code.code_404})
            except Exception as e:
                print(e)
                return Response({"code": status_code.code_509})
            if otp_generation.matched_otp(otp=otp, txn=request.data["txn"]):
                pass
            else:
                message = "OTP is incorrect."
                return Response({"code": {"status": 300, "message": message}})
            if Trip.objects.filter(
                Qm(id=trip_id)
                & (
                    Qm(
                        trip_status__id=global_objects.global_trip_status_master(
                            pi="pi"
                        )["id"]
                    )
                    | Qm(
                        trip_status__id=global_objects.global_trip_status_master(
                            s_p_t="s_p_t"
                        )["id"]
                    )
                    | Qm(
                        trip_status__id=global_objects.global_trip_status_master(
                            n_t="n_t"
                        )["id"]
                    )
                    | Qm(
                        trip_status__id=global_objects.global_trip_status_master(
                            p_t="p_t"
                        )["id"]
                    )
                )
            ):
                return Response(
                    {
                        "code": {
                            "status": 300,
                            "message": "Vehicle is not Place. Please Place vehicle First.",
                        }
                    }
                )
            try:
                updated = False
                trip_obj = Trip.objects.get(id=trip_id)
                try:
                    hub_next_data = trip_obj.next_hub["hub_list"]
                except:
                    hub_next_data = None
                try:
                    hub_last_data = trip_obj.last_hub["hub_list"]
                except:
                    hub_last_data = None
                # Ashish
                if hub_next_data is not None:
                    for index, value in enumerate(hub_next_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_payment"] = advance_payments
                            up_data["advance_data"] = advance_data
                            up_data["advance_data"]["admin_approval"] = 1
                            hub_next_data[index] = up_data
                            hub_id = value["hub_id"]
                            break
                        continue
                    if updated:
                        trip_obj.reload()
                        trip_obj.next_hub = {"hub_list": hub_next_data}
                        trip_obj.save()
                        try:
                            del hub_next_data
                        except:
                            pass
                if hub_last_data is not None and updated is False:
                    for index, value in enumerate(hub_last_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_payment"] = advance_payments
                            up_data["advance_data"] = advance_data
                            up_data["advance_data"]["admin_approval"] = 1
                            hub_last_data[index] = up_data
                            hub_id = value["hub_id"]
                            break
                        continue
                    if updated:
                        trip_obj.reload()
                        trip_obj.last_hub = {"hub_list": hub_last_data}
                        trip_obj.save()
                        try:
                            del hub_last_data
                        except:
                            pass
                try:
                    action_data = {"advance_data": advance_data}
                    user_obj = UserManagement.objects.filter(user_id=request.user.id)
                    user_ser = UserManagementSerializer(user_obj, many=True).data
                    user_code = user_ser[0]["user_code"]
                    user_first_name = user_ser[0]["user_manage_data"]["first_name"]
                    IndentOrderLogs.objects.create(
                        action="own_advance_approval",
                        user={"name": user_first_name, "code": user_code},
                        action_data=action_data,
                        timestamp=timezone.now(),
                        order_id_id=trip_obj.order_id,
                        indent_id=trip_obj.order_data["indent_id"],
                        created_at = timezone.now(),
                        created_by = request.auth.user.username,
                    )
                except Exception as e:
                    print(
                        "Exception is :- \n~",
                        e.args[0],
                        "\noccurred at line number:",
                        sys.exc_info()[2].tb_lineno,
                    )
                operations_p.PaymentMultipleOperations(
                    order_id=trip_obj.order_id,
                    own_1=True,
                    r_time=timezone.now(),
                    txn=PaymentMultiple.generate_rand(),
                    hub_id=hub_id,
                ).create_txn()
                operations_p.PaymentMultipleOperations(
                    order_id=trip_obj.order_id,
                    own_2=True,
                    r_time=timezone.now(),
                    txn=PaymentMultiple.generate_rand(),
                    hub_id=hub_id,
                ).create_txn()
                return Response(
                    {
                        "code": {
                            "status": 200,
                            "message": "Pending Payment has been successfully approved",
                        }
                    }
                )
            except Exception as e:
                print(
                    "Exception is :- \n~",
                    e.args[0],
                    "\noccurred at line number:",
                    sys.exc_info()[2].tb_lineno,
                )
                return Response({"code": status_code.code_400})
        except Exception as e:
            print(
                "Exception is :- \n~",
                e.args[0],
                "\noccurred at line number:",
                sys.exc_info()[2].tb_lineno,
            )
            return Response({"code": {"status": 300, "message": e.args[0]}})


#####################################################################################
#  Class Name- PaymentApproveDecline                                               #
#  Operation - edit the advance and approve                                          #
# By - Prafull                                                                       #
######################################################################################
class PaymentApproveDecline(APIView):
    @method_decorator(login_required(login_url="/"))
    def put(self, request):
        try:
            _data = request.data
            # print(_data)
            request_id = _data["seq"]["request_id"]
            trip_id = _data["id"]
            try:
                updated = False
                trip_obj = Trip.objects.get(id=trip_id)
                try:
                    hub_next_data = trip_obj.next_hub["hub_list"]
                except:
                    hub_next_data = None
                try:
                    hub_last_data = trip_obj.last_hub["hub_list"]
                except:
                    hub_last_data = None
                if hub_next_data is not None:
                    for index, value in enumerate(hub_next_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_data"]["admin_approval"] = 0
                            hub_next_data[index] = up_data
                            break
                        continue
                    if updated:
                        trip_obj.next_hub["hub_list"] = hub_next_data
                        trip_obj.save()
                if hub_last_data is not None and updated is False:
                    for index, value in enumerate(hub_last_data):
                        # if _.where([value], {"request_id": request_id}):
                        if value.get("request_id") == request_id:
                            updated = True
                            up_data = value
                            up_data["advance_data"]["admin_approval"] = 0
                            hub_last_data[index] = up_data
                            break
                        continue
                    if updated:
                        trip_obj.last_hub["hub_list"] = hub_last_data
                        trip_obj.save()
                return Response(
                    {
                        "code": {
                            "status": 200,
                            "message": "Pending Payment has been successfully declined",
                        }
                    }
                )
            except Exception as e:
                return Response({"code": status_code.code_400})
        except Exception as e:
            return Response({"code": status_code.code_300})


######################################################################################
#  Function Name- get_order_data                                                     #
#  Operation - getting vehicle Type Data                                             #
# By - Vishnu Badal                                                                  #
#  Date - 22 Aug 2016                                                                #
######################################################################################


# @login_required(login_url='/')
@api_view(
    ["GET",]
)
def get_order_data(request):
    try:
        # order_data = Order.objects.all().order_by("-created_date_time")
        data_id = request.GET.get("id")
        data_type = request.GET.get("type")
        data_assign = request.GET.get("assign")

        # Ashish
        order_code = request.GET.get("order_code")
        customer_id = request.GET.get("customer_id")
        source = request.GET.get("source")
        destination = request.GET.get("destination")
        vehicle_type = request.GET.get("vehicle_type")
        date_vehicle_required = request.GET.get("date_vehicle_required")
        time_vehicle_required = request.GET.get("time_vehicle_required")
        if data_assign is None:
            if data_type == "unassign":
                Order.objects.filter(id=data_id).update(
                    order_assigned=False, float_to_market=False,
                    updated_at=timezone.now(),
                    updated_by= request.auth.user.username
                )
            if data_type == "float":
                if len(Order.objects.filter(id=data_id, float_to_market=True)):
                    return Response(
                        {
                            "code": {
                                "status": 400,
                                "message": "Vehicle is already floated to market",
                            }
                        }
                    )
                if len(
                    Order.objects.filter(
                        id=data_id, order_assigned=True, float_to_market=False
                    )
                ):
                    return Response(
                        {"code": {"status": 300, "message": "Trip already assigned. "}}
                    )
                Order.objects.filter(id=data_id).update(float_to_market=True,
                    updated_at=timezone.now(),
                    updated_by= request.auth.user.username)
                try:
                    customer_obj = Customer.objects.get(id=customer_id)
                    customer_sourcing_id = customer_obj.sourcing_agent_id
                    customer_sourcing_no = UserManagement.objects.get(
                        id=customer_sourcing_id
                    ).mobile_no
                    msg = (
                        "New order, -%s- assigned to you.\nPlacement Date: %s @%s\nSource: %s\nDestination: %s\n"
                        "Vehicle Type: %s\nGOBOLT."
                        % (
                            order_code,
                            date_vehicle_required,
                            time_vehicle_required,
                            source,
                            destination,
                            vehicle_type,
                        )
                    )
                    # send_sms.send_sms(customer_sourcing_no, msg)
                except:
                    pass
                return Response({"code": status_code.code_200})
        else:
            return Response({"code": status_code.code_200})
    except Exception as e:
        # order_data = []
        pass
    try:
        # Ashish
        unassign_data_obj = Order.objects.filter(
            Q(order_assigned=False)
            & Q(float_to_market=False)
            & ~Q(current_status="Cancelled")
            & ~Q(current_status="Edited")
        )
        unassign_data_obj, paginator = paginator_file.paginator_file_f(
            unassign_data_obj, request
        )
        try:
            unassign_data_obj = OrderSerializerUnassign(
                unassign_data_obj, many=True
            ).data
            y = iter(unassign_data_obj)
            unassign_data_obj = None
            del unassign_data_obj
        except Exception as e:
            print(e)
            return paginator.get_paginated_response(
                {"unassign_data": unassign_data_obj, "code": status_code.code_200}
            )
        # assign_data_obj = OrderAssignSerializer(assign_data_obj, many=True).data
        return paginator.get_paginated_response(
            {"unassign_data": y, "code": status_code.code_200, "wf": 0}
        )
        # return Response(
        #     {'unassign_data': unassign_data_obj,
        #      'code': status_code.code_200})
    except Exception as e:
        return Response({"code": status_code.code_400})


###############################################################################################################
# Name - float_to_unassign
#
################################################################################################################
@api_view(
    ["GET",]
)
def float_to_unassign(request):
    data_id = request.GET.get("id")
    data_type = request.GET.get("type")
    if Order.objects.get(id=data_id).current_status == "Cancelled":
        return Response({"code": {"status": 400, "message": "Order Cancelled"}})
    if data_type == "unassign":
        if len(
            Order.objects.filter(
                id=data_id, order_assigned=False, float_to_market=False
            )
        ):
            return Response(
                {"code": {"status": 400, "message": "Vehicle is already unassign."}}
            )
        Order.objects.filter(id=data_id).update(float_to_market=False,updated_at = timezone.now(), 
                                                updated_by = request.auth.user.username)
        return Response({"code": status_code.code_200})


########################################################################################################################
# Name get_order_for_planning_own_assign
# Date 26 October
########################################################################################################################
@login_required(login_url="/")
@api_view(
    ["GET",]
)
def get_order_for_planning_own_assign(request):
    # Ashish
    assign_data_obj = Order.objects.filter(
        Q(order_assigned=True)
        & Q(float_to_market=False)
        & Q(trip_assign=False)
        & Q(trip_started=False)
        & ~Q(current_status="Cancelled")
    ).order_by("-id")
    assign_data_obj, paginator = paginator_file.paginator_file_f(
        assign_data_obj, request
    )
    assign_data_obj = OrderAssignSerializer(assign_data_obj, many=True).data
    y = iter(assign_data_obj)
    assign_data_obj = None
    del assign_data_obj
    return paginator.get_paginated_response(
        {"assign_data": y, "code": status_code.code_200, "wf": 0}
    )


########################################################################################################################
# Name get_order_for_planning_float_to_market
# Date 26 October
########################################################################################################################
@login_required(login_url="/")
@api_view(
    ["GET",]
)
def get_order_for_planning_float_to_market(request):
    # Ashish
    float_data_obj = Order.objects.filter(
        Q(float_to_market=True)
        & Q(trip_assigned_source=False)
        & ~Q(current_status__in=["Cancelled", "Distributed"])
    ).order_by("-id").prefetch_related(
            Prefetch('rejected_orders', queryset=GoboltBusinessRejectedOrders.objects.order_by('-id').all())
        )
    float_data_obj, paginator = paginator_file.paginator_file_f(float_data_obj, request)
    float_data_obj = MarketOrderSerializer(float_data_obj, many=True).data
    LOGGER.info(f"float_data_obj ------- {float_data_obj[0]}")
    y = iter(float_data_obj)
    float_data_obj = None
    del float_data_obj
    return paginator.get_paginated_response(
        {"float_data": y, "code": status_code.code_200, "wf": 0}
    )


########################################################################################################################
# Name get_order_for_hub
# Date 26 October
########################################################################################################################
@api_view(
    ["GET",]
)
def get_order_for_hub(request):
    try:
        user_obj = request.auth.user.usermanagement_set.all()
        # Here i assume that only one Hub to assign particular hub manager
        hub_obj = HubManagement.objects.filter(hub_manager_id=user_obj[0].id)
        assign_data_obj = []
        if hub_obj is not None:
            order_obj = Order.objects.filter(hubs__contains={"id": hub_obj[0].id})
            assign_data_obj = order_obj.filter(
                Q(order_assigned=True) & Q(float_to_market=False) & Q(trip_assign=False)
            )
        assign_data_obj = OrderAssignSerializer(assign_data_obj, many=True).data
        return Response({"code": status_code.code_200, "assign_data": assign_data_obj})
    except:
        pass
    return Response({"code": status_code.code_404})


@login_required(login_url="/")
@api_view(
    ["GET",]
)
def get_favorite_indents(request):
    try:
        # customer_id = request.GET.get('customer_id')
        favorite_indents = Favorite.objects.filter(
            customer_id=request.GET.get("customer_id")
        )
        favorite_indents = FavoriteIndentSerializer(favorite_indents, many=True).data
        return Response(
            {"favorite_indents": favorite_indents, "code": status_code.code_200}
        )
    except Exception as e:
        print(e)
        # order_data = OrderSerializer(order_data, many=True).data
        return Response({"favorite_indents": [], "code": status_code.code_400})


class AssignOrder(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        start_time = timeit.default_timer()
        try:
            assign_data = request.data
            if (
                validictory.validate(
                    assign_data, validation_model.schema_assign_vehicle_driver
                )
                is None
            ):

                distance_validation = assign_data.get("planning", {}).get(
                    "distance_validation", False
                )
                if not distance_validation and not request.auth.user.is_superuser:
                    return Response(
                        {
                            "code": {
                                "status": 300,
                                "message": "Distance is more than 50 Km.",
                            }
                        }
                    )

                order_id = assign_data["order_id"]
                driver = assign_data.get("driver")
                if not driver or not driver.get("driver_code"):
                    driver = None
                co_driver = assign_data.get("co_driver")
                LOGGER.info(f"AssignOrder co_driver1 >>> {co_driver}")
                if not co_driver or not co_driver.get("driver_code"):
                    co_driver = None
                LOGGER.info(f"AssignOrder co_driver2 >>> {co_driver}")
                try:
                    override_geo = assign_data["override_geo"]
                except:
                    override_geo = False
                vehicle = assign_data["vehicle"]
                if vehicle:
                    vehicle_obj = Vehicles.objects.filter(
                        id=vehicle["id"], source_name__in=VehicleSourcing.OWN_PLANNING.value, is_deleted=True
                        )
                    if vehicle_obj:
                        return Response({"code": {'status': 300, 'message': 'Vehicle is In-Active.'}})
                hubs = assign_data["hubs"]
                check_same_day_condn = assign_data["check_same_day_condn"]
                if check_same_day_condn is False:
                    ord_obj_chk = Order.objects.filter(
                        indent__date_vehicle_required=Order.objects.get(
                            id=order_id
                        ).indent.date_vehicle_required,
                        vehicle_id=assign_data["vehicle"]["id"],
                    )

                    if ord_obj_chk:
                        if (
                            ord_obj_chk.first().cancellation_type is not None
                            or order_id == ord_obj_chk[0].id
                        ):
                            pass
                        elif (
                            assign_data["order_code"] == ord_obj_chk.first().order_code
                        ):
                            pass
                        else:
                            try:
                                order_code = ord_obj_chk.first().order_code
                            except:
                                order_code = ""
                            msg = "Same vehicle can not be placed twice in a day. Vehicle is already assigned for order code {}".format(
                                order_code
                            )

                            return Response(
                                {
                                    "code": {"status": 503, "message": msg},
                                    "order_code": order_code,
                                    "check_same_day_condn": True,
                                }
                            )

                vehicle_adblue_ratio = Vehicles.objects.get(id=vehicle['id']).adblue_ratio


                trip = pymongo_crud.db['trip']
                query = {"order_id": order_id}
                trip_obj = trip.find_one(query)
                set_values = {"$set": {"override_geo": override_geo,
                                       "vehicle_adblue_ratio": vehicle_adblue_ratio}}
                trip.update(query, set_values)

                # Prafull
                if len(Order.objects.filter(id=order_id, float_to_market=True)):
                    return Response(
                        {
                            "code": {
                                "status": 400,
                                "message": "Vehicle is already floated to market",
                            }
                        }
                    )
                if (
                    trip_obj.get('trip_status', {}).get("status_name") == "Planned"
                    or trip_obj.get('trip_status', {}).get("status_name") == "Semi Planned"
                    or trip_obj.get('trip_status', {}).get("status_name") == "New"
                ):
                    # Prafull
                    if assign_data["type"] == "ASSIGN":
                        if len(
                            Order.objects.filter(
                                id=order_id, order_assigned=True, float_to_market=False
                            )
                        ):
                            return Response(
                                {
                                    "code": {
                                        "status": 300,
                                        "message": "Trip already assigned. ",
                                    }
                                }
                            )
                    order_obj = Order.objects.get(id=order_id)
                    if order_obj is not None:
                        if driver:
                            dri_obj = Driver.objects.get(
                                id=driver["id"],
                                driver_status=DriverActiveStatus.ACTIVE.value
                            )
                            if not dri_obj:
                                return Response(
                                    {"code": {"status": 300, "message": "Driver is Not Active"}}
                                )
                            order_obj.driver_id = driver["id"]
                            dri_obj.allocation_status = True
                            dri_obj.updated_at = timezone.now()
                            dri_obj.updated_by = request.auth.user.username
                            dri_obj.save()
                        try:
                            if co_driver is not None:
                                if Driver.objects.filter(
                                    id=co_driver["id"],
                                    driver_status__in=[DriverActiveStatus.INACTIVE.value, DriverActiveStatus.BLACKLIST.value],
                                ):
                                    return Response(
                                        {
                                            "code": {
                                                "status": 300,
                                                "message": "Co-driver is Not Active",
                                            }
                                        }
                                    )
                                order_obj.co_driver["id"] = co_driver["id"]
                                order_obj.co_driver["driver_code"] = co_driver[
                                    "driver_code"
                                ]
                                order_obj.co_driver["driver_name"] = co_driver[
                                    "driver_name"
                                ]
                                order_obj.co_driver["mobile_number"] = co_driver[
                                    "mobile_number"
                                ]
                                order_obj.co_driver["allocation_status"] = co_driver[
                                    "allocation_status"
                                ]
                                Driver.objects.filter(id=co_driver["id"]).update(
                                    allocation_status=True,
                                    updated_at=timezone.now(),
                                    updated_by = request.auth.user.username
                                )
                        except:
                            pass
                        order_obj.vehicle_id = vehicle["id"]
                        order_obj.order_assigned = True
                        order_obj.hubs = hubs
                        order_obj.float_to_market = False
                        # Ashish
                        order_obj.current_status = "Vehicle Assigned"
                        order_obj.updated_at = timezone.now()
                        order_obj.updated_by = request.auth.user.username
                        order_obj.save()
                        order_obj.source_name = order_obj.vehicle.source_name
                        order_obj.save()
                        Vehicles.objects.filter(id=order_obj.vehicle_id).update(
                            vehicle_allocation_status=True,
                            updated_at = timezone.now(),
                            updated_by=request.auth.user.username
                        )
                        LOGGER.info(f"AssignOrder co_driver3 >>> {co_driver}")
                        if (
                            user_define_methods.update_order_data(
                                order_obj, None, "assign"
                            )
                            is True
                        ):
                            print("Successfully Updated Trip Data")
                        else:
                            print("Error Happened in Trip Data")
                        # Function for writing update driver and vehicle

                        # Es call
                        try:
                            views.single_location_veh_customer_index(order=order_obj)
                        except:
                            pass

                        # Ashish
                        user_obj = UserManagement.objects.filter(
                            user_id=request.user.id
                        )
                        user_ser = UserManagementSerializer(user_obj, many=True).data
                        user_code = user_ser[0]["user_code"]
                        user_first_name = user_ser[0]["user_manage_data"]["first_name"]
                        user_email = user_ser[0]["user_manage_data"]["email"]
                        order_obj1 = Order.objects.filter(id=order_id)
                        order_ser = OrderApprovedSerializer(order_obj1, many=True).data
                        hub_manager_id = HubManagement.objects.get(
                            id=hubs["id"]
                        ).hub_manager_id
                        hub_manager_no = UserManagement.objects.get(
                            id=hub_manager_id
                        ).mobile_no
                        customer_obj = Customer.objects.get(
                            id=assign_data["customer_id"]
                        )
                        customer_bilty_id = customer_obj.bilty_boy_id
                        customer_name = customer_obj.company_name
                        customer_bilty_no = UserManagement.objects.get(
                            id=customer_bilty_id
                        ).mobile_no
                        msg_bilty = (
                            "New order, -%s- assigned to you.\nCustomer: %s\nSource: %s @ %s, %s \nGOBOLT"
                            % (
                                assign_data["order_code"],
                                customer_name,
                                assign_data["origin"],
                                assign_data["date_vehicle_required"],
                                assign_data["time_vehicle_required"],
                            )
                        )
                        try:
                            if driver is not None and assign_data["type"] != "PUT":
                                msg_hub = (
                                    "New order, -%s- assigned to you.\nCustomer: %s\nVehicle Number: %s\n "
                                    "Driver: %s (%s)\nGOBOLT"
                                    % (
                                        assign_data["order_code"],
                                        customer_name,
                                        vehicle["code"],
                                        driver["driver_name"],
                                        driver["driver_code"],
                                    )
                                )
                                send_sms.send_sms(hub_manager_no, msg_hub)
                                send_sms.send_sms(customer_bilty_no, msg_bilty)
                            elif assign_data["type"] == "PUT":
                                send_sms.send_sms(customer_bilty_no, msg_bilty)
                            else:
                                msg_hub = (
                                    "New order, -%s- assigned to you.\nCustomer: %s\nVehicle Number: %s\n "
                                    "Driver: Not Assigned\nGOBOLT"
                                    % (
                                        assign_data["order_code"],
                                        customer_name,
                                        vehicle["code"],
                                    )
                                )
                                send_sms.send_sms(hub_manager_no, msg_hub)
                        except:
                            pass
                        try:
                            IndentOrderLogs.objects.create(
                                action="vehicle_assigned",
                                user={
                                    "name": user_first_name,
                                    "code": user_code,
                                    "email": user_email,
                                },
                                action_data=order_ser[0],
                                timestamp=timezone.now(),
                                order_id_id=order_ser[0]["id"],
                                indent_id=order_ser[0]["indent_id"],
                                created_at = timezone.now(),
                                created_by = request.auth.user.username,
                            )
                        except Exception as e:
                            pass

                    else:
                        return Response({"code": status_code.code_203})
                    elapsed = timeit.default_timer() - start_time
                    print("This is time for execution", elapsed)
                    # if vehicle assigned to indent successfully then \
                    # publish data to pubsub on create-indent-stg topic
                    new_order_obj = Order.objects.get(id=order_id)
                    current_order = new_order_obj
                    # get manifest_number using indent foreign key
                    manifest_number = current_order.indent.manifest_number
                    # get customer_code using indent foreign key
                    customer_code = current_order.indent.customer.customer_code
                    # get vehicle details
                    vehicle_details = current_order.vehicle
                    vehicle_code = vehicle_details.code
                    vehicle_number = vehicle_details.vehicle_registration_number
                    placement_date = str(current_order.assign_date_time)
                    # driver details
                    driver_details = current_order.driver

                    if manifest_number and customer_code:
                        vehicle_details = {
                            "manifestNumber": manifest_number,
                            "vehicleNumber": vehicle_number,
                            "vehicleCode": vehicle_code,
                            "placementDate": placement_date,
                            "partition": customer_code,
                            "event-type": "add-vehicle-details",
                        }
                        if driver_details:
                            vehicle_details['driverName'] = driver_details.driver_name
                            vehicle_details['driverCode'] = driver_details.driver_code
                            vehicle_details['driverContactNumber'] = driver_details.mobile_number
                        # publish vehicle details data to pubsub
                        Pub(settings.INDENT_PUBSUB_TOPIC_NAME).publish(vehicle_details)

                    return Response({"code": status_code.code_200})
                else:
                    return Response(
                        {
                            "code": {
                                "status": 300,
                                "message": "Can not edit now.Use cancellation by Bilty.",
                            }
                        }
                    )
        except Exception as e:
            print(
                "Exception is :- \n~",
                e.args[0],
                "\noccurred at line number:",
                sys.exc_info()[2].tb_lineno,
            )
            return Response({"code": {"status": 300, "message": e.args[0]}})


class AssignOrderMarketByHub(APIView):
    @method_decorator(login_required(login_url="/"))
    def post(self, request):
        try:
            assign_data = request.data
            if (
                validictory.validate(
                    assign_data, validation_model.schema_assign_hub_driver
                )
                is None
            ):
                order_id = assign_data["order_id"]
                driver = assign_data["driver"]
                order_obj = Order.objects.get(id=order_id)
                if order_obj is not None:
                    order_obj.driver_id = driver["id"]
                    order_obj.order_assigned = True
                    order_obj.float_to_market = False
                    order_obj.save()
                    # Function for writing update driver and vehicle
                    Driver.objects.filter(id=driver["id"]).update(
                        allocation_status=True
                    )
                    # Writing Function for update order data mongo
                    if user_define_methods.update_order_data(order_obj) is True:
                        print("Successfully Updated Trip Data")
                    else:
                        print("Error Happened in Trip Data")
                else:
                    return Response({"code": status_code.code_203})
                return Response({"code": status_code.code_200})
        except Exception as e:
            print(e)
            return Response({"code": status_code.code_300})


######################################################################################
#  Function Name- assign_order_unassign                                              #
#  Operation - assign order data to send again unassign by planning manager          #
#  By - Vishnu Badal                                                                 #
#  Date - 14 Sep 2016                                                                #
######################################################################################


@login_required(login_url="/")
@api_view(
    ["GET",]
)
def assign_order_unassign(request):
    try:
        return Response(
            {
                "code": {
                    "status_code": 300,
                    "message": "This Functionality is under development. Please reject trip from bilty",
                }
            }
        )
        # order_data = Order.objects.all().order_by("-created_date_time")
        data_id = request.GET.get("id")
        data_type = request.GET.get("type")
        if data_type == "unassign":
            # Write Here Logs
            trip_obj = Trip.objects.get(order_id=data_id)
            if (
                trip_obj.trip_status["status_name"] == "Planned"
                or trip_obj.trip_status["status_name"] == "Semi Planned"
            ):
                order_obj = Order.objects.get(id=data_id)
                driver_id = order_obj.driver_id
                vehicle_id = order_obj.vehicle_id
                if len(
                    Order.objects.filter(
                        id=data_id, order_assigned=False, float_to_market=False
                    )
                ):
                    return Response(
                        {
                            "code": {
                                "status": 300,
                                "message": "Vehicle already unassigned.",
                            }
                        }
                    )
                Order.objects.filter(id=data_id).update(
                    order_assigned=False,
                    float_to_market=False,
                    driver_id="",
                    vehicle_id="",
                )
                Driver.objects.filter(id=driver_id).update(allocation_status=False)
                Vehicles.objects.filter(id=vehicle_id).update(
                    vehicle_allocation_status=False
                )
                # Update order Data of Mongo
                if (
                    user_define_methods.update_order_data(order_obj, None, "" "", False)
                    is True
                ):
                    print("Successfully updated unassign")
                else:
                    print("Something Bad Happened")
                try:
                    user_obj = UserManagement.objects.filter(user_id=request.user.id)
                    user_ser = UserManagementSerializer(user_obj, many=True).data
                    user_code = user_ser[0]["user_code"]
                    user_first_name = user_ser[0]["user_manage_data"]["first_name"]
                    IndentOrderLogs.objects.create(
                        action="own_vehicle_unassign",
                        user={"name": user_first_name, "code": user_code},
                        timestamp=timezone.now(),
                        order_id_id=data_id,
                        indent_id=order_obj.indent_id,
                        created_at = timezone.now(),
                    )
                except Exception as e:
                    print(
                        "Exception is :- \n",
                        e.args[0],
                        "\noccurred at line number:",
                        sys.exc_info()[2].tb_lineno,
                    )
            else:
                return Response(
                    {
                        "code": {
                            "status": 300,
                            "message": "Can not edit now.Use cancellation by Bilty.",
                        }
                    }
                )
        if data_type == "float":
            # if len(Order.objects.filter(id=data_id,float_to_market=True)):
            #    return Response({'code': {'status': 300, 'message': 'Vehicle already unassigned.'}})
            Order.objects.filter(id=data_id).update(float_to_market=True)
    except Exception as e:
        print(e)
    return Response({"code": status_code.code_200})


@login_required(login_url="/")
@api_view(
    ["POST",]
)
def unassign_order_assign_market(request):
    with transaction.atomic():
        try:
            user_dict = request.data["buyerDetails"]
        except:
            user_dict = user_define_methods.creating_user_dict(
                request.user.first_name,
                request.user.id,
                phone_no=request.auth.user.usermanagement_set.all(),
            )
        try:
            input_data = request.data
            LOGGER.info(f"unassign_order_assign_market input_data - {input_data}")
            broker_data = input_data["brokerName"]
            account = input_data["account"]
            source_name = input_data.get("source_name", {}).get("name")
            if source_name not in VehicleSourcing.MARKET.value:
                return Response(
                    {"code": {"status": 503, "message": "Please select Vehicle Source"}}
                )

            if Order.objects.get(id=input_data["order_id"]).b_amount is None:
                if reduce(
                    user_define_methods.check_broker_account,
                    [broker_data["id"], account["bank_acc_no"]],
                ):
                    order_id = input_data["order_id"]
                    driver_data = input_data["driver"]
                    try:
                        existing_driver_data = input_data["driver_name"]
                        if not input_data["driver_name"]["id"]:
                            return Response(
                                {
                                    "code": {
                                        "status": 503,
                                        "message": "Please provide Driver details",
                                    }
                                }
                            )
                    except:
                        existing_driver_data = {}
                        if driver_data["driver_name"] and driver_data["driver_number"]:
                            pass
                        else:
                            return Response(
                                {
                                    "code": {
                                        "status": 503,
                                        "message": "Please provide Driver details",
                                    }
                                }
                            )

                    vehicle_data = input_data["vehicle"]
                    broker_owner_data = input_data["broker_owner"]

                    # try:
                    #     deductions = broker_owner_data["deductions"]
                    # except:
                    #     deductions = 0

                    # broker_advance_cash = float(
                    #     broker_owner_data.get("broker_advance_cash", 0)
                    # )
                    # broker_owner_data["broker_advance_cash"] = broker_advance_cash

                    # if float(broker_owner_data["broker_advance_cash"]) + float(
                    #     broker_owner_data["broker_owner_advance"]
                    # ) - float(deductions) > float(
                    #     broker_owner_data["broker_owner_rate"]
                    # ):
                    #     return Response(
                    #         {
                    #             "code": {
                    #                 "status": 503,
                    #                 "message": "Broker advance and broker cash advance should not be greater then broker rate",
                    #             }
                    #         }
                    #     )
                    if Broker.objects.filter(
                        id=broker_data["id"],
                        is_aprove=True,
                        is_deleted=False,
                        is_malicious=False,
                    ):
                        pass
                    else:
                        return Response(
                            {
                                "code": {
                                    "status": 503,
                                    "message": "Please Activate Broker first..",
                                }
                            }
                        )

                    order_data = Order.objects.select_for_update().get(id=order_id)
                    if order_data.current_status == "Distributed":
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "This order is already distributed in market.",
                                }
                            }
                        )

                    if not order_data.float_to_market:
                        return Response(
                            {
                                "code": {
                                    "status": 503,
                                    "message": "You have enter wrong data. Please Refresh your page.",
                                }
                            }
                        )

                    if order_data.float_to_app:
                        return Response(
                            {
                                "code": {
                                    "status": 503,
                                    "message": "This order is already distributed to broker.",
                                }
                            }
                        )

                    if Order.objects.filter(
                        id=order_id,
                        order_assigned=True,
                        trip_assigned_source=True,
                        current_status="Planning Initiated",
                    ):
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "Planning of trip is already initiated.",
                                }
                            }
                        )
                    if order_data.is_approve:
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": "Vehicle already approved.",
                                }
                            }
                        )
                    trip_obj = Trip.objects.filter(order_id=order_id)
                    t_status = trip_obj[0].trip_status
                    if t_status.get('status_name') in ["Completed", "In Transit", "Vehicle Reported", "Deleted"]:
                        return Response(
                            {
                                "code": {
                                    "status": 300,
                                    "message": f"Trip status already beyond planning ({t_status.get('status_name')}).",
                                }
                            }
                        )

                    if order_data is not None and trip_obj is not None:
                        try:
                            vehicle_data_id = input_data["vehicle_number"]["id"]
                            vehicle_obj = Vehicles.objects.get(id=vehicle_data_id)
                            check_same_day_condn = input_data["check_same_day_condn"]
                            if check_same_day_condn is False:
                                ord_obj_chk = Order.objects.filter(
                                    indent__date_vehicle_required=Order.objects.get(
                                        id=input_data["order_id"]
                                    ).indent.date_vehicle_required,
                                    vehicle_id=input_data["vehicle_number"]["id"],
                                )
                                if len(ord_obj_chk):
                                    if (
                                        ord_obj_chk.first().cancellation_type
                                        is not None
                                        or input_data["order_id"] == ord_obj_chk[0].id
                                    ):
                                        pass
                                    else:
                                        return Response(
                                            {
                                                "code": {
                                                    "status": 503,
                                                    "message": "Same vehicle can not be placed twice in a day.",
                                                },
                                                "order_code": ord_obj_chk.first().order_id,
                                            }
                                        )
                        except Exception as e:
                            try:
                                vehicle_number = vehicle_data["vehicle_number"]
                            except Exception as e:
                                return Response(
                                    {
                                        "code": {
                                            "status": 505,
                                            "message": "Please provide vehicle number",
                                        }
                                    }
                                )
                            vehicle_type_data = vehicle_data["vehicle_type"]
                            vehicle_obj = user_define_methods.create_vehicle_market(
                                vehicle_data, source_name.upper()
                            )
                            if vehicle_obj is None:
                                return Response({"code": status_code.code_504})
                            vehicle_data_id = vehicle_obj.id
                        LOGGER.info(f"Broker vehicle Relation -\n broker id {broker_data['id']} \n-  vehicle id -\n")
                        vehicle_rel_broker = VehicleBrokerRel.objects.filter(
                            vehicle_id=vehicle_data_id,
                        )
                        if vehicle_rel_broker.exists():
                            vehicle_rel_broker.delete()
                        VehicleBrokerRel.objects.create(
                            vehicle_id=vehicle_data_id,
                            broker_id=broker_data["id"],
                        )
                        try:
                            driver_data_id = existing_driver_data["id"]
                            driver_obj = Driver.objects.get(id=driver_data_id)
                        except Exception as e:

                            driver_obj = user_define_methods.create_driver_market(
                                driver_data
                            )
                            if driver_obj is None:
                                return Response({"code": status_code.code_503})
                            driver_data_id = driver_obj.id

                        order_data.vehicle_id = vehicle_data_id
                        order_data.driver_id = driver_data_id
                        order_data.broker_id = broker_data["id"]
                        order_data.order_assigned = True
                        order_data.broker_rate = broker_owner_data["broker_owner_rate"]
                        # order_data.broker_advance = broker_owner_data[
                        #     "broker_owner_advance"
                        # ]
                        order_data.broker_advance_cash = 0
                        order_data.trip_assigned_source = True
                        order_data.sourcing_user = user_dict
                        order_data.broker_account = account
                        order_data.deductions = broker_owner_data.get("deductions", 0)
                        order_data.tds = input_data["tds"]
                        order_data.current_status = "Planning Initiated"
                        order_data.source_name = source_name.upper()
                        order_data.updated_at = timezone.now()
                        order_data.updated_by = request.auth.user.username
                        order_data.save()
                        driver_obj.allocation_status = True
                        driver_obj.updated_at = timezone.now()
                        driver_obj.updated_by = request.auth.user.username
                        driver_obj.save()
                        vehicle_obj.vehicle_allocation_status = True
                        assigned_driver_data = {}
                        assigned_driver_data["id"] = driver_obj.id
                        assigned_driver_data["driver_code"] = driver_obj.driver_code
                        assigned_driver_data["driver_name"] = driver_obj.driver_name
                        assigned_driver_data["mobile_number"] = driver_obj.mobile_number
                        vehicle_obj.driver_data = assigned_driver_data
                        vehicle_obj.updated_by = request.auth.user.username
                        vehicle_obj.updated_at = timezone.now()
                        vehicle_obj.save()
                        try:
                            views.single_location_veh_customer_index(order=order_data)
                            views.single_location_bro_customer_index(order=order_data)

                        except:
                            pass
                        views.broker_based_single_vehicle_create(vehicle_obj, broker_data["broker_code"])
                        views.broker_based_single_driver_create(driver_obj, broker_data["broker_code"])
                        try:
                            msg = (
                                "New order, -%s- for approval.\nSource: %s\n Destination: %s\n Date: %s  @%s\nGOBOLT"
                                % (
                                    order_data.order_id,
                                    input_data["source"],
                                    input_data["destination"],
                                    input_data["date_vehicle_required"],
                                    input_data["time_vehicle_required"],
                                )
                            )

                        except:
                            pass

                        if (
                            user_define_methods.update_order_data(
                                order_data, None, "assign", True
                            )
                            is True
                        ):
                            print("All Data Successfully Updated")

                            try:
                                user_obj = UserManagement.objects.filter(
                                    user_id=request.user.id
                                )
                                user_ser = UserManagementSerializer(
                                    user_obj, many=True
                                ).data
                                user_code = user_ser[0]["user_code"]
                                user_first_name = user_ser[0]["user_manage_data"][
                                    "first_name"
                                ]
                                IndentOrderLogs.objects.create(
                                    action="vehicle_approval_request",
                                    user={"name": user_first_name, "code": user_code},
                                    timestamp=timezone.now(),
                                    order_id_id=order_data.id,
                                    indent_id=order_data.indent_id,
                                    created_at = timezone.now(),
                                    created_by = request.auth.user.username,
                                )
                            except Exception as e:
                                print(
                                    "Exception is :- \n",
                                    e.args[0],
                                    "\noccurred at line number:",
                                    sys.exc_info()[2].tb_lineno,
                                )
                        else:
                            print("Data is not updated")
                        return Response({"code": status_code.code_200})
                    else:
                        return Response({"code": status_code.code_502})
                else:
                    return Response(
                        {
                            "code": {
                                "code": 300,
                                "message": "Please enter valid broker account",
                            }
                        }
                    )
            else:
                return Response(
                    {"code": {"code": 300, "message": "Already send for Bid."}}
                )
        except Exception as e:
            LOGGER.info(f"Exception unassign_order_assign_market - {str(e)}", exc_info=True)
            return Response({"code": {"status": 300, "message": e.args[0]}})


#######################################################################################################################
# Name -
#
#
######################################################################################################################
# Import
from sourcing_agent.serializers import VehicleSourceGet, DriverMarketGet



@login_required(login_url="/")
@api_view(["PUT"])
def assign_market_edit(request):
    try:
        try:
            user_dict = request.data["sourcing_user"]
        except:
            user_dict = user_define_methods.creating_user_dict(
                request.user.first_name,
                request.user.id,
                phone_no=request.auth.user.usermanagement_set.all(),
            )
        edit_data = request.data
        print(edit_data)
        source_name = edit_data.get("source_name", {}).get("name")
        if source_name not in VehicleSourcing.MARKET.value:
            return Response(
                {"code": {"status": 503, "message": "Please select Vehicle Source"}}
            )
        vehicle_id = edit_data["vehicle_id"]
        deductions = edit_data.get("deductions", 0)
        order_id = edit_data["order_id"]
        # broker_advance = edit_data["broker_advance"]
        driver_id = edit_data["driver_id"]
        broker_id = edit_data["broker_id"]
        broker_account = edit_data["broker_account"]
        # broker_advance_cash = edit_data["broker_advance_cash"]
        broker_rate = edit_data["broker_rate"]
        newVehicle = edit_data["newVehicle"]
        newDriver = edit_data["newDriver"]
        tds = edit_data["tds"]
        if not Order.objects.filter(id=order_id, current_status="Planning Initiated"):
            return Response(
                {"code": {"status": 300, "message": "Vehicle already approved."}}
            )
        # if float(broker_advance_cash) + float(broker_advance) - float(
        #     deductions
        # ) > float(edit_data["broker_rate"]):
        #     return Response(
        #         {
        #             "code": {
        #                 "status": 503,
        #                 "message": "Broker advance and broker cash advance should not be greater then broker rate",
        #             }
        #         }
        #     )
        if Order.objects.filter(id=order_id, current_status="Vehicle Assigned"):
            return Response(
                {"code": {"status": 300, "message": "Trip is already planned."}}
            )
        if newDriver != "":
            print("Than create Driver")
            driver_obj = user_define_methods.create_driver_market(newDriver)
            driver_id = driver_obj.id
            if driver_id == "":
                return Response(
                    {"code": {"status": 300, "message": "Driver already exists."}}
                )

        if newVehicle != "":
            # Creating new vehicle as a market type
            vehicle_obj = user_define_methods.create_vehicle_market(newVehicle, source_name.upper())
            if vehicle_obj is not None:
                vehicle_id = vehicle_obj.id
                if vehicle_id != "":
                    try:
                        if not len(
                            VehicleBrokerRel.objects.filter(
                                vehicle_id=vehicle_id, broker_id=broker_id
                            )
                        ):
                            VehicleBrokerRel.objects.create(
                                vehicle_id=vehicle_id, broker_id=broker_id
                            )
                    except Exception as e:
                        print("Exception during broker vehicle attach is", e.args[0])
                print("Than create Vehicle")
                if vehicle_id == "":
                    return Response(
                        {"code": {"status": 300, "message": "Vehicle already exists."}}
                    )
            else:
                return Response(
                    {"code": {"status": 300, "message": "Vehicle already exists."}}
                )
        # if deductions == "":
        #     deductions = 0

        order_obj = Order.objects.get(id=order_id)
        if order_obj is not None:
            order_obj.vehicle_id = vehicle_id
            order_obj.tds = tds
            order_obj.source_name = source_name.upper()
            order_obj.driver_id = driver_id
            order_obj.broker_id = broker_id
            order_obj.order_assigned = True
            order_obj.broker_rate = broker_rate
            # order_obj.broker_advance = broker_advance
            # order_obj.broker_advance_cash = broker_advance_cash
            order_obj.trip_assigned_source = True
            order_obj.sourcing_user = user_dict
            order_obj.broker_account = broker_account
            order_obj.deductions = deductions
            order_obj.vehicle.vehicle_allocation_status = True
            order_obj.vehicle.save()
            order_obj.driver.allocation_status = True
            order_obj.driver.save()
            order_obj.save()
            try:
                views.single_location_veh_customer_index(order=order_obj)
                views.single_location_bro_customer_index(order=order_obj)
                # print("Call both api")
            except:
                pass
            if (
                user_define_methods.update_order_data(order_obj, None, "assign", True)
                is True
            ):
                print("All Data Successfully Updated")
            else:
                print("Data is not updated")
            vehicle_obj = Vehicles.objects.filter(id=vehicle_id)
            vehicle_data = VehicleSourceGet(vehicle_obj, many=True).data
            driver_obj = Driver.objects.filter(id=driver_id)
            driver_data = DriverMarketGet(driver_obj, many=True).data
            return Response(
                {
                    "code": status_code.code_200,
                    "vehicle_data": vehicle_data[0],
                    "driver_data": driver_data[0],
                }
            )
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})

        # Existing vehicle
        # Existing Driver
        # Non Existing vehicle
        # non Existing Driver


################################################################################
# Name - trip_approved
# Operations - It is used for approved by seniors
################################################################################


@api_view(["GET", "POST"])
def order_comment(request):
    try:
        if request.method == "GET":
            order_id = request.GET.get("id")
            order_obj_data = Order.objects.get(id=order_id)
            return Response(
                {"code": status_code.code_200, "comment": order_obj_data.comment}
            )
        elif request.method == "POST":
            input_data = request.data
            order_id = input_data["id"]
            comment = input_data["comment"]
            try:
                Order.objects.filter(Q(id=order_id) & Q(is_approve=False)).update(
                    comment=comment
                )
                return Response({"code": status_code.code_200})
            except Exception as e:
                print(e)
                return Response({"code": status_code.code_203})
        else:
            return Response({"code": status_code.code_301})
    except Exception as e:
        print(e)
    return Response({"code": status_code.code_501})


################################################################################################


@login_required(login_url="/")
@api_view(["POST"])
def expense_admin(request):
    try:
        expense_data = request.data
        print(expense_data)

        soft_expenses = expense_data["soft_expenses"]
        total_advance = expense_data["total_advance"]
        toll = expense_data["toll"]
        fuel = expense_data["fuel"]
        entry = expense_data["entry"]
        fooding = expense_data["fooding"]
        origin_city = expense_data["origin_city"]
        destination_city = expense_data["destination_city"]
        route_name = expense_data["route_name"]
        #     here write a function fr checking already exising data
        if not len(ExpenseManagement.objects.filter(route_name__iexact=route_name)):
            ExpenseManagement(
                soft_expenses=soft_expenses,
                total_advance=total_advance,
                toll=toll,
                fuel=fuel,
                entry=entry,
                food=fooding,
                origin_city=origin_city,
                destination_city=destination_city,
                route_name=route_name,
            ).save()

            return Response({"code": status_code.code_200})
        return Response({"code": status_code.code_401})
    except Exception as e:
        print(e)
        return Response({"code": {"status": 501, "message": e.args[0]}})


class ExpenseListing(APIView):
    @method_decorator(login_required(login_url="/"))
    def get(self, request):
        expense = ExpenseManagement.objects.all().order_by("-id")
        expense = ExpenseSerializer(expense, many=True).data
        y = iter(expense)
        expense = None
        del expense
        gc.collect()

        return Response({"code": status_code.code_200, "indent": y})

    def post(self, request):
        pass


class ExpenseEditing(APIView):
    def put(self, request, *args, **kwargs):
        edit_data = request.data
        try:
            expense_id = edit_data["id"]
            soft_expenses = edit_data["soft_expenses"]
            total_advance = edit_data["total_advance"]
            toll = edit_data["toll"]
            fuel = edit_data["fuel"]
            entry = edit_data["entry"]
            fooding = edit_data["food"]
            origin_city = edit_data["origin_city"]
            destination_city = edit_data["destination_city"]
            route_name = edit_data["route_name"]

            ExpenseManagement.objects.filter(
                id=expense_id, route_name__iexact=route_name
            ).update(
                soft_expenses=soft_expenses,
                total_advance=total_advance,
                toll=toll,
                fuel=fuel,
                entry=entry,
                food=fooding,
                origin_city=origin_city,
                destination_city=destination_city,
                route_name=route_name,
            )
            return Response({"code": status_code.code_200})
        except Exception as e:
            # print(e)
            return Response({"code": status_code.code_300})


@api_view(["POST"])
def get_expense_route_names(request):
    try:
        advance_expense = request.data
        # print(advance_expense)
        origin_id = advance_expense["origin_id"]
        destination_id = advance_expense["destination_id"]
        matching_expense_objects = ExpenseManagement.objects.filter(
            origin_city__icontains=origin_id, destination_city__icontains=destination_id
        )
        matching_expense_objects = ExpenseAdvanceSerializer(
            matching_expense_objects, many=True
        ).data
        return Response(
            {"code": status_code.code_200, "data_list": matching_expense_objects}
        )
        # here write a function fr checking already exising data
    except Exception as e:
        # print(e)
        return Response({"code": {"status": 501, "message": e.args[0]}})


class OrderListBasedIndent(APIView):
    """
    View to list of Order in the System Based on Indent
    """

    def get(self, request, indent_id):
        orders = Order.get_order_indent(indent_id=indent_id)
        data = IndentBasedOrderSerializer(orders, many=True).data
        for d in data:
            trip = Trip.objects.get(order_id=d["id"])
            d["trip_status"] = trip.trip_status["status_name"]
        return Response({"code": status_code.code_200, "data": data})


class GetOrderStatus(APIView):
    """
    Getting latest status of Order Based on order id
    """

    def get(self, request, order_id):
        return Response(
            {"code": status_code.code_200, "data": Order.get_order_status(order_id)}
        )


class GetAllTollData(APIView):
    def post(self, request):
        try:
            toll_data_req = request.data
            state_name = toll_data_req["state"]
            lat_long = toll_data_req["lat_long"]
            data_list = []
            toll_obj = TollDetails.objects.filter(state=state_name)
            for i in toll_obj:
                latti = float(i.lat_long["lat"])
                longi = float(i.lat_long["long"])
                distance_remaining = (
                    vincenty((latti, longi), lat_long).miles
                ) * 1609.34
                if distance_remaining < 1000:
                    data_list.append(i)
                else:
                    pass
            toll_objects = TollSerializer(data_list, many=True).data
            return Response({"code": status_code.code_200, "data_list": toll_objects})
        except Exception as e:
            return Response({"code": {"status": 501, "message": e.args[0]}})


@api_view(["GET"])
def get_all_order_id(request):
    try:
        order_ids = list()
        if request.GET.get("search") is not None:
            try:
                order_ids = Order.objects.filter(
                    order_id__search=request.GET.get("search")
                ).values("order_id", "id")[:200]
            except:
                pass
        y = iter(order_ids)
        return Response({"code": status_code.code_200, "order_ids": y})
    except Exception as e:
        return Response({"code": status_code.code_200, "order_ids": list()})


@api_view(["GET"])
def get_sourcing_agents(request):
    data_list = []
    try:
        user_ids = UserManagement.objects.filter(is_sourcing_agent=True).values(
            "user_id"
        )
        for i in user_ids:
            user_data = User.objects.get(id=i["user_id"])
            try:
                phone_no = UserManagement.objects.get(user_id=i["user_id"]).mobile_no
            except Exception as e:
                print(e)
                phone_no = ""
            data_list.append(
                {
                    "user_id": user_data.id,
                    "username": str(user_data.first_name),
                    "mobile_no": phone_no,
                }
            )
        y = iter(data_list)
        data_list, user_ids = None, None
        del data_list, user_ids
        return Response({"code": status_code.code_200, "data_list": y})
    except Exception as e:
        print(e)


########################################################################################
# Name - bid_status
#
# By - Vishnu
########################################################################################


@api_view(["POST", "PUT"])
def publish_to_app(request):
    """
    This is used for publish order to App and get b_amount money if already publish
    :param request:
    :return:
    """
    try:
        if request.method == "POST":
            try:
                order_id = request.data
                order_obj = Order.objects.get(id=order_id)
                b_amount_saved = None
                b_amount_expected = None
                b_amount_saved = Order.objects.get(id=request.data).b_amount
                if b_amount_saved is None:
                    if order_obj is not None:
                        start_city = order_obj.start_city
                        end_city = order_obj.end_city
                        vehicle_type_id = order_obj.indent.vehicle_type.id
                        ord_obj = Order.objects.filter(
                            Q(current_status="Completed")
                            & Q(start_city__exact=start_city)
                            & Q(end_city__exact=end_city)
                            & Q(vehicle_id__vehicle_type_id=vehicle_type_id)
                        )[:10]
                        list1 = list()
                        for i in ord_obj:
                            if i.broker_rate != None:
                                list1.append(i.broker_rate)
                        b_amount_expected = max(set(list1), key=list1.count)
            except Exception as e:
                b_amount_saved = None
                b_amount_expected = None
            return Response(
                {
                    "code": status_code.code_200,
                    "b_amount_saved": b_amount_saved,
                    "b_amount_expected": b_amount_expected,
                }
            )

        elif request.method == "PUT":
            order_obj = Order.objects.filter(id=request.data["id"])
            order_obj.update(b_amount=request.data["b_amount"])
            #  Send Notification to all broker in city for floated order
            try:
                broker_obj = BrokerCityDetails.objects.filter(
                    start_city__name=order_obj[0].start_city,
                    end_city__name=order_obj[0].end_city,
                )
                user_ids = map(lambda z: z.broker.user.id, iter(broker_obj))
                device_reg_ids = list()
                for i in user_ids:
                    mydev_obj = MyDevice.objects.filter(user_id=i, is_active=True)
                    if len(mydev_obj):
                        device_reg_ids.append(str(mydev_obj[0].reg_id))
                if len(device_reg_ids):
                    send_noti(
                        device_reg_ids=device_reg_ids,
                        title="New Order.",
                        body="New order is available for bidding.",
                        message="New order is available go to floated bid section",
                    )
            except Exception as e:
                print("Unable to send  notification because of: ", e.args[0])
            return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


##################################################################################################
# Name - un_publish_to_app
#
# By - Vishnu
#################################################################################################
@api_view(["POST"])
def un_publish_to_app(request):
    """
    This is used to return back from App.
    :param request:
    :return:
    """
    o_obj = Order.objects.filter(id=request.data, bid_done=False)
    if o_obj:
        o_obj.update(b_amount=None)
        Bid.objects.filter(order_id=o_obj[0].id).update(bid_status="Reverted")
        code = status_code.code_200
    else:
        code = {"status": 300, "message": "Bid already Approved."}
    return Response({"code": code})


@api_view(["PUT"])
def bid_approved_a(request):
    """
    This is used for approved a bid and assign broker, broker rate and other
    :param request:
    :return:
    :parameter order_id, broker_id, broker_rate, broker_advance, broker_advance_cash, deductions etc
    """
    user_dict = user_define_methods.creating_user_dict(
        request.auth.user.first_name,
        request.auth.user.id,
        phone_no=request.auth.user.usermanagement_set.all(),
    )
    b_obj = Bid.objects.filter(order_id=request.data["order_id"])
    o_up, b_obj = (
        b_obj.exclude(broker_id=request.data["broker_id"]),
        b_obj.filter(broker_id=request.data["broker_id"]),
    )
    if b_obj:
        if not b_obj[0].bid_done:
            o_obj = Order.objects.filter(id=b_obj[0].order_id)
            o_obj.update(
                broker_id=b_obj[0].broker_id,
                broker_rate=request.data["broker_rate"],
                broker_advance_cash=request.data["advance_cash"],
                broker_advance=request.data["broker_advance"],
                deductions=request.data["deductions"],
                tds=float(request.data["tds"]),
                bid_done=True,
                sourcing_user=user_dict,
            )
            b_obj.update(
                bid_done=True,
                bid_status="Approved",
                advance_cash=request.data["advance_cash"],
                broker_advance=request.data["broker_advance"],
                deductions=request.data["deductions"],
                tds=float(request.data["tds"]),
                broker_rate=request.data["broker_rate"],
            )
            if o_up:
                o_up.update(bid_status="Rejected")
            # Send notification to broker that bid is approved.
            try:
                mydev_obj = MyDevice.objects.filter(
                    user_id=Broker.objects.filter(id=request.data["broker_id"])[
                        0
                    ].user_id
                )
                reg_ids = list()
                if len(mydev_obj):
                    reg_ids.append(str(mydev_obj[0].reg_id))
                    send_noti(
                        device_reg_ids=reg_ids,
                        title="Your bid is approved.",
                        body="Please assign vehicle.",
                        message="Your bid is approved please assign vehicle.",
                    )
            except Exception as e:
                print("Notification not sent because of: ", e.args[0])

            code = {"status": 200, "message": "Successfully Approved."}
        else:
            code = {"status": 300, "message": "Bid already done"}
    else:
        code = {"status": 300, "message": "You have entered wrong data."}

    return Response({"code": code})


############################################################################################
#
#
###########################################################################################


@api_view(["PUT"])
def selected_bid_set(request):
    """
    This is used for update vehicle number and driver from broker for a Particular Order
    :param request:
    :return:
    :parameter broker_id, order_id, vehicle_no, driver_name, driver_no
    """
    try:
        # print(request.data)
        o_obj = Order.objects.filter(
            id=request.data["order_id"], broker_id=request.data["broker_id"]
        )
        b_accounts = o_obj[0].broker.brokeraccount_set.all()
        if len(b_accounts) > 1:  # Check Main Account Flag
            b_accounts = b_accounts.filter(main_a=True)
        else:
            pass

        v_obj = Vehicles.objects.filter(
            vehicle_registration_number__iexact=request.data["vehicle_no"].strip(),
            is_market_vehicle=True,
        )
        d_obj = Driver.objects.filter(
            Q(driver_name__iexact=request.data["driver_name"])
            & Q(mobile_number__iexact=request.data["driver_no"])
        )

        if v_obj:
            v_obj.update(vehicle_allocation_status=True)
            v_obj = v_obj[0]
        else:  # create vehicle
            v_obj = user_define_methods.create_vehicle_market(
                {
                    "vehicle_number": request.data["vehicle_no"].strip(),
                    "vehicle_type": {"id": o_obj[0].indent.vehicle_type_id},
                }
            )
            v_obj.vehicle_allocation_status = True

            v_obj.save()

        if d_obj:
            d_obj.update(allocation_status=True)
            d_obj = d_obj[0]
        else:
            d_obj = user_define_methods.create_driver_market(
                {
                    "driver_name": request.data["driver_name"],
                    "driver_number": request.data["driver_no"],
                }
            )
            d_obj.allocation_status = True
            d_obj.save()

        # Broker Account Main Account & Sourcing user
        o_obj.update(
            vehicle_id=v_obj.id,
            driver_id=d_obj.id,
            current_status="Planning Initiated",
            trip_assigned_source=True,
            order_assigned=True,
            broker_account={
                "ifsc_code": b_accounts[0].ifsc_code,
                "bank_acc_no": b_accounts[0].bank_acc_no,
            },
        )
        assigned_driver_data = dict()
        assigned_driver_data["id"] = d_obj.id
        assigned_driver_data["driver_code"] = d_obj.driver_code
        assigned_driver_data["driver_name"] = d_obj.driver_name
        assigned_driver_data["mobile_number"] = d_obj.mobile_number
        v_obj.driver_data = assigned_driver_data
        v_obj.save()

        if user_define_methods.update_order_data(o_obj[0], None, "assign", True):
            print("All Data Successfully Updated")
            user_obj = UserManagement.objects.filter(user_id=request.auth.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]
            IndentOrderLogs.objects.create(
                action="vehicle_approval_request",
                user={"name": user_first_name, "code": user_code},
                timestamp=timezone.now(),
                order_id_id=o_obj[0].id,
                indent_id=o_obj[0].indent_id,
                created_at = timezone.now(),
                created_by = request.auth.user.username,
            )
        else:
            print("Trip Data is not Updated.")
        Bid.objects.filter(
            order_id=request.data["order_id"], broker_id=request.data["broker_id"]
        ).update(bid_status="Vehicle Assign")

        return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": status_code.code_203})


@api_view(["POST"])
def selected_bid_set_update(request):
    """
    This is used for update vehicle number and driver from broker for a Particular Order
    :param request:
    :return:
    :parameter broker_id, order_id, vehicle_no, driver_name, driver_no
    """
    try:
        # print(request.data)
        o_obj = Order.objects.filter(
            id=request.data["order_id"], broker_id=request.data["broker_id"]
        )
        b_accounts = o_obj[0].broker.brokeraccount_set.all()
        if len(b_accounts) > 1:  # Check Main Account Flag
            b_accounts = b_accounts.filter(main_a=True)
        else:
            pass

        v_obj = Vehicles.objects.filter(
            vehicle_registration_number__iexact=request.data["vehicle_no"].strip(),
            is_market_vehicle=True,
        )
        d_obj = Driver.objects.filter(
            Q(driver_name__iexact=request.data["driver_name"])
            & Q(mobile_number__iexact=request.data["driver_no"])
        )

        if v_obj:
            v_obj.update(vehicle_allocation_status=True)
            v_obj = v_obj[0]
        else:  # create vehicle
            v_obj = user_define_methods.create_vehicle_market(
                {
                    "vehicle_number": request.data["vehicle_no"].strip(),
                    "vehicle_type": {"id": o_obj[0].indent.vehicle_type_id},
                }
            )
            v_obj.vehicle_allocation_status = True

            v_obj.save()

        if d_obj:
            d_obj.update(allocation_status=True)
            d_obj = d_obj[0]
        else:
            d_obj = user_define_methods.create_driver_market(
                {
                    "driver_name": request.data["driver_name"],
                    "driver_number": request.data["driver_no"],
                }
            )
            d_obj.allocation_status = True
            d_obj.save()

        # Broker Account Main Account & Sourcing user
        o_obj.update(
            vehicle_id=v_obj.id,
            driver_id=d_obj.id,
            current_status="Planning Initiated",
            trip_assigned_source=True,
            order_assigned=True,
            broker_account={
                "ifsc_code": b_accounts[0].ifsc_code,
                "bank_acc_no": b_accounts[0].bank_acc_no,
            },
        )
        assigned_driver_data = dict()
        assigned_driver_data["id"] = d_obj.id
        assigned_driver_data["driver_code"] = d_obj.driver_code
        assigned_driver_data["driver_name"] = d_obj.driver_name
        assigned_driver_data["mobile_number"] = d_obj.mobile_number
        v_obj.driver_data = assigned_driver_data
        v_obj.save()

        if user_define_methods.update_order_data(o_obj[0], None, "assign", True):
            print("All Data Successfully Updated")
            user_obj = UserManagement.objects.filter(user_id=request.auth.user.id)
            user_ser = UserManagementSerializer(user_obj, many=True).data
            user_code = user_ser[0]["user_code"]
            user_first_name = user_ser[0]["user_manage_data"]["first_name"]
            IndentOrderLogs.objects.create(
                action="vehicle_approval_request",
                user={"name": user_first_name, "code": user_code},
                timestamp=timezone.now(),
                order_id_id=o_obj[0].id,
                indent_id=o_obj[0].indent_id,
                created_at = timezone.now(),
                created_by = request.auth.user.username,
            )
        else:
            print("Trip Data is not Updated.")
        Bid.objects.filter(
            order_id=request.data["order_id"], broker_id=request.data["broker_id"]
        ).update(bid_status="Vehicle Assign")

        return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": status_code.code_203})


# class GetAddressViaLatLong(APIView):
#
#     def get(self,request):
#         lat_long = request.GET.get('lat_long')
#
#


#
# with open('gobolt/toll_new.csv') as f:
#         reader = csv.reader(f)
#         for row in reader:
#             _, created = TollDetails.objects.get_or_create(
#                 state=row[0],
#                 nh_no=row[1],
#                 toll_name=row[2],
#                 toll_location=row[3],
#                 section=row[4],
#                 lat_long = {'lat': row[5].split(',')[0], 'long': row[5].split(',')[1]}
#                 )


@api_view(["PUT"])
def update_fav(request):
    try:
        fav_obj = Favorite.objects.all()
        for i in fav_obj:
            start_point_id = i.start_point["id"]
            start_loc_obj = LocationManagement.objects.get(id=start_point_id)
            i.start_point["location_point"]["coordinates"][
                "coordinates"
            ] = start_loc_obj.location_point["coordinates"]
            end_point_id = i.destination_point["id"]
            end_loc_obj = LocationManagement.objects.get(id=end_point_id)
            i.destination_point["location_point"]["coordinates"][
                "coordinates"
            ] = end_loc_obj.location_point["coordinates"]
            i.save()
        return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["GET"])
def get_intrans_time_efficiency(request):
    try:
        time_eff_obj = TimeEfficiency.objects.filter(is_deleted=False).values(
            "id", "name", "is_deleted", "created_at"
        )
        return Response({"code": status_code.code_200, "data_list": time_eff_obj})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["GET"])
def get_placement_efficiency_values(request):
    try:
        place_eff_obj = PlacementEfficiencyTime.objects.filter(is_deleted=False).values(
            "id", "name", "is_deleted", "created_at"
        )
        return Response({"code": status_code.code_200, "data_list": place_eff_obj})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["POST", "GET", "PUT"])
def broker_rate_matrix(request):
    if request.method == "POST":
        try:
            data_obj = request.data
            broker_obj = data_obj["broker_data"]
            for broker in broker_obj:
                broker_id = broker["broker_data"]["id"]
                broker_matrix_obj = BrokerRateMatrix.objects.filter(
                    customer_id=data_obj["customer_id"],
                    vehicle_type_id=data_obj["vehicle_type_id"],
                    start_point=data_obj["start_point"],
                    destination_point=data_obj["destination_point"],
                    broker_id=broker_id,
                    is_active=True,
                    contract_id=data_obj["contract_id"],
                )
                if len(broker_matrix_obj):
                    broker_matrix_obj.update(broker_rate=broker["broker_rate"])
                else:
                    BrokerRateMatrix.objects.create(
                        customer_id=data_obj["customer_id"],
                        vehicle_type_id=data_obj["vehicle_type_id"],
                        start_point=data_obj["start_point"],
                        destination_point=data_obj["destination_point"],
                        broker_rate=broker["broker_rate"],
                        broker_id=broker_id,
                        contract_id=data_obj["contract_id"],
                    )
            return Response({"code": status_code.code_200})
        except Exception as e:
            return Response({"code": {"status": 300, "message": e.args[0]}})

    if request.method == "GET":
        try:
            rate_obj = BrokerRateMatrix.objects.all()
            return Response(
                {
                    "code": status_code.code_200,
                    "data_list": map(
                        lambda z: broker_rate_return_data(z), iter(rate_obj)
                    ),
                }
            )
        except Exception as e:
            return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["POST"])
def broker_data_matrix_contract(request):
    try:
        data_obj = request.data
        data_obj = BrokerRateMatrix.objects.filter(
            customer_id=data_obj["customer"]["id"],
            vehicle_type_id=data_obj["vehicle_type"]["id"],
            start_point=data_obj["start_point"],
            destination_point=data_obj["destination_point"],
            is_active=True,
            contract_id=data_obj["id"],
        )

        return Response(
            {
                "code": status_code.code_200,
                "data_list": map(
                    lambda z: return_contract_broker_data(z), iter(data_obj)
                ),
            }
        )
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["POST"])
def broker_data_matrix_contract_remove(request):
    try:
        data_obj = request.data
        BrokerRateMatrix.objects.filter(
            customer_id=data_obj["contract_data"]["customer"]["id"],
            vehicle_type_id=data_obj["contract_data"]["vehicle_type"]["id"],
            start_point=data_obj["contract_data"]["start_point"],
            destination_point=data_obj["contract_data"]["destination_point"],
            broker_id=data_obj["broker_data"]["broker_data"]["id"],
            contract_id=data_obj["contract_data"]["id"],
        ).update(is_active=False)

        return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.agrs[0]}})


@api_view(["POST"])
def broker_rate_get(request):
    try:
        data_obj = request.data
        contract_id = Indent.objects.get(id=data_obj["indent_id"]).contract.id
        broker_rate = BrokerRateMatrix.objects.filter(
            customer_id=data_obj["customer_id"],
            vehicle_type_id=data_obj["vehicle_type_id"],
            start_point__id=data_obj["start_point_id"],
            destination_point__id=data_obj["destination_point_id"],
            broker_id=data_obj["broker_id"],
            is_active=True,
            contract_id=contract_id,
        )
        if len(broker_rate):
            broker_rate = broker_rate.first().broker_rate
        else:
            broker_rate = None
        return Response({"code": status_code.code_200, "broker_rate": broker_rate})
    except Exception as e:
        return Response({"code": {"status": 300, "message": e.args[0]}})


from dateutil.relativedelta import relativedelta


def create_csv_for_predict_market_rate(request):
    try:
        """
        Make CSV for analyzing the data

        """
        # print(request.data)
        order_id = request.data
        order_obj = Order.objects.get(id=order_id)
        if order_obj is not None:
            start_city = order_obj.start_city
            end_city = order_obj.end_city
            vehicle_type_id = order_obj.indent.vehicle_type.id
            current_order_vehicle_req_date = order_obj.indent.date_vehicle_required
            # print('current_order_vehicle_req_date', current_order_vehicle_req_date)
            # print('previous year date', current_order_vehicle_req_date + relativedelta(years=-1))
            ord_obj = Order.objects.filter(
                Q(current_status="Completed")
                & Q(start_city__exact=start_city)
                & Q(end_city__exact=end_city)
                & Q(vehicle_id__vehicle_type_id=vehicle_type_id)
            )
            #  Check historical data here
            order_id_list = list()
            filter_data_obj = []
            dict_history = {}
            for j in ord_obj:
                vehicle_req_date = j.indent.date_vehicle_required
                if (
                    (current_order_vehicle_req_date + relativedelta(years=-1))
                    - vehicle_req_date
                ).days == 0:
                    order_id_list.append(j.id)
                    # print('DATA FOUND')
                    filter_data_obj = Order.objects.filter(
                        Q(current_status="Completed")
                        & Q(start_city__exact=start_city)
                        & Q(end_city__exact=end_city)
                        & Q(vehicle_id__vehicle_type_id=vehicle_type_id)
                        & Q(
                            indent__date_vehicle_required__gte=vehicle_req_date
                            + relativedelta(days=-10)
                        )
                        & Q(
                            indent__date_vehicle_required__lte=vehicle_req_date
                            + relativedelta(days=+10)
                        )
                    )
                    # print(len(filter_data_obj))
                else:
                    pass
            for k in filter_data_obj:
                dict_history[str(k.indent.date_vehicle_required)] = k.broker_rate
                # print('Vehicle Req Date', k.indent.date_vehicle_required, ' Broker Rate', k.broker_rate)
                # print 'DATA NOT FOUND'
            # print(order_id_list)
            # print(dict_history)
            # csv_path = 'media/data.csv'
            # with open(csv_path, 'w') as csvfile:
            #     fieldnames = ['Customer', 'Source', 'Destination', 'Vehicle Required Date', 'Broker Rate']
            #     writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            #     writer.writeheader()
            #     for i in ord_obj:
            #         print 'Customer: ', i.indent.customer.company_name
            #         print 'Vehicle Reqiuired: ', i.indent.date_vehicle_required
            #         print 'Broker Rate: ', i.broker_rate
            #         writer.writerow({'Customer': i.indent.customer.company_name, 'Source': start_city,
            #                          'Destination': end_city, 'Vehicle Required Date': i.indent.date_vehicle_required,
            #                          'Broker Rate': i.broker_rate})
        return Response({"code": status_code.code_200})
    except Exception as e:
        print(e)


@api_view(["GET"])
def create_contract_id(request):
    try:
        broker_matrix_obj = BrokerRateMatrix.objects.all()
        for i in broker_matrix_obj:
            contract_obj = Contract.objects.filter(
                customer_id=i.customer_id,
                start_point=i.start_point,
                destination_point=i.destination_point,
                vehicle_type_id=i.vehicle_type_id,
            ).order_by("-id")
            if contract_obj:
                i.contract_id = contract_obj.first().id
                i.save()
        return Response({"code": status_code.code_200})
    except Exception as e:
        return Response({"code": {"status": 400, "message": e.arsg[0]}})


def get_fuel_price():
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        from urllib.request import urlopen

        from bs4 import BeautifulSoup

        url = "https://economictimes.indiatimes.com/wealth/fuel-price/diesel"
        agent = {
            "User-Agent": "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.115 Safari/537.36"
        }
        import requests

        page = requests.get(url, headers=agent)
        soup = BeautifulSoup(page.content, "lxml")
        all_table = soup.find_all("table")
        for table in all_table:
            try:
                table_name = table.thead.tr.th.contents[0].upper()
                # Famous City wise data
                if table_name == "CITY":
                    keys = list()
                    city_price = list()
                    tr_all = table.find_all("tr")
                    for tr in tr_all:
                        if tr_all.index(tr) == 0:
                            th_all = tr.find_all("th")
                            for th in th_all:
                                keys.append(th.contents[0].upper().replace(" ", "_"))
                        else:
                            dict_price_city = {}
                            for td_all in tr:
                                dict_price_city[keys[tr.index(td_all)]] = td_all.text
                            city_price.append(dict_price_city)
                    # print('City Price: ', city_price)

                # All state wise data
                elif table_name == "STATE":
                    keys = list()
                    state_price = list()
                    tr_all = table.find_all("tr")
                    for tr in tr_all:
                        if tr_all.index(tr) == 0:
                            th_all = tr.find_all("th")
                            for th in th_all:
                                keys.append(th.contents[0].upper().replace(" ", "_"))
                        else:
                            dict_price_city = {}
                            for td_all in tr:
                                dict_price_city[keys[tr.index(td_all)]] = td_all.text
                            state_price.append(dict_price_city)
                    # print('State Price: ', state_price)
            except Exception as e:
                print(e)
    except Exception as e:
        return Response({"code": {"status": 400, "message": e.args[0]}})


# get_fuel_price()
def create_v_demand(data):
    """

    :param data:
    :return:
    """
    data_dict = dict()
    data_dict["origin"] = data.get("origin")["location_name"]
    data_dict["destination"] = data.get("destination")["location_name"]
    data_dict["weight"] = "{}".format(data.get("weight"))
    data_dict["vehicleSize"] = data.get("vehicleSize")["name"]
    data_dict["vehicleSize"] = data.get("vehicleSize")["name"]
    data_dict["customerCode"] = data.get("customerCode")
    data_dict["manifestNumber"] = data.get("manifestNumber")
    data_dict["plannedDate"] = " ".join(
        (data.get("plannedDate"), data.get("plannedTime"))
    )
    return data_dict


class VehicleDemandProxyView(ProxyView):
    """
    vehicle demands proxy view
    """

    upstream = (
        f"{settings.PROXY_VIEW_SCHEME}://" f"{settings.VEHICLE_DEMAND_PROXY_VIEW_HOST}"
    )

    def get_upstream(self, path):
        """
        :returns: upstream for vehicle demands
        """
        self.upstream = f"{self.upstream}{settings.SERVICE_IDENTIFIER}"
        return super(VehicleDemandProxyView, self).get_upstream(path)

    def get_request_headers(self):
        request_headers = super(VehicleDemandProxyView, self).get_request_headers()
        token, token_type = GetJWTToken().get_token()
        request_headers["Authorization"] = token
        return request_headers


@api_view(["GET"])
def get_order_code_from_order_id(request):
    try:
        LOGGER.info(f"get_order_code_from_order_id request - {request.GET}")
        _id = request.GET.get('id')
        vehicle_obj = Order.objects.filter(id=_id).values(
            'order_id').first()
        if not vehicle_obj:
            raise Exception("No Order Found")

        return Response({"code": status_code.code_200, "details": {"order_code": vehicle_obj.get('order_id')}})
    except Exception as e:
        LOGGER.info(f"Order Code form order id Error - {str(e)}")
        return Response({"code": status_code.code_400})



@api_view(
    ["POST",]
)
def float_to_gobolt_business(request):
    with transaction.atomic():
        try:
            user_dict = request.data["buyerDetails"]
        except:
            user_dict = user_define_methods.creating_user_dict(
                request.user.first_name,
                request.user.id,
                phone_no=request.auth.user.usermanagement_set.all(),
            )
        try:
            input_data = request.data
            LOGGER.info(f"float_to_gobolt_business input_data - {input_data}")
            broker_data = input_data["brokerName"]
            account = input_data["account"]
            source_name = input_data.get("source_name", {}).get("name")
            if source_name not in VehicleSourcing.MARKET.value:
                return Response(
                    {"code": {"status": 503, "message": "Please select Vehicle Source"}}
                )

            if Order.objects.get(id=input_data["order_id"]).b_amount is None:
                if reduce(
                        user_define_methods.check_broker_account,
                        [broker_data["id"], account["bank_acc_no"]],
                ):
                    order_id = input_data["order_id"]
                    broker_owner_data = input_data["broker_owner"]

                    if Broker.objects.filter(
                            id=broker_data["id"],
                            is_aprove=True,
                            is_deleted=False,
                            is_malicious=False,
                    ):
                        pass
                    else:
                        raise Exception("Please Activate Broker first.")

                    order_data = Order.objects.select_for_update().get(id=order_id)
                    if order_data.current_status == "Distributed":
                        raise Exception("This order is already distributed in market.")

                    if order_data.float_to_app is True:
                        raise Exception(f"This order has already been assigned to the "
                                        f"vendor - {order_data.broker.broker_name}.")

                    if not order_data.float_to_market:
                        raise Exception("You have enter wrong data. Please Refresh your page.")

                    if order_data.current_status == "Planning Initiated" and order_data.order_assigned is True and \
                        order_data.trip_assigned_source is True:
                        raise Exception("Planning of trip is already initiated.")

                    if order_data.is_approve:
                        raise Exception("Vehicle already approved.")

                    trip_obj = Trip.objects.filter(order_id=order_id)
                    t_status = trip_obj[0].trip_status
                    if t_status.get('status_name') in ["Completed", "In Transit", "Vehicle Reported", "Deleted"]:
                        raise Exception(f"Trip status already beyond planning ({t_status.get('status_name')}).")

                    if order_data is not None and trip_obj is not None:
                        order_data.broker_id = broker_data["id"]
                        order_data.broker_rate = broker_owner_data["broker_owner_rate"]
                        # order_data.broker_advance = broker_owner_data[
                        #     "broker_owner_advance"
                        # ]
                        order_data.broker_advance_cash = 0
                        order_data.sourcing_user = user_dict
                        order_data.broker_account = account
                        order_data.deductions = broker_owner_data.get("deductions", 0)
                        order_data.tds = input_data["tds"]
                        # order_data.current_status = "Planning Initiated"
                        order_data.source_name = source_name.upper()
                        order_data.float_to_app = True
                        order_data.save()

                        vehicle_req_datetime = datetime.combine(
                            order_data.indent.date_vehicle_required,
                            order_data.indent.time_vehicle_required,
                        )
                        _tds = float(order_data.tds) if order_data.tds else 0
                        _broker_rate = float(order_data.broker_rate) if order_data.broker_rate else 0
                        GoboltBusinessApp.objects.using("gobolt_business").create(
                            broker_id=broker_data["id"],
                            broker_code=broker_data["broker_code"],
                            broker_name=broker_data["name"].get("first_name"),
                            order_code=order_data.order_code,
                            order_id=order_data.id,
                            origin_name=order_data.indent.start_point['location_name'],
                            origin_code=order_data.indent.start_point['location_code'],
                            destination_name=order_data.indent.destination_point['location_name'],
                            destination_code=order_data.indent.destination_point['location_code'],
                            customer_name=order_data.indent.customer.customer_name,
                            customer_code=order_data.indent.customer.customer_code,
                            status="PENDING",
                            order_status=order_data.current_status,
                            payment_status=order_data.payment_status,
                            broker_rate=_broker_rate,
                            tat=order_data.indent.contract.tat,
                            total_distance=order_data.indent.contract.lane_distance,
                            vehicle_required_datetime=vehicle_req_datetime,
                            vehicle_type=order_data.indent.vehicle_type.name,
                            created_by=request.user.username,
                            created_at=datetime.now(),
                            updated_by=request.user.username,
                            updated_at=datetime.now(),
                            ops_assigned_vehicle=False,
                            tds=round_decimals_up((_broker_rate * _tds)/100),
                            trip_id=str(trip_obj[0].id),
                            pod_status=trip_obj[0].pod_data.get("pod_status")
                        )


                        if user_define_methods.update_market_vahicle_trip_data(order_data, broker=True) is True:
                            LOGGER.info("Broker Data Successfully Updated")

                            try:
                                IndentOrderLogs.objects.create(
                                    action="float_to_gobolt_business_app_request",
                                    user={"name": broker_data["name"].get("first_name"), "code": broker_data["broker_code"]},
                                    timestamp=timezone.now(),
                                    order_id_id=order_data.id,
                                    indent_id=order_data.indent.id,
                                )
                            except Exception as e:
                                LOGGER.info(f"Exception is - {str(e)}", exc_info=True)
                        else:
                            print("Data is not updated")



                        return Response({"code": status_code.code_200})
                    else:
                        return Response({"code": status_code.code_502})
                else:
                    return Response(
                        {
                            "code": {
                                "code": 300,
                                "message": "Please enter valid broker account",
                            }
                        }
                    )
            else:
                return Response(
                    {"code": {"code": 300, "message": "Already send for Bid."}}
                )
        except Exception as e:
            LOGGER.info(f"Exception float_to_gobolt_business- {str(e)}", exc_info=True)
            return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["PUT"])
def float_to_gobolt_business_edit(request):
    with transaction.atomic():
        try:
            user_dict = request.data["buyerDetails"]
        except:
            user_dict = user_define_methods.creating_user_dict(
                request.user.first_name,
                request.user.id,
                phone_no=request.auth.user.usermanagement_set.all(),
            )
        try:
            edit_data = request.data
            LOGGER.info(f"float_to_gobolt_business edit_data - {edit_data}")
            broker_data = edit_data["brokerName"]
            account = edit_data["account"]
            source_name = edit_data.get("source_name", {}).get("name")
            if source_name not in VehicleSourcing.MARKET.value:
                return Response(
                    {"code": {"status": 503, "message": "Please select Vehicle Source"}}
                )

            if Order.objects.get(id=edit_data["order_id"]).b_amount is None:
                if reduce(
                        user_define_methods.check_broker_account,
                        [broker_data["id"], account["bank_acc_no"]],
                ):
                    order_id = edit_data["order_id"]
                    broker_owner_data = edit_data["broker_owner"]

                    if Broker.objects.filter(
                            id=broker_data["id"],
                            is_aprove=True,
                            is_deleted=False,
                            is_malicious=False,
                    ):
                        pass
                    else:
                        raise Exception("Please Activate Broker first.")

                    order_data = Order.objects.select_for_update().get(id=order_id)
                    if order_data.current_status == "Distributed":
                        raise Exception("This order is already distributed in market.")

                    if not order_data.float_to_market:
                        raise Exception("You have enter wrong data. Please Refresh your page.")

                    if order_data.is_approve:
                        raise Exception("Vehicle already approved.")

                    trip_obj = Trip.objects.filter(order_id=order_id)
                    t_status = trip_obj[0].trip_status
                    if t_status.get('status_name') in ["Completed", "In Transit", "Vehicle Reported", "Deleted"]:
                        raise Exception(f"Trip status already beyond planning ({t_status.get('status_name')}).")

                    if order_data is not None and trip_obj is not None:
                        order_data.broker_rate = broker_owner_data["broker_owner_rate"]
                        order_data.save()

                        _tds = float(order_data.tds) if order_data.tds else 0
                        _broker_rate = float(order_data.broker_rate) if order_data.broker_rate else 0
                        _tds = float(round_decimals_up((_broker_rate * _tds) / 100))
                        gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                            order_code=order_data.order_code
                        ).exclude(status="CANCELED").first()
                        if gobolt_app_obj:
                            gobolt_app_obj.broker_rate = _broker_rate
                            gobolt_app_obj.updated_by = request.user.username
                            gobolt_app_obj.updated_at = datetime.now()
                            gobolt_app_obj.tds = _tds
                            gobolt_app_obj.save()
                        else:
                            raise Exception("No gobolt business app record found")

                        if user_define_methods.update_market_vahicle_trip_data(order_data, broker=True) is True:
                            LOGGER.info("Broker Data Successfully Updated")

                            try:
                                IndentOrderLogs.objects.create(
                                    action="float_to_gobolt_business_app_edit_request",
                                    user={"name": broker_data["name"].get("first_name"),
                                          "code": broker_data["broker_code"]},
                                    timestamp=timezone.now(),
                                    order_id_id=order_data.id,
                                    indent_id=order_data.indent.id,
                                )
                            except Exception as e:
                                LOGGER.info(f"Exception is - {str(e)}", exc_info=True)
                        else:
                            print("Data is not updated")

                        return Response({"code": status_code.code_200})
                    else:
                        return Response({"code": status_code.code_502})
                else:
                    return Response(
                        {
                            "code": {
                                "code": 300,
                                "message": "Please enter valid broker account",
                            }
                        }
                    )
            else:
                return Response(
                    {"code": {"code": 300, "message": "Already send for Bid."}}
                )
        except Exception as e:
            LOGGER.info(f"Exception float_to_gobolt_business_edit- {str(e)}", exc_info=True)
            return Response({"code": {"status": 300, "message": e.args[0]}})

@api_view(
    ["POST",]
)
def unassign_broker(request):
    try:
        input_data = request.data
        LOGGER.info(f"unassign_broker input_data - {input_data}")
        order_code = input_data.get("order_code")
        if not order_code:
            raise Exception("Order code is mandatory")
        broker_name = input_data.get("broker_name")
        if not broker_name:
            raise Exception("Broker name is mandatory")
        broker_code = input_data.get("broker_code")
        if not broker_code:
            raise Exception("Broker code is mandatory")
        rejected_reason = input_data.get("rejected_reason")
        if not rejected_reason:
            raise Exception("Rejected reason is mandatory")
        order_obj = Order.objects.get(order_code=order_code)
        if order_obj:
            trip = pymongo_crud.db['trip']
            query = {"order_id": int(order_obj.id)}
            trip_obj = trip.find_one(query)
            if not trip_obj:
                raise Exception("Trip not found")
            # if order_obj.current_status != "Planning Initiated":
            #     raise Exception(f"Can't unassign broker, current status is ({order_obj.current_status}).")
            if order_obj.float_to_app is False:
                raise Exception("This order has already been un-assigned to the broker.")
            order_code = order_obj.order_code
            vehicle_no = trip_obj.get("order_data", {}).get("vehicle_no")

            # check advance recovery
            from finance.models import TripAdvanceRecovery

            if TripAdvanceRecovery.objects.filter(
                    recent_trip_order_code=order_code).exists() or TripAdvanceRecovery.objects.filter(
                recovery_order_code=order_code).exists():
                raise Exception("Trip Advance already Done. You can not reject this trip.")

            if vehicle_no:
                # calling fuel module
                fuel_obj = FuelPump(order_code=order_code, vehicle_number=vehicle_no)
                fuel_result = fuel_obj.get_fuel_pump_data()
                if len(fuel_result.get('details', [])):
                    raise Exception("There are Fuel Entries against the trip in Fuel Pump  Module delete them First")

                allowed_status = ["PAID", "PENDING", "RECONCILED"]
                data = {
                    "vehicle_number": vehicle_no,
                    "order_code": order_code,
                    "status": allowed_status
                }
                result = PaymentsModules(
                    "dummy").payments_module_status_data_call(data)
                if result.get("details", {}).get("error"):
                    raise Exception(f"Trip {order_obj.order_id} can not be cancelled, Invalid {result.get('details').get('error')} data sent to payments module for fetching Trip data.")
                elif result.get("details", {}).get("count"):
                    raise Exception("There are Payment Entries against the trip in Payments Module delete them First.")

                restrict_status = ["OPEN", "IN_PROGRESS", "AWAITING_APPROVAL", "PAYMENT_REJECTED", "PAYMENT_RAISED"]
                data["status"] = restrict_status
                result = OpsMaintenance().check_maintenance_requests(data)
                if result.get("details", False) is True:
                    raise Exception(
                        "There are maintenance requests against the trip in Maintenance Module delete them First."
                    )

                if trip_obj.get("payment_status").get("payment_status_name") == "Advance Pending":
                    raise Exception("Broker advance is already approved.")

            if EntityPayments.objects.filter(
                    order_code=order_obj.order_id, id_disable=False
            ).exists():
                raise Exception(f"Trip {order_obj.order_id} can not be cancelled because There are payment entries against the trip.")

            if order_obj.finance_data_paid["broker_paid"]["broker_advance_paid"] != "":
                raise Exception("Broker payment done.")

            if order_obj.finance_data_paid["driver_paid"]["driver_advance_paid"] != "":
                raise Exception("Broker Cash Adavnce payment done.")

            user_define_methods.trip_rejected_by_broker_clear_trip_data(order_obj, trip_obj, trip)

            GoboltBusinessRejectedOrders.objects.create(
                broker_name=broker_name,
                broker_code=broker_code,
                rejected_reason=rejected_reason,
                rejected_at=datetime.now(),
                order=order_obj
            )
            try:
                IndentOrderLogs.objects.create(
                    indent_id=order_obj.indent.id,
                    order_id_id=order_obj.id,
                    action="trip_rejected_by_broker",
                    user={"name": broker_name, "code": broker_code},
                    timestamp=timezone.now(),
                    action_data=input_data,
                )
            except Exception as e:
                LOGGER.info(f"Exception is - {str(e)}", exc_info=True)

        else:
            raise Exception("Order not found")
        try:
            gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                order_code=order_code
            ).exclude(status="CANCELED").first()
            if gobolt_app_obj:
                gobolt_app_obj.status = "CANCELED"
                gobolt_app_obj.remarks = rejected_reason
                gobolt_app_obj.canceled_at = datetime.now(timezone.utc).isoformat()
                gobolt_app_obj.canceled_by = input_data.get("canceled_by")
                gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while bololt business cancel request - {str(e)}", exc_info=True)
            raise Exception("Can't update gobolt business app details")

        return Response({"code": status_code.code_200})
    except Exception as e:
        LOGGER.info(f"Exception unassign_broker - {str(e)}", exc_info=True)
        return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(
    ["POST",]
)
def broker_vehicle_assign(request):
    with transaction.atomic():
        try:
            input_data = request.data
            LOGGER.info(f"broker_vehicle_assign input_data - {input_data}")
            order_code = input_data.get("order_code")
            if not order_code:
                raise Exception("Order code is mandatory")
            broker_code = input_data.get("broker_code")
            if not broker_code:
                raise Exception("Broker code is mandatory")
            broker_name = input_data.get("broker_name")
            if not broker_name:
                raise Exception("Broker name is mandatory")
            vehicle_number = input_data.get("vehicle_number")
            if not vehicle_number:
                raise Exception("Vehicle number is mandatory")
            driver_code = input_data.get("driver_code")
            if not driver_code:
                raise Exception("Driver code is mandatory")
            driver_name = input_data.get("driver_name")
            driver_mobile_number = input_data.get("driver_mobile_number")

            broker_obj = Broker.objects.filter(
                broker_code=broker_code,
                is_aprove=True,
                is_deleted=False,
                is_malicious=False,
            )
            if not broker_obj:
                raise Exception("Please Activate Broker first.")
            broker_obj = broker_obj.first()

            order_obj = Order.objects.select_for_update(nowait=True).get(order_code=order_code)
            if order_obj:
                if order_obj.current_status == "Distributed":
                    raise Exception("This order is already distributed in market.")

                if order_obj.is_review is True:
                    raise Exception("Vehicle is reviewed.")

                if not order_obj.float_to_market:
                    raise Exception("You have enter wrong data. Please Refresh your page.")

                if order_obj.is_approve:
                    if order_obj.vehicle_id:
                        veh_obj = Vehicles.objects.get(id=order_obj.vehicle_id)
                        if veh_obj:
                            veh_obj.vehicle_allocation_status = False
                            veh_obj.save()
                    if order_obj.driver_id:
                        dri_obj = Driver.objects.get(id=order_obj.driver_id)
                        if dri_obj:
                            dri_obj.allocation_status = False
                            dri_obj.save()

                    try:
                        order_code = order_obj.order_code
                        vehicle_no = order_obj.vehicle.vehicle_registration_number

                        # check advance recovery
                        from finance.models import TripAdvanceRecovery

                        if TripAdvanceRecovery.objects.filter(
                                recent_trip_order_code=order_code).exists() or TripAdvanceRecovery.objects.filter(
                            recovery_order_code=order_code).exists():
                            raise Exception(f"Trip Advance already Done. You can not reject this trip.")

                        # calling fuel module
                        fuel_obj = FuelPump(order_code=order_code, vehicle_number=vehicle_no)
                        fuel_result = fuel_obj.get_fuel_pump_data()
                        if len(fuel_result.get('details', [])):
                            raise Exception(f"There are Fuel Entries against the trip in Fuel Pump  Module delete them First")

                        allowed_status = ["PAID", "PENDING", "RECONCILED"]
                        data = {
                            "vehicle_number": vehicle_no,
                            "order_code": order_code,
                            "status": allowed_status
                        }
                        result = PaymentsModules(
                            "dummy").payments_module_status_data_call(data)
                        if result.get("details", {}).get("error"):
                            raise Exception(f"Trip {order_obj.order_id} can not be cancelled, "
                                            f"Invalid {result.get('details').get('error')} "
                                            f"data sent to payments module for fetching Trip data.")

                        elif result.get("details", {}).get("count"):
                            raise Exception("There are Payment Entries against the trip in "
                                            "Payments Module delete them First.")

                        restrict_status = ["OPEN", "IN_PROGRESS", "AWAITING_APPROVAL", "PAYMENT_REJECTED",
                                           "PAYMENT_RAISED"]
                        data["status"] = restrict_status
                        result = OpsMaintenance().check_maintenance_requests(data)
                        if result.get("details", False) is True:
                            raise Exception(
                                "There are maintenance requests against the trip in "
                                "Maintenance Module delete them First."
                            )

                    except Exception as e:
                        LOGGER.info(f"Exception whiel reject trip - {str(e)}", exc_info=True)
                        return Response({"code": {"status": 300, "message": str(e)}})

                trip = pymongo_crud.db['trip']
                query = {"order_id": int(order_obj.id)}
                trip_obj = trip.find_one(query)
                if trip_obj:
                    if trip_obj['trip_status']['status_name'] in ["Completed", "In Transit", "Vehicle Reported",
                                                                  "Deleted"]:
                        raise Exception(f"Trip status already beyond planning ({trip_obj['trip_status']['status_name']}).")
                    if trip_obj['payment_status']["payment_status_name"] != "PENDING_FOR_ADV_APPROVAL":
                        raise Exception(f"Trip advance is already booked")

                    vehicle_obj = Vehicles.objects.filter(vehicle_registration_number=vehicle_number)
                    if not vehicle_obj:
                        raise Exception("Vehicle not found")
                    vehicle_obj = vehicle_obj.first()
                    if trip_obj['trip_status']['status_name'] == "Vehicle Placed":
                        if vahicle_placement_check(vehicle_number, order_obj.id):
                            raise Exception("Vehicle was already assigned on another running trip")
                    check_same_day_condn = input_data.get("check_same_day_condn")
                    if check_same_day_condn is False:
                        ord_obj_chk = Order.objects.filter(
                            indent__date_vehicle_required=order_obj.indent.date_vehicle_required,
                            vehicle_id=vehicle_obj.id,
                        )
                        if len(ord_obj_chk):
                            if (
                                ord_obj_chk.first().cancellation_type
                                is not None
                                or order_obj.id == ord_obj_chk[0].id
                            ):
                                pass
                            else:
                                raise Exception("Same vehicle can not be placed twice in a day.")
                    vehicle_data_id = vehicle_obj.id
                    vehicle_rel_broker = VehicleBrokerRel.objects.filter(
                        vehicle_id=vehicle_data_id,
                    )
                    if vehicle_rel_broker.exists():
                        vehicle_rel_broker.delete()
                    VehicleBrokerRel.objects.create(
                        vehicle_id=vehicle_data_id,
                        broker_id=broker_obj.id,
                    )
                    driver_obj = Driver.objects.filter(driver_code=driver_code).first()
                    if not driver_obj:
                        raise Exception("Driver not found")
                    license_expiry_date = driver_obj.license_expiry_date
                    today = date_module.today()
                    if license_expiry_date <= today:
                        raise Exception("Driver's license is expired.")

                    driver_data_id = driver_obj.id

                    order_obj.vehicle_id = vehicle_data_id
                    order_obj.driver_id = driver_data_id
                    order_obj.is_approve = True
                    order_obj.is_review = False
                    order_obj.order_assigned = True
                    order_obj.payment_status = "PENDING_FOR_ADV_APPROVAL"
                    order_obj.is_broker_trip_flow = True
                    order_obj.save()
                    driver_obj.allocation_status = True
                    driver_obj.save()
                    vehicle_obj.vehicle_allocation_status = True
                    assigned_driver_data = dict(
                        id=driver_obj.id,
                        driver_code=driver_obj.driver_code,
                        driver_name=driver_obj.driver_name,
                        mobile_number=driver_obj.mobile_number,
                    )
                    vehicle_obj.driver_data = assigned_driver_data
                    vehicle_obj.save()
                    try:
                        views.single_location_veh_customer_index(order=order_obj)
                        views.single_location_bro_customer_index(order=order_obj)
                    except Exception as e:
                        LOGGER.info(f"Exception while create index - {str(e)}", exc_info=True)

                    if user_define_methods.update_market_vahicle_trip_data(order_obj, vehicle=True) is True:
                        LOGGER.info("All Data Successfully Updated")

                        try:
                            IndentOrderLogs.objects.create(
                                action="vehicle_assigned_by_gobolt_business_app",
                                user={"name": broker_name, "code": broker_code},
                                timestamp=timezone.now(),
                                order_id_id=order_obj.id,
                                indent_id=order_obj.indent_id,
                            )
                        except Exception as e:
                            LOGGER.info(f"Exception while assign vehicle - {str(e)}")
                    else:
                        LOGGER.info("Data not updated")
                else:
                    raise Exception("Trip Not Found")
            else:
                raise Exception("Order Not Found")
            try:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.vehicle_number = vehicle_number
                    gobolt_app_obj.vehicle_code = input_data.get("vehicle_code")
                    gobolt_app_obj.driver_code = driver_code
                    gobolt_app_obj.driver_name = driver_name
                    gobolt_app_obj.driver_mobile_number = driver_mobile_number
                    gobolt_app_obj.order_status = "Vehicle Assigned"
                    gobolt_app_obj.trip_status = "Planned"
                    gobolt_app_obj.payment_status = "PENDING_FOR_ADV_APPROVAL"
                    gobolt_app_obj.updated_at = datetime.now(timezone.utc).isoformat()
                    gobolt_app_obj.updated_by = input_data.get("updated_by")
                    gobolt_app_obj.save()
            except Exception as e:
                LOGGER.info(f"Exception while bololt business cancel request - {str(e)}", exc_info=True)
                raise Exception("Can't update gobolt business app details")

            return Response({"code": status_code.code_200})
        except Exception as e:
            LOGGER.info(f"Exception broker_vehicle_assign - {str(e)}", exc_info=True)
            return Response({"code": {"status": 300, "message": e.args[0]}})


@api_view(["POST"])
def gobolt_business_delete_documents(request):
    try:
        input_data = request.data
        LOGGER.info(f"gobolt_business_delete_documents input_data - {input_data}")
        trip_request_id = input_data.get("trip_request_id")
        document_name = input_data.get("document_name")
        _uuid = input_data.get("uuid")
        vehicle_number = input_data.get("vehicle_number")
        if trip_request_id:
            gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                id=trip_request_id
            ).first()
            if gobolt_app_obj:
                trip = pymongo_crud.db['trip']
                LOGGER.info(f"gobolt_app_obj.order_id ------ {gobolt_app_obj.order_id}")
                query = {"order_id": gobolt_app_obj.order_id}
                trip_obj = trip.find_one(query)
                if not trip_obj:
                    raise Exception("Trip not found")
                set_dict = {}

                if document_name == "invoice":
                    doc = GoboltBusinessTripDocuments.objects.using("gobolt_business").filter(
                        trip_id=gobolt_app_obj.id,
                        document_name=document_name,
                        uuid=_uuid
                    )
                    if doc:
                        doc.delete()
                        invoice_pic = trip_obj["invoice_pic"]
                        invoice_pic["front_invoice"] = ""
                        invoice_pic["invoice_number"] = ""
                        set_dict["invoice_pic"] = invoice_pic

                elif document_name == "invoice_back":
                    doc = GoboltBusinessTripDocuments.objects.using("gobolt_business").filter(
                        trip_id=gobolt_app_obj.id,
                        document_name=document_name,
                        uuid=_uuid
                    )
                    if doc:
                        doc.delete()
                        invoice_pic = trip_obj["invoice_pic"]
                        invoice_pic["back_invoice"] = ""
                        set_dict["invoice_pic"] = invoice_pic

                elif document_name == "e_way_bill":
                    doc = GoboltBusinessTripDocuments.objects.using("gobolt_business").filter(
                        trip_id=gobolt_app_obj.id,
                        document_name=document_name,
                        uuid=_uuid
                    )
                    if doc:
                        doc.delete()
                        set_dict["e_way_bill"] = {"e_way_bill_number": "", "pic_url": ""}

                elif document_name == "lr_number":
                    doc = GoboltBusinessTripDocuments.objects.using("gobolt_business").filter(
                        trip_id=gobolt_app_obj.id,
                        document_name=document_name,
                        uuid=_uuid
                    )
                    if doc:
                        doc.delete()
                        lr_data = trip_obj["lr"]["lr_data"]
                        LOGGER.info(f"lr_data -------- {lr_data}")
                        for x in lr_data:
                            if x.get("gobolt_business_app") is True:
                                if len(lr_data) > 1:
                                    lr_data.remove(x)
                                else:
                                    x["lr_reciept"] = ""
                                    x["pic_url"] = ""
                                    x.pop("gobolt_business_app")
                                set_dict["lr"] = {"lr_data": lr_data}
                LOGGER.info(f"gobolt_business_delete_documents set_dict - {set_dict}")
                if set_dict:
                    set_values = {"$set": set_dict}
                    trip.update(query, set_values)
        elif vehicle_number:
            pass

        return Response({"code": status_code.code_200})
    except Exception as e:
        LOGGER.info(f"Exception gobolt_business_delete_documents - {str(e)}", exc_info=True)
        return Response({"code": {"status": 300, "message": e.args[0]}})