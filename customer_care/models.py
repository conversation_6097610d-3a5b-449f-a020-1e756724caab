from __future__ import unicode_literals

from django.contrib.auth.models import User
from django.db import models
from django.utils.translation import ugettext_lazy as _
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from registration.models import Driver
from broker.models import <PERSON>roker, BrokerAccount
from vehicle_owner.models import VehicleOwner
from administrator.models import UserManagement, HubManagement
from vehicle.models import VehicleType, Vehicles
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db.models import Q
import uuid
import datetime
from django.utils import timezone
import numpy as np
from time import time


# Create your models here.
class OrderStatusMaster(models.Model):
    name = models.CharField(max_length=400, null=True)
    is_delete = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)


# Ashish
# class VehicleType(models.Model):
#     name = models.CharField(max_length=100, default="", null=True)
#     is_delete = models.BooleanField(default=False)


class Customer(models.Model):
    customer_name = models.CharField(max_length=40, null=True)
    company_name = models.CharField(max_length=40)
    customer_code = models.CharField(max_length=20)
    phone_number = models.CharField(max_length=11, default=0)
    user = models.OneToOneField(User, default="", on_delete=models.CASCADE)
    address = models.TextField(null=True)
    account_manager = models.ForeignKey(
        UserManagement,
        default="",
        related_name="account_manager",
        null=True,
        on_delete=models.CASCADE,
    )
    bilty_boy = models.ForeignKey(
        UserManagement,
        default="",
        related_name="bilty_boy",
        null=True,
        on_delete=models.CASCADE,
    )
    sourcing_agent = models.ForeignKey(
        UserManagement,
        related_name="sourcing_agent",
        null=True,
        on_delete=models.CASCADE,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    is_delete = models.BooleanField(default=False)
    sac = models.CharField(max_length=7, default=0)
    created_by = models.CharField(max_length=300, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.CharField(max_length=300, null=True, blank=True)

    @classmethod
    def get_c_b_code(cls, code):
        return cls.objects.get(customer_code=code)

    def __str__(self):
        return self.customer_code


####################################################################################
# Points need to discuss according status                                          #
# Created Date                                                                     #
####################################################################################
def loading_dict():
    return list()


class Contract(models.Model):
    contract_id = models.CharField(max_length=100, default="")
    remarks = models.CharField(max_length=1000, default="")
    customer = models.ForeignKey(
        Customer, blank=True, null=True, on_delete=models.CASCADE
    )
    start_point = JSONField(null=True)
    destination_point = JSONField(null=True)
    vehicle_type = models.ForeignKey(VehicleType, null=True, on_delete=models.CASCADE)
    customer_detention_rate = models.CharField(
        max_length=100, null=True
    )  # Rate for route per day
    broker_detention_rate = models.CharField(
        max_length=100, null=True
    )  # Rate for route per day
    contract_rate = models.FloatField(null=True, validators=[MinValueValidator(0.0)])
    tat = models.CharField(max_length=1000, null=True)
    loading_charges = models.FloatField(null=True)
    billing_cycle = models.CharField(max_length=20, null=True)
    unloading_charges = models.FloatField(null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    created_by = models.CharField(max_length=300, null=True)
    month = models.CharField(max_length=100, null=True)
    year = models.CharField(max_length=100, null=True)
    is_express = models.BooleanField(default=False)
    is_surcharge = models.BooleanField(default=False)
    surcharge_rate = models.FloatField(default=0.0)
    is_hazardous = models.BooleanField(default=False)
    cus_deten3 = models.CharField(max_length=100, null=True)
    cus_deten10 = models.CharField(max_length=100, null=True)
    cus_detenN = models.CharField(max_length=100, null=True)
    cus_deten_first = models.IntegerField(default=0)
    cus_deten_second = models.IntegerField(default=0)
    cus_deten_third = models.IntegerField(default=0)
    broker_rate = models.IntegerField(default=0)
    multiple_stopage = JSONField(null=True, default=None)
    loading_points = JSONField(default=loading_dict, null=True)
    lane_distance = models.FloatField(null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    updated_by = models.CharField(max_length=300, null=True)
    is_active = models.BooleanField(default=True)


class ContractType(models.Model):
    created_at = models.DateTimeField(auto_now=True)
    active = models.BooleanField(default=True)
    type = models.CharField(max_length=100)

    def __str__(self):
        return self.type


class FuelSurchargeRate(models.Model):
    created_at = models.DateTimeField(auto_now=True)
    created_by = models.CharField(max_length=300)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    used = models.BooleanField(default=False)
    rate = models.FloatField(default=0.0)
    contract_code = models.CharField(max_length=100, default="")


####################################################################################
# Monthly contract rate.                                                           #
# Created Date: 06/03/17                                                           #
####################################################################################


class ContractByMonth(models.Model):
    contract_obj = models.ForeignKey(Contract, null=True, on_delete=models.CASCADE)
    new_contract_rate = models.CharField(max_length=100, null=True)
    month = models.CharField(max_length=20, null=True)  # Need to discuss
    year = models.CharField(max_length=20, null=True)
    created_at = models.DateTimeField(auto_now=True, null=True)
    is_edited = models.BooleanField(default=False)

    class Meta:
        ordering = ["created_at"]


####################################################################################
# Points need to discuss according status                                          #
# Created Date                                                                     #
####################################################################################
def user_d():
    return {"user_id": "", "username": "", "snap_shot_url": ""}


def advance_pay_broker_d():
    return {
        "broker": {"own_ref": "", "bank_ref": ""},
        "driver": {"own_ref": "", "bank_ref": ""},
    }


def advance_pay_own_d():
    return {
        "fuel": {"own_ref": "", "bank_ref": ""},
        "own": {"own_ref": "", "bank_ref": ""},
    }


def finance_data_paid_d():
    return {
        "broker_paid": {
            "payment_ref": "",
            "payment_type": "",
            "broker_advance_paid": "",
            "bank": "",
            "fuel_advance_paid": "",
        },
        "driver_paid": {
            "payment_ref": "",
            "payment_type": "",
            "driver_advance_paid": "",
        },
    }


def co_driver_d():
    return {
        "driver_code": "",
        "allocation_status": "",
        "driver_name": "",
        "id": "",
        "mobile_number": "",
    }


class Order(models.Model):
    order_id = models.CharField(max_length=50, default="")
    source_name = models.CharField(max_length=20, null=True)
    current_status = models.CharField(max_length=40, null=True)
    total_payment = models.CharField(max_length=50, default="")
    payment_made = models.CharField(max_length=50, default="")
    payment_balance = models.CharField(max_length=50, default="")
    detention = models.CharField(max_length=50, default="")
    taxes = models.CharField(max_length=50, default="")
    vehicle_reporting_date = models.DateField(null=True)
    vehicle_reporting_time = models.TimeField(null=True)
    tracking_link = models.CharField(max_length=50, default="")
    order_status_link = models.CharField(max_length=50, default="")
    ref_no_legacy = models.CharField(max_length=200, unique=True, null=True, blank=True)
    advance_pay_status = models.CharField(max_length=200, null=True, blank=True)
    bank_name = models.CharField(max_length=50, null=True, blank=True)
    # broker_advance_payment_status = models.CharField(max_length=100, null=True)
    # This for Market
    vehicle_owner = models.ForeignKey(VehicleOwner, null=True, on_delete=models.CASCADE)
    driver = models.ForeignKey(Driver, null=True, on_delete=models.CASCADE)
    broker = models.ForeignKey(Broker, null=True, on_delete=models.CASCADE)
    vehicle = models.ForeignKey(Vehicles, null=True, on_delete=models.CASCADE)
    broker_rate = models.IntegerField(null=True)
    broker_advance_cash = models.CharField(max_length=300, null=True)
    broker_advance = models.CharField(max_length=50, null=True)
    broker_advance_in_percentage = models.CharField(max_length=50, null=True)
    broker_detention = models.CharField(max_length=1000, null=True)
    indent = models.ForeignKey(
        "customer_care.Indent",
        verbose_name=_("indent_reference"),
        blank=True,
        null=True,
        default="",
        on_delete=models.CASCADE,
    )
    created_date_time = models.DateTimeField(auto_now_add=True)
    assign_date_time = models.DateTimeField(auto_now=True)
    float_to_market = models.BooleanField(default=False)
    order_assigned = models.BooleanField(default=False)
    trip_assign = models.BooleanField(default=False)
    trip_assigned_source = models.BooleanField(default=False)
    start_city = models.CharField(max_length=1000, null=True)
    end_city = models.CharField(max_length=1000, null=True)
    trip_started = models.BooleanField(default=False)
    is_approve = models.BooleanField(default=False)
    is_review = models.BooleanField(default=False)
    # Added hubs in order
    hubs = JSONField(null=True)
    # Multiple city due to multiple stop
    multiple_city = JSONField(null=True, default=dict)
    order_code = models.CharField(max_length=100, null=True)
    broker_account = JSONField(null=True)
    planning_user = JSONField(null=True, default=user_d)
    snap_shot_finance = JSONField(null=True, default=user_d)
    finance_user = JSONField(null=True, default=user_d)
    sourcing_user = JSONField(null=True, default=user_d)
    hub_user = JSONField(null=True, default=user_d)
    comment = models.TextField(null=True, max_length=4000)
    bilty_boy = JSONField(default=user_d)
    account_manager = JSONField(default=user_d)
    deductions = models.IntegerField(null=True)
    # Vishnu
    finance_data_paid = JSONField(null=True, default=finance_data_paid_d)
    # Ashish
    advance_pay_broker = JSONField(null=True, default=advance_pay_broker_d)
    advance_pay_own = JSONField(null=True, default=advance_pay_own_d)
    cancellation_type = models.TextField(null=True, max_length=50)
    cancellation_date = models.DateTimeField(auto_now=True)
    tds = models.FloatField(null=True)
    co_driver = JSONField(null=True, default=co_driver_d)
    bid_done = models.BooleanField(default=False)
    b_amount = models.FloatField(null=True)
    indent_type = models.CharField(max_length=50, default="Scheduled")
    invalid = models.BooleanField(default=False)
    demand_reference_id = models.CharField(max_length=255, null=True)
    updated_date_time = models.DateTimeField(auto_now=True, null=True, blank=True)
    payment_status = models.CharField(max_length=255, null=True)
    fuel_advance = models.FloatField(null=True)
    fuel_advance_percentage = models.FloatField(null=True)
    previous_trip_deduction = models.FloatField(null=True, default=0)
    used_amount_p_trip_deduction = models.FloatField(null=True, default=0)
    is_broker_trip_flow = models.BooleanField(default=False)
    bulk_payment_request_by = models.CharField(max_length=255, null=True)
    bulk_payment_request_at = models.DateTimeField(null=True)
    is_decline = models.BooleanField(default=False)
    float_to_app = models.BooleanField(default=False)
    ops_assigned_vehicle = models.BooleanField(default=False)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_by = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        ordering = ("-indent__date_vehicle_required", "-indent__time_vehicle_required")
        models.UniqueConstraint(
            fields=["order_id", "indent.customer.customer_code", "demand_reference_id"],
            name="idx_unique_demand_reference_id",
        )
        indexes = [
            models.Index(fields=["current_status"], name="current_statusidx",),
            models.Index(
                fields=["current_status", "indent"], name="current_status_indent_idx",
            ),
            models.Index(
                fields=["order_id", "is_broker_trip_flow"], name="order_id_broker_trip_idx",
            ),
        ]

        permissions = (
            ("can_pay_broker_advance", "Can pay broker advance"),
            ("can_pay_broker_balance", "Can pay broker balance"),
        )

    @classmethod
    def get_order_indent(cls, default=[], **key):
        try:
            return cls.objects.filter(**key)
        except cls.DoesNotExist:
            return default

    @classmethod
    def get_order_status(cls, key, default=None):
        try:
            return cls.objects.get(id=key).current_status
        except cls.DoesNotExist:
            return default

    @classmethod
    def return_broker_order(cls, **kwargs):
        default = list()
        if kwargs.get("id") is not None:
            x = iter(Order.objects.filter(broker_id=74))
            while 1:
                try:
                    z = next(x)
                    default.append(
                        {
                            "order_id": z.order_id,
                            "start_point": z.start_city,
                            "destintion_city": z.end_city,
                            "vehicle_number": z.vehicle.vehicle_registration_number,
                            "broker_advance": z.broker_advance,
                            "broker_advance_cash": z.broker_advance_cash,
                        }
                    )
                except StopIteration:
                    break
        return default

    def mean_frequency(self):
        print("Do Operation here")

    @classmethod
    def getting_origin_destination(cls, **kwargs):
        return cls.objects.filter(
            float_to_market=True, current_status="Completed"
        ).values("start_city", "end_city", "indent__vehicle_type")

    @classmethod
    def getting_origin_destination_customer(cls, **kwargs):
        return cls.objects.filter(
            float_to_market=True, current_status="Completed"
        ).values("start_city", "end_city", "indent__vehicle_type", "indent__customer")

    @classmethod
    def _o_objs(cls, **kwargs):
        return cls.objects.filter(**kwargs)

        # @classmethod
        # de


class Favorite(models.Model):
    customer = models.ForeignKey(
        "customer_care.Customer",
        verbose_name=_("customer_details"),
        blank=True,
        null=True,
        default="",
        on_delete=models.CASCADE,
    )
    start_point = JSONField(null=True)
    destination_point = JSONField(null=True)
    multi_stopage = JSONField(null=True, default=None)
    vehicle_type = models.ForeignKey(VehicleType, null=True, on_delete=models.CASCADE)
    created_date_time = models.DateTimeField(auto_now=True)


class Indent(models.Model):
    customer = models.ForeignKey(
        "customer_care.Customer",
        verbose_name=_("indent_customer"),
        blank=True,
        null=True,
        default="",
        on_delete=models.CASCADE,
    )
    start_point = JSONField(null=True, default=dict)
    destination_point = JSONField(null=True, default=dict)
    multi_stopage = JSONField(null=True, default=dict)
    date_indent = models.DateField(auto_now_add=True)
    time_indent = models.TimeField(auto_now_add=True)
    date_vehicle_required = models.DateField()
    time_vehicle_required = models.TimeField()
    vehicle_type = models.ForeignKey(VehicleType, null=True, on_delete=models.CASCADE)
    number_of_vehicle = models.PositiveSmallIntegerField()
    indent_code = models.CharField(max_length=30)
    is_vehicle_allocated = models.BooleanField(default=False)
    contract = models.ForeignKey(Contract, null=True, on_delete=models.CASCADE)
    is_disable = models.BooleanField(default=False)
    # Ashish
    cancellation_type = models.TextField(null=True, max_length=50)
    cancellation_date = models.DateTimeField(auto_now=True)
    is_ad_hoc = models.BooleanField(default=False)
    hoc_contract = models.PositiveIntegerField(default=0)
    is_repetitive = models.BooleanField(default=False)
    # Adding extra field to support vehicle-demand intregation with indent
    manifest_number = models.TextField(null=True, max_length=50)
    initial_indent_id = models.IntegerField(null=True, blank=True)
    previous_indent_id = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    updated_by = models.CharField(max_length=100, null=True, blank=True)
    is_separate_indent = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.indent_code}"

    class Meta:
        ordering = ("date_vehicle_required", "time_indent")

    @classmethod
    def query_all(cls):
        return cls.objects.all()


class AdHocIndent(models.Model):
    """

    """

    contract = models.ForeignKey(Contract, on_delete=models.CASCADE)
    indent = models.ForeignKey(Indent, on_delete=models.CASCADE)
    hoc_contract = models.PositiveIntegerField(default=0)


########################################################################################################################
# Name - IndentOrderLogs                                                                                               #
# Written By Vishnu Badal                                                                                              #
#                                                                                                                      #
########################################################################################################################

# Modified Outright by Ashish
class IndentOrderLogs(models.Model):
    indent_id = models.CharField(max_length=100, null=True)
    indent_data = JSONField(null=True)
    order_id = models.ForeignKey(Order, null=True, on_delete=models.CASCADE)
    order_data = JSONField(null=True)
    action = models.CharField(max_length=1000, null=True)
    user = JSONField(null=True)
    timestamp = models.DateTimeField(null=True)
    action_data = JSONField(null=True)
    valid_log = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    updated_by = models.CharField(max_length=100, null=True, blank=True)


########################################################################################################################
# Name - OrderAssignAlert                                                                                              #
# Written By Vishnu Badal                                                                                              #
#                                                                                                                      #
########################################################################################################################


class OrderAssignAlert(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    assign_order_alert_history = models.TextField(null=True, max_length=4000)
    trip_id = models.CharField(max_length=50, null=True)


########################################################################################################################
# Name - HubWaitingAlert                                                                                               #
# Written By Vishnu Badal                                                                                              #
#                                                                                                                      #
########################################################################################################################
class HubWaitingAlert(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    hub = models.ForeignKey(HubManagement, on_delete=models.CASCADE)
    hub_waiting_alert_history = models.TextField(null=True, max_length=4000)
    trip_id = models.CharField(max_length=50, null=True)


########################################################################################################################
# Name - CwhWaitingAlert                                                                                               #
# Written By Vishnu Badal                                                                                              #
#                                                                                                                      #
########################################################################################################################
class CwhWaitingAlert(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    # hub = models.ForeignKey(HubManagement)
    cwh_waiting_alert_history = models.TextField(null=True, max_length=4000)
    trip_id = models.CharField(max_length=50, null=True)


########################################################################################################################
# Name - ExpenseManagement                                                                                             #
# Written By Prafull Shukla                                                                                            #
########################################################################################################################


class ExpenseManagement(models.Model):
    toll = models.CharField(max_length=1000, null=True)
    soft_expenses = models.CharField(max_length=1000, null=True)
    food = models.CharField(max_length=1000, null=True)
    entry = models.CharField(max_length=1000, null=True)
    fuel = models.CharField(max_length=1000, null=True)
    total_advance = models.CharField(max_length=1000, null=True)
    origin_city = JSONField(null=True)
    destination_city = JSONField(null=True)
    route_name = models.CharField(max_length=1000, unique=True)


########################################################################################################################
# Name - Toll Plaza                                                                                             #
# Written By Prafull Shukla                                                                                            #
########################################################################################################################
class TollDetails(models.Model):
    state = models.CharField(max_length=500, null=True)
    nh_no = models.CharField(max_length=500, null=True)
    toll_name = models.CharField(max_length=500, null=True)
    toll_location = models.CharField(max_length=500, null=True)
    section = models.CharField(max_length=500, null=True)
    lat_long = JSONField(null=True)


#######################################################################################################################
# Name - CustomerEfficiency
#
#
#######################################################################################################################
class CustomerEfficiency(models.Model):
    indent = models.ForeignKey(Indent, on_delete=models.CASCADE)
    e_24 = models.DecimalField(max_digits=5, decimal_places=2)
    e_48 = models.DecimalField(max_digits=5, decimal_places=2)
    e_72 = models.DecimalField(max_digits=5, decimal_places=2)
    vehicle_r_date = models.DateField()
    time_vehicle_required = models.TimeField()
    indent_code = models.CharField(max_length=30)
    order_data = JSONField(null=True)
    per_date = models.DateField()
    per_date_time = models.TimeField()
    vehicle_count = models.IntegerField()
    e_24_count = models.IntegerField(default=0)
    e_48_count = models.IntegerField(default=0)
    e_72_count = models.IntegerField(default=0)
    is_express = models.BooleanField(default=False)

    class Meta:
        ordering = ("-vehicle_r_date", "-time_vehicle_required")  # Don't change

    @classmethod
    def _avg_efficiency(cls, **kwargs):
        """
        Used for calculate avg placement efficiency
        Remark:Don't change any line of Code
        :param kwargs:
        :return:
        """
        data_obj = cls.objects.all()
        if (
            kwargs.get("from_date") != ""
            and kwargs.get("to_date") != ""
            and kwargs.get("from_date") is not None
        ):
            data_obj = data_obj.filter(
                vehicle_r_date__range=[
                    datetime.datetime.strptime(kwargs.get("from_date"), "%Y-%m-%d"),
                    datetime.datetime.strptime(kwargs.get("to_date"), "%Y-%m-%d"),
                ]
            )
        if (
            kwargs.get("indent__customer") is not None
            and kwargs.get("indent__customer") != ""
        ):
            data_obj = data_obj.filter(indent__customer=kwargs.get("indent__customer"))

        if kwargs.get("is_express") is not None and kwargs.get("is_express") != "":
            data_obj = data_obj.filter(is_express=kwargs.get("is_express"))
        try:

            if kwargs.get("hours")["name"].strip() == "24":
                data_obj = data_obj.filter(e_24=100.00)
            elif kwargs.get("hours")["name"].strip() == "48":
                data_obj = data_obj.filter(e_48=100.00, e_24=0.0)
            elif kwargs.get("hours")["name"].strip() == "72":
                data_obj = data_obj.filter(e_72=100.00, e_48=0.0, e_24=0.0)
        except:
            pass
        if data_obj:
            sum_24 = np.sum(np.array(data_obj.values_list("e_24_count", flat=True)))
            sum_48 = np.sum(np.array(data_obj.values_list("e_48_count", flat=True)))
            sum_72 = np.sum(np.array(data_obj.values_list("e_72_count", flat=True)))
            sum_count = np.sum(
                np.array(data_obj.values_list("vehicle_count", flat=True))
            )
            avg_24 = round(float(sum_24) / sum_count, 2)
            try:
                avg_48 = round(float(sum_48) / sum_count, 2)
            except ZeroDivisionError:
                avg_48 = 0
            try:
                avg_72 = round(float(sum_72) / sum_count, 2)
            except ZeroDivisionError:
                avg_72 = 0
        else:
            avg_24 = 0
            avg_48 = 0
            avg_72 = 0  #
        if kwargs.get("hours") is not None:
            try:
                if kwargs.get("hours")["name"].strip() == "24":
                    avg_24 = avg_24
                    avg_48 = 0
                    avg_72 = 0  #
                elif kwargs.get("hours")["name"].strip() == "48":
                    avg_24 = 0
                    avg_48 = avg_48
                    avg_72 = 0  #
                elif kwargs.get("hours")["name"].strip() == "72":
                    avg_24 = 0
                    avg_48 = 0
                    avg_72 = avg_72  #
            except:
                pass
        return {
            "avg_24": round(avg_24 * 100, 2),
            "avg_48": round(avg_48 * 100, 2),
            "avg_72": round(avg_72 * 100, 2),
        }

    @classmethod
    def filter_placement_efficiency(cls, **kwargs):
        """
        Filter for placement efficiency
        Remark: Don't change any line of Code
        :param kwargs:
        :return:
        """
        filter_data_obj = cls.objects.all()
        if (
            kwargs.get("from_date") != ""
            and kwargs.get("to_date") != ""
            and kwargs.get("from_date") is not None
        ):
            filter_data_obj = filter_data_obj.filter(
                vehicle_r_date__range=[
                    datetime.datetime.strptime(kwargs.get("from_date"), "%Y-%m-%d"),
                    datetime.datetime.strptime(kwargs.get("to_date"), "%Y-%m-%d"),
                ]
            )
        if (
            kwargs.get("indent__customer") is not None
            and kwargs.get("indent__customer") != ""
        ):
            filter_data_obj = filter_data_obj.filter(
                indent__customer=kwargs.get("indent__customer")
            )
        if kwargs.get("is_express") is not None and kwargs.get("is_express") != "":
            filter_data_obj = filter_data_obj.filter(
                is_express=kwargs.get("is_express")
            )
        if kwargs.get("hours") is not None and kwargs.get("hours") != "":
            # for i in filter_data_obj:
            #     print i.e_24
            # This functionality has been built according to Yogender sir
            if kwargs.get("hours")["name"].strip() == "24":
                filter_data_obj = filter_data_obj.filter(e_24=100.00)
            elif kwargs.get("hours")["name"].strip() == "48":
                filter_data_obj = filter_data_obj.filter(e_48=100.00, e_24=0.0)
            elif kwargs.get("hours")["name"].strip() == "72":
                filter_data_obj = filter_data_obj.filter(
                    e_72=100.00, e_48=0.0, e_24=0.0
                )
            return filter_data_obj.values(
                "indent_code",
                "vehicle_r_date",
                "vehicle_count",
                "e_24",
                "e_48",
                "e_72",
                "e_24_count",
                "e_48_count",
                "e_72_count",
            )

        else:
            return filter_data_obj.values(
                "indent_code",
                "vehicle_r_date",
                "vehicle_count",
                "e_24",
                "e_48",
                "e_72",
                "e_24_count",
                "e_48_count",
                "e_72_count",
            )


######################################################################################################################
#
#
# Name - CustomerInTransitEfficiency
#
######################################################################################################################
class CustomerInTransitEfficiency(models.Model):
    order_code = models.CharField(max_length=30, null=True)
    indent_code = models.CharField(max_length=30, null=True)
    indent = models.ForeignKey(Indent, null=True, on_delete=models.CASCADE)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    efficiency = models.DecimalField(max_digits=15, decimal_places=2)
    start_point = JSONField(null=True, default=dict)
    destination_point = JSONField(null=True, default=dict)
    is_express = models.BooleanField(default=False)

    class Meta:
        ordering = ("-indent__date_vehicle_required", "-indent__time_vehicle_required")

    # variance = models.IntegerField(default=5)

    @classmethod
    def _avg_in_transit_efficiency(cls, value, **kwargs):  # value use later
        """
        Used for average efficiency
        Remark : Don't change any code
        :param value:
        :param kwargs:
        :return:
        """
        data_obj = cls.objects.all()
        if (
            kwargs.get("from_date") != ""
            and kwargs.get("to_date") != ""
            and kwargs.get("from_date") is not None
        ):
            data_obj = cls.objects.filter(
                indent__date_vehicle_required__range=[
                    datetime.datetime.strptime(kwargs.get("from_date"), "%Y-%m-%d"),
                    datetime.datetime.strptime(kwargs.get("to_date"), "%Y-%m-%d"),
                ]
            )
        if kwargs.get("is_customer") is not None and kwargs.get("is_customer"):
            data_obj = data_obj.filter(indent__customer=kwargs.get("indent__customer"))

        if kwargs.get("is_express") is not None and kwargs.get("is_express") != "":
            data_obj = data_obj.filter(is_express=kwargs.get("is_express"))
        total_count = data_obj.count()
        # print total_count
        if data_obj:  # hell it
            # in_time = filter(lambda y: 95 <= round(y, 0) <= 105, data_obj.values_list('efficiency', flat=True))
            in_time = filter(
                lambda y: 95 <= round(y, 0),
                data_obj.values_list("efficiency", flat=True),
            )
            try:
                in_time_avg = round(sum(1 for _ in in_time) / float(total_count), 4)
            except:
                in_time_avg = 0
            before_time_avg = 0
            # before_time = filter(lambda y: 105 < round(y, 0), data_obj.values_list('efficiency', flat=True))
            #
            # try:
            #     before_time_avg = round(before_time.__len__() / float(total_count), 4)
            #
            #
            # except:
            #     before_time_avg = 0

            after_time = filter(
                lambda y: 95 > round(y, 0),
                data_obj.values_list("efficiency", flat=True),
            )

            try:
                after_time_avg = round(
                    sum(1 for _ in after_time) / float(total_count), 4
                )
            except:
                after_time_avg = 0
            if kwargs.get("time_efficiency_type") is not None:
                try:
                    if kwargs.get("time_efficiency_type")["name"].strip() == "IN TIME":
                        in_time_avg = in_time_avg
                        after_time_avg = 0
                        before_time_avg = 0
                        pass
                    elif (
                        kwargs.get("time_efficiency_type")["name"].strip()
                        == "BEFORE TIME"
                    ):
                        before_time_avg = before_time_avg
                        in_time_avg = 0
                        after_time_avg = 0
                    elif (
                        kwargs.get("time_efficiency_type")["name"].strip()
                        == "AFTER TIME"
                    ):
                        after_time_avg = after_time_avg
                        in_time_avg = 0
                        before_time_avg = 0
                except:
                    pass
        else:
            in_time_avg = 0
            before_time_avg = 0
            after_time_avg = 0
        return (
            round(in_time_avg * 100, 4),
            round(before_time_avg * 100, 4),
            round(after_time_avg * 100, 4),
        )

    @classmethod
    def intransit_efficiency_filter(cls, **kwargs):
        """
        This is in transit efficiency filter
        Remark: Don't change code
        :param kwargs:
        :return:
        """
        filter_data_obj = cls.objects.all()
        if kwargs.get("from_date") != "" and kwargs.get("to_date") != "":
            filter_data_obj = cls.objects.filter(
                indent__date_vehicle_required__range=[
                    datetime.datetime.strptime(kwargs.get("from_date"), "%Y-%m-%d"),
                    datetime.datetime.strptime(kwargs.get("to_date"), "%Y-%m-%d"),
                ]
            )
        if kwargs.get("is_customer") is not None and kwargs.get("is_customer"):
            filter_data_obj = filter_data_obj.filter(
                indent__customer=kwargs.get("indent__customer")
            )
        if kwargs.get("is_express") is not None and kwargs.get("is_express") != "":
            filter_data_obj = filter_data_obj.filter(
                is_express=kwargs.get("is_express")
            )
        if (
            kwargs.get("time_efficiency_type") is not None
            and kwargs.get("time_efficiency_type") != ""
        ):
            if kwargs.get("time_efficiency_type")["name"].strip() == "IN TIME":
                filter_data_obj = filter_data_obj.filter(efficiency__gte=95)
            elif kwargs.get("time_efficiency_type")["name"].strip() == "BEFORE TIME":
                filter_data_obj = filter_data_obj.filter(efficiency__gt=105)
            elif kwargs.get("time_efficiency_type")["name"].strip() == "AFTER TIME":
                filter_data_obj = filter_data_obj.filter(efficiency__lt=95)

        return filter_data_obj.values(
            "indent_code", "order_code", "efficiency", "indent__date_vehicle_required"
        )


#####################################################################################################################
#
#
#
#
####################################################################################################################
class Bid(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    broker = models.ForeignKey(Broker, on_delete=models.CASCADE)
    advance_cash = models.CharField(blank=True, null=True, max_length=10)
    broker_advance = models.CharField(blank=True, null=True, max_length=10)
    deductions = models.CharField(blank=True, null=True, max_length=10)
    broker_rate = models.CharField(blank=True, null=True, max_length=10)
    floating_order_obj = JSONField(null=True)
    broker_username = models.CharField(blank=True, null=True, max_length=50)
    bid_done = models.BooleanField(default=False)
    bid_status = models.CharField(blank=True, null=True, max_length=30)
    tds = models.FloatField(null=True)


class PaymentMultiple(models.Model):
    entity_pay_id = models.IntegerField(null=True, blank=True)
    employee_pay_id = models.IntegerField(null=True, blank=True)
    order = models.ForeignKey(Order, null=True, blank=True, on_delete=models.CASCADE)
    txn = models.CharField(max_length=50)
    b_adv = models.BooleanField(default=False)
    entity = models.BooleanField(default=False)
    salary = models.BooleanField(default=False)
    own_1 = models.BooleanField(default=False)
    own_2 = models.BooleanField(default=False)
    b_balance = models.BooleanField(default=False)
    c_adv = models.BooleanField(default=False)
    y_res = models.BooleanField(default=False)  # False only work
    r_time = models.DateTimeField(null=True, blank=True)
    repeat = models.BooleanField(default=False)
    count_r = models.IntegerField(default=0)
    hub_id = models.IntegerField(null=True, blank=True)
    is_fast_tag = models.BooleanField(default=False)
    fast_tag_id = models.IntegerField(null=True, blank=True)

    # fast_tag_txn = models.IntegerField(null=True, blank=True)

    # def __unicode__(self):
    #     try:
    #         return self.order
    #     except:
    #         return ""

    @classmethod
    def generate_rand(cls):
        return hex(int(time() * 10000000))[2:]


class TimeEfficiency(models.Model):
    name = models.CharField(max_length=100, null=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now=True)


class PlacementEfficiencyTime(models.Model):
    name = models.CharField(max_length=100, null=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now=True)


class CustomerDataLastPush(models.Model):
    CO_NAME = (("A", "Amazon"), ("F", "Flipkart"), ("M", "Myntra"))
    co_name = models.CharField(max_length=1, choices=CO_NAME)
    comment = models.CharField(max_length=100, null=True)
    exception = models.BooleanField(default=False)
    updated_at = models.DateTimeField(null=True)
    is_deleted = models.BooleanField(default=False)
    is_msg = models.BooleanField(default=False)
    is_email = models.BooleanField(default=False)


class BrokerRateMatrix(models.Model):
    customer = models.ForeignKey(Customer, null=True, on_delete=models.CASCADE)
    contract = models.ForeignKey(Contract, null=True, on_delete=models.CASCADE)
    vehicle_type = models.ForeignKey(VehicleType, null=True, on_delete=models.CASCADE)
    broker = models.ForeignKey(Broker, null=True, on_delete=models.CASCADE)
    start_point = JSONField(null=True, default=dict)
    destination_point = JSONField(null=True, default=dict)
    broker_rate = models.CharField(max_length=50, null=True)
    is_active = models.BooleanField(default=True)


##################################################################################
# Name - Yes Bank cross check model
###############################################################################


class YesBankCrossCheck(models.Model):
    amount = models.CharField(max_length=20, blank=True)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    payment = models.CharField(max_length=20, blank=True)
    yes_status = JSONField()


class HourCheck(models.Model):
    hour = models.CharField(max_length=20, blank=True)
    is_active = models.BooleanField(default=False)


class AllowDefaultVendorType(models.Model):
    customer = models.OneToOneField(Customer, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.customer.company_name}<{self.customer.customer_code}>"


class GoboltBusinessApp(models.Model):
    broker_id = models.IntegerField(null=False)
    broker_code = models.CharField(max_length=255, null=False)
    broker_name = models.CharField(max_length=255, null=False)
    order_code = models.CharField(max_length=40, null=False)
    order_id = models.IntegerField(null=False)
    trip_code = models.CharField(max_length=40)
    vehicle_number = models.CharField(max_length=40)
    vehicle_code = models.CharField(max_length=40)
    vehicle_type = models.CharField(max_length=40)
    origin_name = models.CharField(max_length=255, null=False)
    origin_code = models.CharField(max_length=40, null=False)
    destination_name = models.CharField(max_length=255, null=False)
    destination_code = models.CharField(max_length=40, null=False)
    driver_name = models.CharField(max_length=255)
    driver_code = models.CharField(max_length=40)
    driver_mobile_number = models.CharField(max_length=100)
    customer_name = models.CharField(max_length=255)
    customer_code = models.CharField(max_length=40)
    status = models.CharField(max_length=255, null=False)
    order_status = models.CharField(max_length=255, null=False)
    trip_status = models.CharField(max_length=255)
    payment_status = models.CharField(max_length=255)
    pod_status = models.CharField(max_length=255)
    broker_rate = models.FloatField()
    tat = models.CharField(max_length=255)
    eta_datetime = models.CharField(max_length=255)
    total_distance = models.FloatField()
    vehicle_required_datetime = models.DateTimeField()
    trip_start_datetime = models.DateTimeField()
    trip_end_datetime = models.DateTimeField()
    vehicle_placement_datetime = models.DateTimeField()
    vehicle_reported_datetime = models.DateTimeField()
    lr_number = models.CharField(max_length=255)
    remarks = models.TextField()
    # trip_documents = models.ForeignKey("GoboltBusinessTripDocuments", null=True, on_delete=models.CASCADE)
    created_by = models.CharField(max_length=255, null=False)
    created_at = models.DateTimeField(null=False)
    updated_by = models.CharField(max_length=255, null=False)
    updated_at = models.DateTimeField(null=False)
    canceled_by = models.CharField(max_length=255)
    canceled_at = models.DateTimeField(),
    ops_assigned_vehicle = models.BooleanField(default=False)
    fuel_advance = models.FloatField()
    broker_advance = models.FloatField()
    tds = models.FloatField()
    balance_due = models.FloatField()
    trip_id = models.CharField(max_length=40)
    front_pod_pic = models.TextField()
    back_pod_pic = models.TextField()
    eta_delay_duration = models.FloatField(default=0)
    alert_status = models.CharField(max_length=255)
    pod_reject_remark = models.TextField()

    class Meta:
        db_table = 'trip'


class GoboltBusinessTripDocuments(models.Model):
    uuid = models.CharField(max_length=50, primary_key=True)
    trip_id = models.IntegerField()
    document_link = models.TextField()
    document_name = models.CharField(max_length=255)
    document_size = models.FloatField()
    lr_number = models.CharField(max_length=255)
    e_way_bill_number = models.CharField(max_length=255)
    invoice_number = models.CharField(max_length=255)
    created_by = models.CharField(max_length=255)
    created_at = models.DateTimeField()

    class Meta:
        db_table = 'trip_documents'


class GoboltBusinessVehicles(models.Model):
    vehicle_number = models.CharField(max_length=255)
    broker_code = models.CharField(max_length=255)
    insurance_start_datetime = models.DateTimeField()
    insurance_end_datetime = models.DateTimeField()
    fitness_start_datetime = models.DateTimeField()
    fitness_end_datetime = models.DateTimeField()
    driver_name = models.CharField(max_length=255)
    driver_code = models.CharField(max_length=40)
    driver_mobile_number = models.CharField(max_length=255, null=False)
    vehicle_type = models.CharField(max_length=255, null=False)
    rc_copy = models.TextField()
    insurance_paper = models.TextField()
    fitness_paper = models.TextField()
    permit_copy = models.TextField()
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=255)
    created_at = models.DateTimeField()
    updated_by = models.CharField(max_length=255)
    updated_at = models.DateTimeField()

    class Meta:
        db_table = 'vehicles'


class GoboltBusinessRejectedOrders(models.Model):
    broker_name = models.CharField(max_length=255)
    broker_code = models.CharField(max_length=255)
    rejected_reason = models.TextField()
    rejected_at = models.DateTimeField()
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='rejected_orders')
