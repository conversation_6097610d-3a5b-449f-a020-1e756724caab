# -*- coding: utf-8 -*-
"""
Django staging settings for gobolt project.
"""
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from settings.base import *  # pylint: disable=unused-wildcard-import
ENV = os.environ
DEBUG = True
# SENTRY_DSN = "https://<EMAIL>/5203480"
SENTRY_DSN = ""



# sentry_sdk.init(dsn=SENTRY_DSN, integrations=[DjangoIntegration()])
RAVEN_CONFIG = {
    "dsn": SENTRY_DSN,
    # If you are using git, you can also automatically configure the
    # release based on the git info.
    "release": raven.fetch_git_sha(os.path.abspath(os.getcwd())),
}
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
         "NAME": ENV.get("DB_NAME", "production_26_12_new"),
        # "NAME": ENV.get("DB_NAME", "production_26_12_live"),
        "USER": ENV.get("DB_USER", "postgres"),
        "PASSWORD": ENV.get("DB_PASSWORD", "A8dOH9oq32bxEHrj"),
        "HOST": ENV.get("DB_HOST", "127.0.0.1"),
        "PORT": ENV.get("DB_PORT", "5502"),
    },
    "replica": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": ENV.get("REPLICA_DB_NAME", "production_26_12_new"),
        # "NAME": ENV.get("REPLICA_DB_NAME", "production_26_12_live"),
        "USER": ENV.get("REPLICA_DB_USER", "postgres"),
        "PASSWORD": ENV.get("REPLICA_DB_PASSWORD", "A8dOH9oq32bxEHrj"),
        "HOST": ENV.get("REPLICA_DB_HOST", "127.0.0.1"),
        "PORT": ENV.get("REPLICA_DB_PORT", "5502"),
    },
    "gobolt_business": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": ENV.get("GOBOLT_BUSINESS_DB_NAME", "gobolt-business-dev"),
        "USER": ENV.get("GOBOLT_BUSINESS_DB_USER", "postgres"),
        "PASSWORD": ENV.get("GOBOLT_BUSINESS_DB_PASSWORD", "A8dOH9oq32bxEHrj"),
        "HOST": ENV.get("GOBOLT_BUSINESS_DB_HOST", "127.0.0.1"),
        "PORT": ENV.get("GOBOLT_BUSINESS_DB_PORT", "5502"),
    }
}

GOOGLE_IDP_URL = "https://storage.googleapis.com/gobolt-auth-dev/.well-known/jwks.json"
GOOGLE_ISSUER = "https://idp.gobolt.dev/"
GOOGLE_AUDIENCE = "https://google_endpoints/"

#
REDIS_HOST = ENV.get("REDIS_HOST", "localhost")
REDIS_PORT = ENV.get("REDIS_PORT", 6379)
REDIS_DB = int(ENV.get("REDIS_DB", 1))
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}"
CACHES = {
    "default": {
        "BACKEND": "redis_cache.RedisCache",
        "LOCATION": f"{REDIS_HOST}:{REDIS_PORT}",
        "OPTIONS": {"DB": REDIS_DB,},
    },
}
MONOGODB_HOST = ENV.get("MONOGODB_HOST", "tms-0-vo1xm.gcp.mongodb.net")
MONGODB_USERNAME = ENV.get("MONGODB_USERNAME", "tms_live")
MONOGODB_PASSWORD = ENV.get("MONOGODB_PASSWORD", "RUV5Vy0V7WOTcEkJ")
MONGODB_NAME = ENV.get("MONGODB_NAME", "production_26_12_new")
# MONGODB_NAME = ENV.get("MONGODB_NAME", "production_26_12_live")
MONOGO_URI = (
    f"mongodb+srv://{MONGODB_USERNAME}:"
    f"{MONOGODB_PASSWORD}@"
    f"{MONOGODB_HOST}/"
    f"{MONGODB_NAME}?retryWrites=true&w=majority"
)
mongoengine_connection(MONOGO_URI)

ES_PROVIDER = ENV.get("ES_PROVIDER", "")

ES_HOST = ENV.get("ES_HOST", "es-np-cd2292.es.us-central1.gcp.cloud.es.io")
ES_PORT = ENV.get("ES_PORT", 9243)

STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = ENV.get("STATIC_URL", "/static/")
INVOICE_SERVICE_HOST = ENV.get("INVOICE_SERVICE_HOST", "invoicing.gobolt.dev")
INVOICE_DETAIL_URL = ENV.get(
    "INVOICE_DETAIL_URL", "https://invoice.gobolt.dev/invoice/"
)
INVOICE_HOST_PREFIX = ENV.get("INVOICE_HOST_PREFIX", "/")

INVOICE_SERVICE_SCHEME = ENV.get("INVOICE_SERVICE_SCHEME", "http")

INVOICE_API_PREFIX = ENV.get("INVOICE_API_PREFIX", "/v1/invoice")
# TODO pub/sub
PUB_PROJECT_ID = ENV.get("PUB_PROJECT_ID", "services-248607")
PAYMENT_MODULE_PLACEMENT_TOPIC_CODE = os.environ.get("PAYMENT_MODULE_PLACEMENT_TOPIC_CODE", "trip-invalid-placement-trigger-dev")
PUSH_VEHICLE_GPS_DATA_TOPIC = ENV.get(
    "PUSH_VEHICLE_GPS_DATA_TOPIC", "push-data-to-stream-stg"
)
CREATE_IN_TRIP_SERVICE = ENV.get("CREATE_IN_TRIP_SERVICE", "trip-legacy-events")
NOTIFICATIONS_EVENTS_DOMAIN = ENV.get(
    "NOTIFICATIONS_EVENTS_DOMAIN", "https://notifications-events.gobolt.dev"
)
SIM_TRACKING_TASK_HOST = ENV.get("SIM_TRACKING_TASK_HOST", "sim-tracking-rest")
INVOICE_PUB_SUB_TOPIC_NAME = "invoicing-payments-events-listener"
INVOICE_PUB_SUB_PROJECT_ID = "services-248607"
PAYMENTS_HEALTH = ENV.get('PAYMENTS_HEALTH', "/v1/health/")

PUB_SUB_PROJECT_ID = ENV.get("PUB_SUB_PROJECT_ID", "services-248607")

BROKER_PAYMENT_PUB_SUB_TOPIC_NAME = ENV.get(
    "BROKER_PAYMENT_PUB_SUB_TOPIC_NAME", "ops-bulk-broker-balance-payment-proccess-dev")

OPS_BULK_ENTITY_PAYMENTS_PUB_SUB_TOPIC_NAME = ENV.get(
    "OPS_BULK_ENTITY_PAYMENTS_PUB_SUB_TOPIC_NAME", "ops-bulk-entity-payment-process-dev")

BULK_ENTITY_CREATION_PUB_SUB_TOPIC_NAME = ENV.get(
    "BULK_ENTITY_CREATION_PUB_SUB_TOPIC_NAME", "ops-bulk-entity-payments-creation")


POD_STATUS_UPDATE_PUB_SUB_TOPIC_NAME = ENV.get(
    "POD_STATUS_UPDATE_PUB_SUB_TOPIC_NAME", "ops-pod-status-update-dev")

INVOICE_MASTER_NORMS_PUB_SUB_TOPIC_NAME = ENV.get(
    "INVOICE_MASTER_NORMS_PUB_SUB_TOPIC_NAME", "ops-bulk-invoice-master-norms-creation-dev")

DUMMY_DRIVER_CODE = ENV.get("DUMMY_DRIVER_CODE", "GBXXXX")
DUMMY_DRIVER_NAME = ENV.get("DUMMY_DRIVER_NAME", "Dummy Driver")

FUELPUMP_PUB_SUB_TOPIC_NAME = ENV.get(
    "FUELPUMP_PUB_SUB_TOPIC_NAME", "gobolt-fuelpump-cancel-fuel-request")

FUELPUMP_MODULE_PLACEMENT_TOPIC_CODE = os.environ.get(
    "FUELPUMP_MODULE_PLACEMENT_TOPIC_CODE", "fuelpump-trip-placement-trigger-dev")

MAINTENANCE_PAYMENT_STATUS_UPDATE_TOPIC_NAME = os.environ.get(
    "MAINTENANCE_PAYMENT_STATUS_UPDATE_TOPIC_NAME", "maintenance-payment-status-update")

DAS_UPDATE_PUB_SUB_TOPIC_NAME = os.environ.get(
    "DAS_UPDATE_PUB_SUB_TOPIC_NAME", "ops-trip-invalid-trigger-dev")

PAYMENT_ADVICE_TOPIC_NAME = os.environ.get(
    "PAYMENT_ADVICE_TOPIC_NAME", "payment-advice-send-dev"
)

SIM_CONSENT_URI = os.environ.get("SIM_CONSENT_URI", "/v1/consent/check")

GOOGLE_PLACE_API_KEY = os.environ.get("GOOGLE_PLACE_API_KEY", "AIzaSyCU63ExwAZq3KuO-5uXBGZheiENHivpc0k")

BROKER_ADVANCE_PAYMENT_PUB_SUB_TOPIC_NAME = ENV.get(
    "BROKER_ADVANCE_PAYMENT_PUB_SUB_TOPIC_NAME", "ops-bulk-broker-advance-payment-dev")

GOBOLT_BUSINESS_ES_RECORDS_PUB_SUB_TOPIC_NAME = ENV.get(
    "GOBOLT_BUSINESS_ES_RECORDS_PUB_SUB_TOPIC_NAME", "gobolt-business-elastic-records-creation-dev")
