{% load static %}
{% load template_tag %}
<!DOCTYPE html>
<!--
This is a starter template page. Use this page to start your new project from
scratch. This page gets rid of all links and provides the needed markup only.
-->
<html ng-app="myApp" ng-controller="stateChangeCtrls">
{% if Mi %}

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>GoBOLT</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="{% static 'minify/lib/alte/bootstrap/css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'minify/stylesheet/headerFreeze.css' %}">
    <script data-require="ui-bootstrap@*" data-semver="1.3.3"
        src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/1.3.3/ui-bootstrap-tpls.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" type="text/css" href="{% static 'minify/lib/custom/sweetalert.css' %}">

    <!-- Theme style -->
    <link rel="stylesheet" href="{% static 'minify/lib/common/jquery-ui/themes/base/jquery-ui.min.css' %}">

    <link rel="stylesheet" href="{% static 'minify/lib/alte/dist/css/AdminLTE.min.css' %}">

    <link rel="stylesheet" href="{% static 'minify/lib/alte/dist/css/skins/_all-skins.min.css' %}">

    <link rel="stylesheet" href="{% static 'minify/lib/common/angular-ui-select/dist/select.min.css' %}">
    <link rel="stylesheet" href="{% static 'minify/stylesheet/gobolt.css' %}">
    <script src="{% static 'minify/lib/custom/sweetalert.min.js' %}"></script>
    <script src="{% static 'minify/lib/custom/csv.min.js' %}"></script>
    {% comment %} <script src="https://cdnjs.cloudflare.com/ajax/libs/alasql/0.3.7/alasql.min.js"></script> {% endcomment %}
    <script src="{% static 'cdn/js/alasql.min.js' %}"></script>
    {% comment %} <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.9.2/xlsx.core.min.js"></script> {% endcomment %}
    <script src="{% static 'cdn/js/xlsx.core.min.js' %}"></script>

    <script src="{% static 'minify/lib/alte/plugins/jQuery/jquery-2.2.3.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/jquery-ui/jquery-ui.min.js' %}"></script>
    <script src="{% static 'minify/lib/custom/jquery-clockpicker.min.js' %}"></script>
    {#        <script src="{% static 'minify/lib/custom/auto_reload.js' %}"></script>#}
    <link rel="stylesheet" href="{% static 'minify/lib/custom/jquery-clockpicker.min.css' %}">
    <script type="text/javascript"
        src="https://maps.googleapis.com/maps/api/js?key={% google_api_key %}&libraries=geometry,places"></script>
    <script src="https://cdn.zingchart.com/zingchart.min.js"></script>
    <script> zingchart.MODULESDIR = "https://cdn.zingchart.com/modules/";
        ZC.LICENSE = ["569d52cefae586f634c54f86dc99e6a9", "ee6b7db5b51705a13dc2339db3edaf6d"];</script>
    <!-- AdminLTE Skins. We have chosen the skin-blue for this starter
              page. However, you can choose any other skin. Make sure you
              apply the skin class to the body tag so the changes take effect.
        -->
    <link rel="stylesheet" href="{% static 'minify/lib/alte/dist/css/skins/skin-blue.min.css' %}">
    <link rel="stylesheet" href="{% static 'minify/lib/alte/plugins/pace/pace.min.css' %}">
    <script src="{% static 'minify/lib/common/angular/angular.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/angular-ui-router/release/angular-ui-router.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/ngmap/build/scripts/ng-map.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/angular-cookies/angular-cookies.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/angular-sanitize/angular-sanitize.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/angular-ui-select/dist/select.min.js' %}"></script>
    <script src="{% static 'minify/lib/common/async/dist/async.js' %}"></script>
    <script src="{% static 'minify/lib/common/angularUtils-pagination/dirPagination.js' %}"></script>
    <!--<script type="text/javascript">

             var idleTime = 0;
             $(document).ready(function () {
                 //Increment the idle time counter every minute.
                 var idleInterval = setInterval(timerIncrement, 60000); // 1 minute

                 //Zero the idle timer on mouse movement.
                 $(this).mousemove(function (e) {
                     idleTime = 0;
                 });
                 $(this).keypress(function (e) {
                     idleTime = 0;
                 });
             });

             function timerIncrement() {
                 idleTime = idleTime + 1;
                 if (idleTime > 2) { // 5 minutes
                     window.location.reload();
                 }
             }
         </script>-->

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<!--
    BODY TAG OPTIONS:
    =================
    Apply one or more of the following classes to get the
    desired effect
    |---------------------------------------------------------|
    | SKINS         | skin-blue                               |
    |               | skin-black                              |
    |               | skin-purple                             |
    |               | skin-yellow                             |
    |               | skin-red                                |
    |               | skin-green                              |
    |---------------------------------------------------------|
    |LAYOUT OPTIONS | fixed                                   |
    |               | layout-boxed                            |
    |               | layout-top-nav                          |
    |               | sidebar-collapse                        |
    |               | sidebar-mini                            |
    |---------------------------------------------------------|
    -->

<body class="hold-transition skin-blue sidebar-mini">
    <div class="wrapper">

        <!-- Main Header -->
        <header class="main-header">
            <div class="web-view">
                <a href="














                        {% if request.user.is_superuser or request.user.is_staff %}{% url 'admin-login' %}{% elif request.user|can_planning_team %}{% url 'planning-dashboard' %}{% endif %}"
                    class="logo-new logo">

                    <span class="logo-mini"><img src="../static/apps/common/images/icon.png" /></span>

                    <span class="logo-lg"><img src="../static/minify/common/images/logo.png" /></span>
                </a>
            </div>


            <nav class="navbar navbar-new navbar-static-top" role="navigation">
                <div class="web-view">
                    <a href="#" class="sidebar-toggle sidebar-toggle-new" data-toggle="offcanvas" role="button">
                        <span class="sr-only">Toggle navigation</span>
                    </a>
                </div>

                <div class="web-view top-nav">
                    {% if request.user|can_planning_team or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/planning/"><span type="button" class="btn btn-info">Planning</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_sourcing_agent or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/market/"><span type="button" class="btn btn-info">Market</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_view_bilty or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/bilty/"><span type="button" class="btn btn-info">Bilty</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_account_manager or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/account/"><span type="button" class="btn btn-info">Account</span></a></li>
                    {% endif %}

                    {% if request.user|can_hub_manager or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/hub/"><span type="button" class="btn btn-info">Hub</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_finance or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/finance/"><span type="button" class="btn btn-info">Finance</span></a></li>
                    {% endif %}

                    {% if request.user|can_compliance or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/compliance/"><span type="button" class="btn btn-info">Compliance</span></a>
                    </li>
                    {% endif %}
                    <!--                    <li ><a href="/vehicle-demand/"><span type="button"-->
                    <!--                                                                            class="btn btn-info">Vehicle Demand</span></a>-->
                    <!--                    </li>-->

                    <li><a href="/analytics/"><span type="button" class="btn btn-info">Analytics</span></a>
                    </li>
                </div>


                <div class="navbar-custom-menu">

                    <ul class="nav navbar-nav web-view">

                        <!--                        <li class="dropdown notifications-menu" data-toggle="tooltip" data-placement="bottom"-->
                        <!--                            title="Unschedule Alerts">-->
                        <!--                            <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown"-->
                        <!--                               aria-expanded="false">-->
                        <!--                                <div ng-class="{'animated heartBeat infinite': badgeValue}">-->
                        <!--                                    <i class="fa fa-bell-o" ng-click="openAlertModal()">-->
                        <!--                                    </i>-->
                        <!--                                    <span class="label label-warning" ng-bind="badgeValue"></span>-->
                        <!--                                </div>-->
                        <!--                            </a>-->
                        <!--                            &lt;!&ndash;<ul class="dropdown-menu">-->
                        <!--                                <li class="header">You have <span ng-bind="badgeValue"></span> Unschedule stoppage</li>-->
                        <!--                                <li>-->

                        <!--                                    <ul class="menu">-->

                        <!--                                        <li ng-repeat="item in data_list">-->
                        <!--                                            <a href="javascript:void(0)" ng-click="openAlertBox(item)">-->
                        <!--                                                <i class="fa fa-warning text-yellow"></i> <span-->
                        <!--                                                    ng-bind="item.v_no"></span>-->
                        <!--                                            </a>-->
                        <!--                                        </li>-->
                        <!--                                    </ul>-->
                        <!--                                </li>-->
                        <!--                            </ul>&ndash;&gt;-->
                        <!--                        </li>-->

                        <!-- Notification -->
                        <li class="dropdown">
                            <a href="javascript:void(0)" class="bell-icon dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-bell" aria-hidden="true"></i>
                                <span class="notification-count" id="global-notification-count"></span>
                            </a>
                            <div class="dropdown-menu notification-menu">
                                <div class="notification-header" id="global-notification-header"> 0 New Notifications
                                </div>
                                <ul class="notification-list" id="global-notification-list">
                                </ul>
                                <a class="notification-view-btn" href="/notification">View All</a>
                            </div>
                        </li>
                        <!--/ Notification-->


                        <li class="dropdown user user-menu">


                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="../static/apps/common/images/login-user.png" alt="user" />
                                <span class="hidden-xs">Hi {{ request.user.first_name }}</span>
                                <i class="fa fa-angle-down" aria-hidden="true"></i>
                            </a>
                            <ul class="dropdown-menu">

                                <li class="user-header">

                                    <p>
                                        Hi {{ request.user.first_name }}
                                        <small>Member since Oct. 2016</small>
                                    </p>
                                </li>

                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                    </div>

                                </li>

                                <li class="user-footer">
                                    <div class="pull-left">
                                        <a ui-sref="profile" class="btn btn-default btn-flat">Profile</a>
                                    </div>
                                    <div class="pull-right">
                                        <a href="{% url '12-logout-user' %}" class="btn btn-default btn-flat">Sign
                                            out</a>
                                    </div>
                                </li>
                            </ul>
                        </li>

                    </ul>

                    <!-- Control Sidebar Toggle Button -->

                    <ul class="nav navbar-nav mobile-view">

                        <li>
                            <a href="#" data-toggle="control-sidebar" class="pull-right"><i class="fa fa-list-ul"
                                    aria-hidden="true"></i></a>
                        </li>
                        <div style="float: left"><a
                                href="


                                {% if request.user.is_superuser or request.user.is_staff %}{% url 'admin-login' %}{% elif request.user|can_planning_team %}{% url 'planning-dashboard' %}{% endif %}"
                                class="logo-new logo"><img src="../static/apps/common/images/icon.png"
                                    style="height: 45px;  margin-left: 110px" /></a></div>

                        <li class="dropdown notifications-menu" style="float: right" data-toggle="tooltip"
                            data-placement="bottom" title="Unschedule Alerts">
                            <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown"
                                aria-expanded="false">
                                <div ng-class="{'animated heartBeat infinite': badgeValue}">
                                    <i class="fa fa-bell-o" ng-click="openAlertModal()">
                                    </i>
                                    <span class="label label-warning" ng-bind="badgeValue"></span>
                                </div>

                            </a>
                            <!--<ul class="dropdown-menu">
                                <li class="header">You have <span ng-bind="badgeValue"></span> Unschedule stoppage</li>
                                <li>

                                    <ul class="menu">

                                        <li ng-repeat="item in data_list">
                                            <a href="javascript:void(0)" ng-click="openAlertBox(item)">
                                                <i class="fa fa-warning text-yellow"></i> <span
                                                    ng-bind="item.v_no"></span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>-->
                        </li>

                        <li class="dropdown user user-menu" style="float: right">

                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="../static/apps/common/images/login-user.png" alt="user" />
                                <span class="logout-popup">Hi {{ request.user.first_name }}</span>
                                <i class="fa fa-angle-down" aria-hidden="true"></i>
                            </a>
                            <ul class="dropdown-menu">

                                <li class="user-header">

                                    <p>
                                        Hi {{ request.user.first_name }}
                                        <small>Member since Oct. 2016</small>
                                    </p>
                                </li>

                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                    </div>

                                </li>

                                <li class="user-footer">
                                    <div class="pull-left">
                                        <a ui-sref="profile" class="btn btn-default btn-flat">Profile</a>
                                    </div>
                                    <div class="pull-right">
                                        <a href="{% url '12-logout-user' %}" class="btn btn-default btn-flat">Sign
                                            out</a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>

                        <div class="mobile-view" style="float:left; margin-left: 0; max-width:350px;">
                            <ul  class="list-link" style="padding-left: 16px;">
                    <div class="mobile-view" style="float:left; margin-left: 25%; max-width:400px;">
                        <ul class="list-link" style="padding-left: 16px;">
                            {% if request.user|can_planning_team or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/planning/"><span type="button"
                                        class="btn btn-info">Pln</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_sourcing_agent or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/market/"><span type="button"
                                        class="btn btn-info">Mrk</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_view_bilty or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/bilty/"><span type="button"
                                        class="btn btn-info">Blt</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_account_manager or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/account/"><span type="button"
                                        class="btn btn-info">Acc</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_hub_manager or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/hub/"><span type="button"
                                        class="btn btn-info">Hub</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_compliance or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/compliance/"><span type="button"
                                        class="btn btn-info">Com</span></a>
                            </li>
                            {% endif %}

                            <li style=" margin: 4px 2px;"><a href="/vehicle-demand/"><span type="button"
                                        class="btn btn-info">Veh
                                        Demand</span></a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <aside class="main-sidebar sidebar-new">


            <section class="sidebar">


                <ul class="sidebar-menu sidebar-menu-new">


                    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Trip Management</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="vehicle_tracking"><i
                                        class="fa fa-circle-o"></i>Vehicle
                                    Tracking</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="ongoing-trip"><i class="fa fa-circle-o"></i>On Going
                                    Trip</a></li>-->
                            <li ui-sref-active="active"><a ui-sref="completed-trip"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip</a></li>
                            <li ui-sref-active="active"><a ui-sref="completed-trip-history"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip History</a></li>
                            <li ui-sref-active="active"><a ui-sref="realtime-tracking-data"><i
                                        class="fa fa-circle-o"></i>Realtime Tracking Data</a></li>
                            <li ui-sref-active="active"><a ui-sref="gps-crossover"><i class="fa fa-circle-o"></i>Vehicle
                                    GPS Crossover</a></li>
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Alert</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li ui-sref-active="active"><a ui-sref="placement-assurance"><i
                                        class="fa fa-circle-o"></i>Placement
                                    Assurance</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="hub-waiting"><i class="fa fa-circle-o"></i>Hub
                                    Waiting</a></li>-->
                            <li ui-sref-active="active"><a ui-sref="waiting-at-cwh"><i
                                        class="fa fa-circle-o"></i>Waiting
                                    at CWH</a></li>
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage"><i
                                        class="fa fa-circle-o"></i>Unscheduled
                                    Stoppage</a></li>

                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage-v2"><i
                                        class="fa fa-circle-o"></i>
                                    Stoppage Alerts</a></li>

                            <li ui-sref-active="active"><a ui-sref="detour-alert">
                                    <i class="fa fa-circle-o"></i>Detour Alerts</a>
                            </li>

                            <li ui-sref-active="active"><a ui-sref="detour-alert-v2"><i class="fa fa-circle-o"></i>
                                    Route Deviation Alerts</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="pilferage-alert">
                                    <i class="fa fa-circle-o"></i>Pilferage Alerts</a>
                                </li>-->
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Analytics & Stats</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li ui-sref-active="active"><a ui-sref="completed-trip-analytics"><i
                                        class="fa fa-circle-o"></i>
                                    Fuel and Distance</a></li>
                        </ul>
                    </li>

                    <li class="treeview" ui-sref-active="active">
                        <a ui-sref="update-trip-time">
                            <i class="fa fa-link"></i>
                            <span>Change Trip Times</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </section>
        </aside>

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper content-wrapper-new custom-box">


            <!-- Main content -->
            <section class="content" ui-view="">

                <!-- Your Page Content Here -->

            </section>

        </div>

        <footer class="main-footer">

            <!--<div class="pull-right hidden-xs">
                Anything you want
            </div>-->

            <strong>Copyright &copy; 2017 <a href="#">Camions Logistics Solutions Pvt Ltd</a>.</strong> All rights
            reserved.
        </footer>
        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <section class="sidebar">


                <ul class="sidebar-menu sidebar-menu-new">


                    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Trip Management</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="vehicle_tracking"><i
                                        class="fa fa-circle-o"></i>Vehicle
                                    Tracking</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="ongoing-trip"><i class="fa fa-circle-o"></i>On Going
                                    Trip</a></li>-->
                            <li ui-sref-active="active"><a ui-sref="completed-trip"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip</a></li>
                            <li ui-sref-active="active"><a ui-sref="completed-trip-history"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip History</a></li>
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Alerts</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="placement-assurance"><i
                                        class="fa fa-circle-o"></i>Placement
                                    Assurance</a></li>
                            <li ui-sref-active="active"><a ui-sref="hub-waiting"><i class="fa fa-circle-o"></i>Hub
                                    Waiting</a></li>
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage"><i
                                        class="fa fa-circle-o"></i>Unscheduled
                                    Stoppage</a></li>
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage-v2"><i
                                        class="fa fa-circle-o"></i>
                                    Stoppage Alerts</a></li>
                            <li ui-sref-active="active"><a ui-sref="detour-alert-v2"><i class="fa fa-circle-o"></i>
                                    Route Deviation Alerts</a></li>
                            <li ui-sref-active="active"><a ui-sref="waiting-at-cwh"><i
                                        class="fa fa-circle-o"></i>Waiting
                                    at CWH</a></li>
                        </ul>
                    </li>

                    {% endif %}
                </ul>

            </section>
        </aside>
        <!-- /.control-sidebar -->
        <!-- Add the sidebar's background. This div must be placed
             immediately after the control sidebar -->
        <div class="control-sidebar-bg"></div>
    </div>
    <!-- ./wrapper -->

    <!-- REQUIRED JS SCRIPTS -->


    <!-- Bootstrap 3.3.6 -->
    <script src="{% static 'minify/lib/alte/bootstrap/js/bootstrap.min.js' %}"></script>
    <script src="https://www.gstatic.com/firebasejs/4.9.0/firebase.js"></script>
    <script data-require="ui-bootstrap@*" data-semver="1.3.3"
        src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/1.3.3/ui-bootstrap-tpls.min.js"></script>

    <script src="{% static 'minify/lib/alte/plugins/pace/pace.min.js' %}"></script>
    <!-- AdminLTE App -->
    <script src="{% static 'minify/lib/alte/dist/js/app.min.js' %}"></script>

    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}
    <!-- add here tracking agent -->

    <script src="{% static 'minify/gobolt_opc/tracking_team/modules.js' %}"></script>
    <script src="{% static 'minify/gobolt_opc/tracking_team/app.js' %}"></script>
    <script src="{% static 'minify/common/directive/headerfreeze.js' %}"></script>

    <script src="{% static 'minify/gobolt_opc/tracking_team/controller/tripMgt.js' %}"></script>
    <!--<script src="{% static 'minify/gobolt_opc/tracking_team/controller/tracking_controller.js' %}"></script>-->
    <script src="{% static 'minify/gobolt_opc/tracking_team/controller/trip_time.js' %}"></script>
    <script src="{% static 'minify/gobolt_opc/tracking_team/service/service.js' %}"></script>
    <script src="{% static 'minify/gobolt_opc/tracking_team/filter/filter.js' %}"></script>

    <!-- add here planning team -->
    {% endif %}

    <script src="{% static 'minify/common/directive/directives.js' %}"></script>
    <script src="{% static 'minify/common/directive/loading.js' %}"></script>
    <script src="{% static 'minify/common/directive/g_autocompletes.js' %}"></script>

    <script src="{% static 'minify/gobolt_admin/controller/profile_management/profileCtrl.js' %}"></script>
    <script src="{% static 'minify/gobolt_admin/service/service.js' %}"></script>
    <script src="{% static 'minify/common/services/master.js' %}"></script>
    <audio id="xyz" src="{% static 'apps/common/tones/alert_signal.mp3' %}"></audio>
    <audio id="noti" src="{% static 'apps/common/tones/noti.mp3' %}"></audio>
    <!-- Alerts Modal -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Stopped Vehicles</h4>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Vehicle Number</th>
                                    <th>Customer</th>
                                    <th>Origin</th>
                                    <th>Destination</th>
                                    <th>Current Location</th>
                                    <th>Delay Status</th>
                                    <th>Stopped Duration</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in data_list">
                                    <td ng-bind="$index + 1"></td>
                                    <td><span ng-bind="item.v_no"> </span></td>
                                    <td><span ng-bind="item.customer"></span></td>
                                    <td><span ng-bind="item.origin"></span></td>
                                    <td><span ng-bind="item.destination"></span></td>
                                    <td><span ng-bind="item.location.current_location"></span></td>
                                    <td><span ng-bind="item.final_delay | secondsToHM"></span></td>
                                    <td><span ng-bind="item.duration | secondsToHM"></span></td>
                                    <td><span data-toggle="tooltip" data-placement="top" title="Reason for delay">
                                            <button class="fa fa-commenting" ng-click="openAlertBox(item)">
                                            </button>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Update Reason Alerts Modal -->
    <div class="modal fade" id="updateReason" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Stopped Vehicles</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="reason_frm">
                        <div class="form-group">

                            <label class="col-sm-2 control-label">Select Reason</label>
                            <div class="col-md-4"
                                ng-class="{ 'has-error' : reason_frm.rsn.$invalid && !reason_frm.rsn.$pristine }">
                                <ui-select name="rsn" data-ng-model="item.reason" ng-required="true" theme="">
                                    <ui-select-match placeholder="Reason...">
                                        <span ng-bind="$select.selected.reason"></span></ui-select-match>
                                    <ui-select-choices repeat="reason in reasons | filter: $select.search">
                                        <div ng-bind-html="reason.reason | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="reason_frm.rsn.$dirty && reason_frm.rsn.$error.required" class="help-block">
                                    Reason is required.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="clear-button btn btn-default"
                                data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary apply-button" data-dismiss="modal"
                                ng-disabled="reason_frm.$invalid" ng-click="submitReason(item)">Submit
                            </button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>


    <!-- Optionally, you can add Slimscroll and FastClick plugins.
         Both of these plugins are recommended to enhance the
         user experience. Slimscroll is required when using the
         fixed layout. -->

    <script>
        (function () {
            var gobolt = angular.module('myApp');

            gobolt.controller('stateChangeCtrls', ['$scope', '$state', '$http', '$timeout', function ($scope, $state, $http, $timeout) {
                $scope.changeState = changeState;

                function changeState() {
                    $state.go('vehicle_tracking');
                };

                $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));
                $scope.badgeValue = localStorage.getItem("badgeValue");

                $scope.openAlertModal = function () {
                    if ($scope.badgeValue) {
                        $('#myModal').modal();
                        $('#myModal').on('shown.bs.modal', function () {
                            $(document).off('focusin.modal');
                        });
                    }
                    else {
                        swal("You have 0 stopped vehicles.", "", "warning")
                    }

                    var url = '/gobolt/trip/un-sch-reasons/';
                    $http.get(url).then(function (return_data) {
                        console.log('reason_list', return_data);
                        if (return_data.data.code.status === 200) {
                            $scope.reasons = return_data.data.reason_list;
                        }
                    })



                };

                try {
                    /**
                     * Firebase configuration
                     * Written By: Prafull
                     * Dated: 11/06/2019
                     */
                    $scope.buzzSiren = false;

                    var firebaseConfig = {
                        apiKey: "AIzaSyAFKI5wFobjhhrgSOFJLQf4N9vAD7CF724",
                        authDomain: "applied-tractor-172109.firebaseapp.com",
                        databaseURL: "https://applied-tractor-172109.firebaseio.com",
                        projectId: "applied-tractor-172109",
                        storageBucket: "applied-tractor-172109.appspot.com",
                        messagingSenderId: "1020989094457",
                        appId: "1:1020989094457:web:a7e7a0b76304a23d"
                    };

                    // Initialize Firebase
                    firebase.initializeApp(firebaseConfig);
                    const messaging = firebase.messaging();
                    messaging.requestPermission()
                        .then(function () {
                            console.log('Have Permission To Access Firebase.');
                            return messaging.getToken()
                        })
                        .then(function (token) {
                            console.log('Token Value', token);
                            var token_obj = {
                                'token': token
                            };
                            var url = '/gobolt/trip/reg-web-device/';
                            $http.post(url, token_obj).then(function (return_data) {
                                console.log(return_data);
                                if (return_data.data.code.status !== 200) {
                                    swal("FCM Token not registered. Unable to send notifications.", "", "error")
                                }
                            })

                        })
                        .catch(function (reason) {
                            console.log('Error', reason)
                        });



                    messaging.onMessage(function (payLoad) {
                        console.log('onMessage', payLoad);
                        if (payLoad) {
                            if (payLoad.data.alert) {
                                $scope.data_list = [];
                                // now call post api and get data there vehicle
                                var url = '/gobolt/trip/notification/get_data/';
                                $http.post(url, payLoad.data.data_list).then(function (return_data) {


                                    // localStorage block


                                    //set badge value
                                    localStorage.setItem("badgeValue", return_data.data.data.length);
                                    console.log(return_data);
                                    var str_obj = payLoad.data.data_list;
                                    $scope.data_list = eval('(' + str_obj + ')');

                                    localStorage.setItem("alertDataList", JSON.stringify(return_data.data.data));
                                    $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));
                                    $scope.badgeValue = localStorage.getItem("badgeValue");
                                    if ($scope.badgeValue === 0) {
                                        document.getElementById('noti').pause();
                                        swal("Updated!", "All alerts updated successfully.", "success")
                                    }
                                    else {
                                        swal({
                                            title: "Stop alert!",
                                            text: "Vehicles have been stopped.",
                                            type: "warning",
                                            timer: 300
                                        },
                                            function () {
                                                // document.getElementById('xyz').play().loop = true;
                                                document.getElementById('noti').play()
                                            });
                                    }

                                });
                                //  document.getElementById('xyz').play().loop = true;



                            }
                        }
                    });
                }
                catch (err) {
                    console.log(err)
                }


                //  $scope.badgeValue = $scope.data_list.length;

                $scope.changeState();

                $scope.openAlertBox = function (item) {
                    $scope.index = $scope.data_list.indexOf(item);
                    $scope.item = item;
                    console.log('$scope.index', $scope.index);
                    $('#updateReason').modal();
                    $('#updateReason').on('shown.bs.modal', function () {
                        $(document).off('focusin.modal');
                    });
                    /**
                     * swal({
                            title: "Vehicle number " + item.v_no + " is stopped for " + item.duration / 60 + 'Min.',
                            text: "Current location is " + item.location.current_location,
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "Yes, update it!",
                            cancelButtonText: "No, cancel !",
                            closeOnConfirm: false,
                            closeOnCancel: false
                        },
                     function (isConfirm) {
                            if (isConfirm) {
                                swal({
                                        title: "Enter!",
                                        text: "Reason for delay:",
                                        type: "input",
                                        showCancelButton: true,
                                        closeOnConfirm: false,
                                        animation: "slide-from-top",
                                        inputPlaceholder: "Reason"
                                    },
                                    function (inputValue) {
                                        if (inputValue === false) return false;

                                        var inputV = inputValue.trim();
                                        if (inputV === "") {
                                            swal.showInputError("You need to write the reason!");
                                            return false
                                        }
                                        $scope.data_list.splice($scope.index, 1);
                                        $scope.badgeValue = $scope.data_list.length;
                                        console.log('data_list', $scope.data_list)
                                        var url = '/gobolt/trip/update-unschedule-via-noti/'
                                        var data_obj = {
                                            'item': item,
                                            'return_data_list': $scope.data_list,
                                            'reason': inputV
                                        };
                                        $http.post(url, data_obj).then(function (return_data) {
                                            console.log(return_data);
                                            if (return_data.data.code.status === 200) {
                                                $scope.data_list = return_data.data.data_list
                                                $scope.badgeValue = $scope.data_list.length;
                                                swal("Updated!", "Your data has been updated", "success")
                                            }
                                        });
                                        // biltyServices.rejectTrip(id, inputV).then(function (response) {
                                        //     if (response.data.code.status === 200) {
                                        //         $scope.orderAssigns.splice(index, 1);
                                        //         swal("Deleted!", "Your data has been deleted.", "success");
                                        //     }
                                        //     else {
                                        //         swal("Can't Cancel!", response.data.code.message, "error")
                                        //     }
                                        // })
                                        if ($scope.badgeValue === 0) {
                                            $('#myModal').modal('hide');
                                            document.getElementById('xyz').pause()
                                        }
                                        try {
                                            $scope.$apply()
                                        }
                                        catch (ew) {

                                        }

                                    })
                            } else {
                                swal("Cancelled", "Your data is safe :)", "error");
                            }
                        });
                     */

                };


                $scope.submitReason = function (item) {
                    $scope.data_list.splice($scope.index, 1);

                    //set updated badge value
                    localStorage.setItem("badgeValue", $scope.data_list.length);

                    //get updated stored badge value
                    $scope.badgeValue = localStorage.getItem("badgeValue");

                    console.log('reason')
                    var url = '/gobolt/trip/update-unschedule-via-noti/'
                    var data_obj = {
                        'item': item,
                        'return_data_list': $scope.data_list,
                        'reason': item.reason.reason
                    };
                    $http.post(url, data_obj).then(function (return_data) {
                        console.log(return_data);
                        if (return_data.data.code.status === 200) {

                            // Update data_list in localstorage
                            localStorage.setItem("alertDataList", JSON.stringify(return_data.data.data_list));

                            // Get data list from localstorage
                            $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));

                            // Update badgeValue in localstorage
                            localStorage.setItem("badgeValue", $scope.data_list.length);

                            // Get badgeValue from localstorage
                            $scope.badgeValue = localStorage.getItem("badgeValue");
                            swal("Updated!", "Your data has been updated", "success")
                        }
                    });
                }


            }])
        })();

    </script>
    <script>
        var pic = "{{ pic }}";
    </script>
</body>
{% endif %}

{% if not Mi %}

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>GoBOLT</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="{% static 'apps/lib/alte/bootstrap/css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'apps/stylesheet/headerFreeze.css' %}">
    <script data-require="ui-bootstrap@*" data-semver="1.3.3"
        src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/1.3.3/ui-bootstrap-tpls.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" type="text/css" href="{% static 'apps/lib/custom/sweetalert.css' %}">

    <!-- Theme style -->
    <link rel="stylesheet" href="{% static 'apps/lib/common/jquery-ui/themes/base/jquery-ui.min.css' %}">

    <link rel="stylesheet" href="{% static 'apps/lib/alte/dist/css/AdminLTE.min.css' %}">

    <link rel="stylesheet" href="{% static 'apps/lib/alte/dist/css/skins/_all-skins.min.css' %}">

    <link rel="stylesheet" href="{% static 'apps/lib/common/angular-ui-select/dist/select.min.css' %}">
    <link rel="stylesheet" href="{% static 'apps/stylesheet/gobolt.css' %}">
    <script src="{% static 'apps/lib/custom/sweetalert.min.js' %}"></script>
    <script src="{% static 'apps/lib/custom/csv.min.js' %}"></script>
    {% comment %} <script src="https://cdnjs.cloudflare.com/ajax/libs/alasql/0.3.7/alasql.min.js"></script> {% endcomment %}
    <script src="{% static 'cdn/js/alasql.min.js' %}"></script>
    {% comment %} <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.9.2/xlsx.core.min.js"></script> {% endcomment %}
    <script src="{% static 'cdn/js/xlsx.core.min.js' %}"></script>

    <script src="{% static 'apps/lib/alte/plugins/jQuery/jquery-2.2.3.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/jquery-ui/jquery-ui.min.js' %}"></script>
    <script src="{% static 'apps/lib/custom/jquery-clockpicker.min.js' %}"></script>
    <link rel="stylesheet" href="{% static 'apps/lib/custom/jquery-clockpicker.min.css' %}">
    <script type="text/javascript"
        src="https://maps.googleapis.com/maps/api/js?key={% google_api_key %}&libraries=geometry,places"></script>
    <script src="https://cdn.zingchart.com/zingchart.min.js"></script>
    <script> zingchart.MODULESDIR = "https://cdn.zingchart.com/modules/";
        ZC.LICENSE = ["569d52cefae586f634c54f86dc99e6a9", "ee6b7db5b51705a13dc2339db3edaf6d"];</script>
    <!-- AdminLTE Skins. We have chosen the skin-blue for this starter
              page. However, you can choose any other skin. Make sure you
              apply the skin class to the body tag so the changes take effect.

        -->
    <link rel="stylesheet" href="{% static 'apps/lib/alte/dist/css/skins/skin-blue.min.css' %}">
    <link rel="stylesheet" href="{% static 'apps/lib/alte/plugins/pace/pace.min.css' %}">
    <script src="{% static 'apps/lib/common/angular/angular.min.js' %}"></script>
    <link rel="stylesheet"
        href="{% static 'apps/lib/common/angularjs-datetime-picker/angularjs-datetime-picker.css' %}" />
    <script src="{% static 'apps/lib/common/angularjs-datetime-picker/angularjs-datetime-picker.js' %}"></script>
    <script src="{% static 'apps/lib/common/angular-ui-router/release/angular-ui-router.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/ngmap/build/scripts/ng-map.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/angular-cookies/angular-cookies.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/angular-sanitize/angular-sanitize.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/angular-ui-select/dist/select.min.js' %}"></script>
    <script src="{% static 'apps/lib/common/async/dist/async.js' %}"></script>
    <script src="{% static 'apps/lib/common/angularUtils-pagination/dirPagination.js' %}"></script>
    {#        <script src="{% static 'apps/lib/custom/auto_reload.js' %}"></script>#}
    <!--<script type="text/javascript">

                 var idleTime = 0;
                 $(document).ready(function () {
                     //Increment the idle time counter every minute.
                     var idleInterval = setInterval(timerIncrement, 60000); // 1 minute

                     //Zero the idle timer on mouse movement.
                     $(this).mousemove(function (e) {
                         idleTime = 0;
                     });
                     $(this).keypress(function (e) {
                         idleTime = 0;
                     });
                 });

                 function timerIncrement() {
                     idleTime = idleTime + 1;
                     if (idleTime > 2) { // 5 minutes
                         window.location.reload();
                     }
                 }
             </script>-->

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<!--
    BODY TAG OPTIONS:
    =================
    Apply one or more of the following classes to get the
    desired effect
    |---------------------------------------------------------|
    | SKINS         | skin-blue                               |
    |               | skin-black                              |
    |               | skin-purple                             |
    |               | skin-yellow                             |
    |               | skin-red                                |
    |               | skin-green                              |
    |---------------------------------------------------------|
    |LAYOUT OPTIONS | fixed                                   |
    |               | layout-boxed                            |
    |               | layout-top-nav                          |
    |               | sidebar-collapse                        |
    |               | sidebar-mini                            |
    |---------------------------------------------------------|
    -->

<body class="hold-transition skin-blue sidebar-mini">
    <div class="wrapper">

        <!-- Main Header -->
        <header class="main-header">
            <div class="web-view">
                <a href="




                        {% if request.user.is_superuser or request.user.is_staff %}{% url 'admin-login' %}{% elif request.user|can_planning_team %}{% url 'planning-dashboard' %}{% endif %}"
                    class="logo-new logo">

                    <span class="logo-mini"><img src="../static/apps/common/images/icon.png" /></span>

                    <span class="logo-lg"><img src="../static/apps/common/images/logo.png" /></span>
                </a>
            </div>


            <nav class="navbar navbar-new navbar-static-top" role="navigation">
                <div class="web-view">
                    <a href="#" class="sidebar-toggle sidebar-toggle-new" data-toggle="offcanvas" role="button">
                        <span class="sr-only">Toggle navigation</span>
                    </a>
                </div>

                <div class="web-view top-nav">
                    {% if request.user|can_planning_team or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/planning/"><span type="button" class="btn btn-info">Planning</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_sourcing_agent or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/market/"><span type="button" class="btn btn-info">Market</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_view_bilty or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/bilty/"><span type="button" class="btn btn-info">Bilty</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_account_manager or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/account/"><span type="button" class="btn btn-info">Account</span></a></li>
                    {% endif %}

                    {% if request.user|can_hub_manager or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/hub/"><span type="button" class="btn btn-info">Hub</span></a>
                    </li>
                    {% endif %}

                    {% if request.user|can_finance or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/finance/"><span type="button" class="btn btn-info">Finance</span></a></li>
                    {% endif %}

                    {% if request.user|can_compliance or request.user.is_superuser or request.user.is_staff %}
                    <li><a href="/compliance/"><span type="button" class="btn btn-info">Compliance</span></a>
                    </li>
                    {% endif %}
                    <!--                <li ><a href="/vehicle-demand/"><span type="button"-->
                    <!--                                                                            class="btn btn-info">Vehicle Demand</span></a>-->
                    <!--                        </li>-->

                    <li><a href="/analytics/"><span type="button" class="btn btn-info">Analytics</span></a>
                    </li>
                </div>


                <div class="navbar-custom-menu">



                    <ul class="nav navbar-nav web-view">

                        <!--                        <li class="dropdown notifications-menu " data-toggle="tooltip" data-placement="bottom"-->
                        <!--                            title="Unschedule Alerts">-->
                        <!--                            <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown"-->
                        <!--                               aria-expanded="false">-->
                        <!--                                <div ng-class="{'animated heartBeat infinite': badgeValue}">-->
                        <!--                                    <i class="fa fa-bell-o" ng-click="openAlertModal()">-->
                        <!--                                        <span class="label label-warning" ng-bind="badgeValue"></span>-->
                        <!--                                    </i>-->
                        <!--                                </div>-->
                        <!--                            </a>-->
                        <!--                            &lt;!&ndash;<ul class="dropdown-menu">-->
                        <!--                                <li class="header">You have <span ng-bind="badgeValue"></span> Unschedule stoppage</li>-->
                        <!--                                <li>-->

                        <!--                                    <ul class="menu">-->

                        <!--                                        <li ng-repeat="item in data_list">-->
                        <!--                                            <a href="javascript:void(0)" ng-click="openAlertBox(item)">-->
                        <!--                                                <i class="fa fa-warning text-yellow"></i> <span-->
                        <!--                                                    ng-bind="item.v_no"></span>-->
                        <!--                                            </a>-->
                        <!--                                        </li>-->
                        <!--                                    </ul>-->
                        <!--                                </li>-->
                        <!--                            </ul>&ndash;&gt;-->
                        <!--                        </li>-->
                        <!-- Notification -->
                        <li class="dropdown">
                            <a href="javascript:void(0)" class="bell-icon dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-bell" aria-hidden="true"></i>
                                <span class="notification-count" id="global-notification-count"></span>
                            </a>
                            <div class="dropdown-menu notification-menu">
                                <div class="notification-header" id="global-notification-header"> 0 New Notifications
                                </div>
                                <ul class="notification-list" id="global-notification-list">
                                </ul>
                                <a class="notification-view-btn" href="/notification">View All</a>
                            </div>
                        </li>
                        <!--/ Notification-->

                        <li class="dropdown user user-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="../static/apps/common/images/login-user.png" alt="user" />
                                <span class="hidden-xs">Hi {{ request.user.first_name }}</span>
                                <i class="fa fa-angle-down" aria-hidden="true"></i>
                            </a>
                            <ul class="dropdown-menu">

                                <li class="user-header">

                                    <p>
                                        Hi {{ request.user.first_name }}
                                        <small>Member since Oct. 2016</small>
                                    </p>
                                </li>

                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                    </div>

                                </li>

                                <li class="user-footer">
                                    <div class="pull-left">
                                        <a ui-sref="profile" class="btn btn-default btn-flat">Profile</a>
                                    </div>
                                    <div class="pull-right">
                                        <a href="{% url '12-logout-user' %}" class="btn btn-default btn-flat">Sign
                                            out</a>
                                    </div>
                                </li>
                            </ul>
                        </li>

                    </ul>

                    <!-- Control Sidebar Toggle Button -->

                    <ul class="nav navbar-nav mobile-view">

                        <li>
                            <a href="#" data-toggle="control-sidebar" class="pull-right"><i class="fa fa-list-ul"
                                    aria-hidden="true"></i></a>
                        </li>
                        <div style="float: left"><a
                                href="


                                {% if request.user.is_superuser or request.user.is_staff %}{% url 'admin-login' %}{% elif request.user|can_planning_team %}{% url 'planning-dashboard' %}{% endif %}"
                                class="cust-log logo-new logo"><img src="../static/apps/common/images/icon.png"
                                    style="height: 45px;  margin-left: 110px" /></a></div>

                        <li class="dropdown notifications-menu" style="float: right" data-toggle="tooltip"
                            data-placement="bottom" title="Unschedule Alerts">
                            <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown"
                                aria-expanded="false">
                                <div ng-class="{'animated heartBeat infinite': badgeValue}">
                                    <i class="fa fa-bell-o" ng-click="openAlertModal()">
                                    </i>
                                    <span class="label label-warning" ng-bind="badgeValue"></span>
                                </div>

                            </a>
                            <!--<ul class="dropdown-menu">
                                <li class="header">You have <span ng-bind="badgeValue"></span> Unschedule stoppage</li>
                                <li>
                                    <ul class="menu">

                                        <li ng-repeat="item in data_list">
                                            <a href="javascript:void(0)" ng-click="openAlertBox(item)">
                                                <i class="fa fa-warning text-yellow"></i> <span
                                                    ng-bind="item.v_no"></span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>-->
                        </li>

                        <li class="dropdown user user-menu" style="float: right">

                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="../static/apps/common/images/login-user.png" alt="user" />
                                <span class="logout-popup">Hi {{ request.user.first_name }}</span>
                                <i class="fa fa-angle-down" aria-hidden="true"></i>
                            </a>
                            <ul class="dropdown-menu">

                                <li class="user-header">

                                    <p>
                                        Hi {{ request.user.first_name }}
                                        <small>Member since Oct. 2016</small>
                                    </p>
                                </li>

                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                        <div class="col-xs-4 text-center">

                                        </div>
                                    </div>

                                </li>

                                <li class="user-footer">
                                    <div class="pull-left">
                                        <a ui-sref="profile" class="btn btn-default btn-flat">Profile</a>
                                    </div>
                                    <div class="pull-right">
                                        <a href="{% url '12-logout-user' %}" class="btn btn-default btn-flat">Sign
                                            out</a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <div class="mobile-view" style="float:left; margin-left: 0 ;max-width:350px;">
                        <ul  class="list-link" style="padding-left: 0;">
                            {% if request.user|can_planning_team or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/planning/"><span type="button"
                                        class="btn btn-info">Pln</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_sourcing_agent or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/market/"><span type="button"
                                        class="btn btn-info">Mrk</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_view_bilty or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/bilty/"><span type="button"
                                        class="btn btn-info">Blt</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_account_manager or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/account/"><span type="button"
                                        class="btn btn-info">Acc</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_hub_manager or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/hub/"><span type="button"
                                        class="btn btn-info">Hub</span></a>
                            </li>
                            {% endif %}

                            {% if request.user|can_compliance or request.user.is_superuser or request.user.is_staff %}
                            <li style=" margin: 4px 2px;"><a href="/compliance/"><span type="button"
                                        class="btn btn-info">Com</span></a>
                            </li>
                            {% endif %}

                            <!--                        <li ><a href="/vehicle-demand/"><span type="button"-->
                            <!--                                                                            class="btn btn-info">Vehicle Demand</span></a>-->
                            <!--                        </li>-->
                            <li style=" margin: 4px 2px;"><a href="/analytics/"><span type="button"
                                        class="btn btn-info">Ana</span></a>
                            </li>
                        </ul>
                    </div>


                </div>
            </nav>
        </header>

        <aside class="main-sidebar sidebar-new">


            <section class="sidebar">


                <ul class="sidebar-menu sidebar-menu-new">


                    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Trip Management</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="vehicle_tracking"><i
                                        class="fa fa-circle-o"></i>Vehicle
                                    Tracking</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="ongoing-trip"><i class="fa fa-circle-o"></i>On Going
                                    Trip</a></li>-->
                            <li ui-sref-active="active"><a ui-sref="completed-trip"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip</a></li>
                            <li ui-sref-active="active"><a ui-sref="completed-trip-history"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip History</a></li>
                            <li ui-sref-active="active"><a ui-sref="realtime-tracking-data"><i
                                        class="fa fa-circle-o"></i>Realtime Tracking Data</a></li>
                            <li ui-sref-active="active"><a ui-sref="gps-crossover"><i class="fa fa-circle-o"></i>Vehicle
                                    GPS Crossover</a></li>
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Alerts</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
<!--                            <li ui-sref-active="active"><a ui-sref="placement-assurance"><i-->
<!--                                        class="fa fa-circle-o"></i>Placement-->
<!--                                    Assurance</a></li>-->
<!--                            &lt;!&ndash;<li ui-sref-active="active"><a ui-sref="hub-waiting"><i class="fa fa-circle-o"></i>Hub-->
<!--                                    Waiting</a></li>&ndash;&gt;-->
<!--                            <li ui-sref-active="active"><a ui-sref="waiting-at-cwh"><i-->
<!--                                        class="fa fa-circle-o"></i>Waiting-->
<!--                                    at CWH</a></li>-->
                            <!-- <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage"><i
                                        class="fa fa-circle-o"></i>Unscheduled
                                    Stoppage</a></li> -->
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage-v2"><i
                                        class="fa fa-circle-o"></i>
                                    Unscheduled Stoppage</a></li>
                            <!-- <li ui-sref-active="active"><a ui-sref="detour-alert">
                                    <i class="fa fa-circle-o"></i>Detour Alerts</a>
                                </li> -->
                            <li ui-sref-active="active"><a ui-sref="detour-alert-v2"><i class="fa fa-circle-o"></i>
                                    Detour Alerts</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="pilferage-alert">
                                    <i class="fa fa-circle-o"></i>Pilferage Alerts</a>
                                </li>-->
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Analytics & Stats</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li ui-sref-active="active"><a ui-sref="completed-trip-analytics"><i
                                        class="fa fa-circle-o"></i>
                                    Fuel and Distance</a></li>
                        </ul>
                    </li>

                    <li class="treeview" ui-sref-active="active">
                        <a ui-sref="update-trip-time">
                            <i class="fa fa-link"></i>
                            <span>Change Trip Times</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </section>
        </aside>

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper content-wrapper-new custom-box">


            <!-- Main content -->
            <section class="content" ui-view="">

                <!-- Your Page Content Here -->

            </section>

        </div>

        <footer class="main-footer">

            <!--<div class="pull-right hidden-xs">
                Anything you want
            </div>-->

            <strong>Copyright &copy; 2017 <a href="#">Camions Logistics Solutions Pvt Ltd</a>.</strong> All rights
            reserved.
        </footer>
        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <section class="sidebar">


                <ul class="sidebar-menu sidebar-menu-new">


                    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Trip Management</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="vehicle_tracking"><i
                                        class="fa fa-circle-o"></i>Vehicle
                                    Tracking</a></li>
                            <!--<li ui-sref-active="active"><a ui-sref="ongoing-trip"><i class="fa fa-circle-o"></i>On Going
                                    Trip</a></li>-->
                            <li ui-sref-active="active"><a ui-sref="completed-trip"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip</a></li>
                            <li ui-sref-active="active"><a ui-sref="completed-trip-history"><i
                                        class="fa fa-circle-o"></i>Completed
                                    Trip History</a></li>
                        </ul>
                    </li>

                    <li class="treeview">
                        <a href="#"><i class="fa fa-link"></i> <span>Alerts</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">

                            <li ui-sref-active="active"><a ui-sref="placement-assurance"><i
                                        class="fa fa-circle-o"></i>Placement
                                    Assurance</a></li>
                            <li ui-sref-active="active"><a ui-sref="hub-waiting"><i class="fa fa-circle-o"></i>Hub
                                    Waiting</a></li>
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage"><i
                                        class="fa fa-circle-o"></i>Unscheduled
                                    Stoppage</a></li>
                            <li ui-sref-active="active"><a ui-sref="unscheduled-stoppage-v2"><i
                                        class="fa fa-circle-o"></i>
                                    Stoppage Alerts</a></li>

                            <li ui-sref-active="active"><a ui-sref="detour-alert-v2"><i class="fa fa-circle-o"></i>
                                    Route Deviation Alerts</a></li>
                            <li ui-sref-active="active"><a ui-sref="waiting-at-cwh"><i
                                        class="fa fa-circle-o"></i>Waiting
                                    at CWH</a></li>
                        </ul>
                    </li>

                    {% endif %}
                </ul>

            </section>
        </aside>
        <!-- /.control-sidebar -->
        <!-- Add the sidebar's background. This div must be placed
             immediately after the control sidebar -->
        <div class="control-sidebar-bg"></div>
    </div>
    <!-- ./wrapper -->

    <!-- REQUIRED JS SCRIPTS -->


    <!-- Bootstrap 3.3.6 -->
    <script src="{% static 'apps/lib/alte/bootstrap/js/bootstrap.min.js' %}"></script>
    <script src="https://www.gstatic.com/firebasejs/4.9.0/firebase.js"></script>
    <script data-require="ui-bootstrap@*" data-semver="1.3.3"
        src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/1.3.3/ui-bootstrap-tpls.min.js"></script>

    <script src="{% static 'apps/lib/alte/plugins/pace/pace.min.js' %}"></script>

    <!-- ----------Firebase Scripts--------- -->
    {% comment %} <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"
        integrity="sha512-rmZcZsyhe0/MAjquhTgiUcb4d9knaFc7b5xAfju483gbEXTkeJRUMIPk6s3ySZMYUHEcjKbjLjyddGWMrNEvZg=="
        crossorigin="anonymous"></script> {% endcomment %}
    <script src="{% static 'cdn/js/moment-2.27.0.min.js' %}"></script>
    
    <script src="https://www.gstatic.com/firebasejs/7.14.2/firebase.js"></script>
    <script src="https://cdn.firebase.com/libs/angularfire/2.3.0/angularfire.min.js"></script>
    <script src="{% static 'apps/common/notification/firebase.js' %}"></script>
    <script src="{% static 'apps/common/notification/globalNotification.js' %}"></script>
    <!----------------END---------->
    <!-- AdminLTE App -->
    <script src="{% static 'apps/lib/alte/dist/js/app.min.js' %}"></script>

    {% if request.user|can_tracking_agent or request.user.is_superuser or request.user.is_staff %}
    <!-- add here tracking agent -->

    <script src="{% static 'apps/gobolt_opc/tracking_team/modules.js' %}"></script>
    <script src="{% static 'apps/gobolt_opc/tracking_team/app.js' %}"></script>
    <script src="{% static 'apps/common/directive/headerfreeze.js' %}"></script>

    <script src="{% static 'apps/gobolt_opc/tracking_team/controller/tripMgt.js' %}"></script>
    <script src="{% static 'apps/gobolt_opc/tracking_team/controller/trip_time.js' %}"></script>
    <!--<script src="{% static 'apps/gobolt_opc/tracking_team/controller/tracking_controller.js' %}"></script>-->
    <script src="{% static 'apps/gobolt_opc/tracking_team/service/service.js' %}"></script>
    <script src="{% static 'apps/gobolt_opc/tracking_team/filter/filter.js' %}"></script>

    <!-- add here planning team -->
    {% endif %}

    <script src="{% static 'apps/common/directive/directives.js' %}"></script>
    <script src="{% static 'apps/common/directive/loading.js' %}"></script>
    <script src="{% static 'apps/common/directive/g_autocompletes.js' %}"></script>

    <script src="{% static 'apps/gobolt_admin/controller/profile_management/profileCtrl.js' %}"></script>
    <script src="{% static 'apps/gobolt_admin/service/service.js' %}"></script>
    <script src="{% static 'apps/common/services/master.js' %}"></script>
    <audio id="xyz" src="{% static 'apps/common/tones/alert_signal.mp3' %}"></audio>
    <audio id="noti" src="{% static 'apps/common/tones/noti.mp3' %}"></audio>
    <!-- Alerts Modal -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Stopped Vehicles</h4>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Vehicle Number</th>
                                    <th>Customer</th>
                                    <th>Origin</th>
                                    <th>Destination</th>
                                    <th>Current Location</th>
                                    <th>Delay Status</th>
                                    <th>Stopped Duration</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in data_list">
                                    <td ng-bind="$index + 1"></td>
                                    <td><span ng-bind="item.v_no"> </span></td>
                                    <td><span ng-bind="item.customer"></span></td>
                                    <td><span ng-bind="item.origin"></span></td>
                                    <td><span ng-bind="item.destination"></span></td>
                                    <td><span ng-bind="item.location.current_location"></span></td>
                                    <td><span ng-bind="item.final_delay | secondsToHM"></span></td>
                                    <td><span ng-bind="item.duration | secondsToHM"></span></td>
                                    <td><span data-toggle="tooltip" data-placement="top" title="Reason for delay">
                                            <button class="fa fa-commenting" ng-click="openAlertBox(item)">
                                            </button>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Update Reason Alerts Modal -->
    <div class="modal fade" id="updateReason" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Stopped Vehicles</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="reason_frm">
                        <div class="form-group">

                            <label class="col-sm-2 control-label">Select Reason</label>
                            <div class="col-md-4"
                                ng-class="{ 'has-error' : reason_frm.rsn.$invalid && !reason_frm.rsn.$pristine }">
                                <ui-select name="rsn" data-ng-model="item.reason" ng-required="true" theme="">
                                    <ui-select-match placeholder="Reason...">
                                        <span ng-bind="$select.selected.reason"></span></ui-select-match>
                                    <ui-select-choices repeat="reason in reasons | filter: $select.search">
                                        <div ng-bind-html="reason.reason | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="reason_frm.rsn.$dirty && reason_frm.rsn.$error.required" class="help-block">
                                    Reason is required.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="clear-button btn btn-default"
                                data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary apply-button" data-dismiss="modal"
                                ng-disabled="reason_frm.$invalid" ng-click="submitReason(item)">Submit
                            </button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {

            {% for message in messages %}


            {% if message.tags == 'error' %}

            alert('{{ message }}!');
            {% elif message.tags == 'success' %}
            alert('{{ message }}!');
            {% endif %}
            {% endfor %}
        });
    </script>


    <!-- Optionally, you can add Slimscroll and FastClick plugins.
         Both of these plugins are recommended to enhance the
         user experience. Slimscroll is required when using the
         fixed layout. -->

    <script>
        (function () {

            var gobolt = angular.module('myApp');

            gobolt.controller('stateChangeCtrls', ['$scope', '$state', '$http', '$timeout', function ($scope, $state, $http, $timeout) {
                $scope.changeState = changeState;

                function changeState() {
                    $state.go('vehicle_tracking');
                };

                $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));
                $scope.badgeValue = localStorage.getItem("badgeValue");

                $scope.openAlertModal = function () {
                    if ($scope.badgeValue) {
                        $('#myModal').modal();
                        $('#myModal').on('shown.bs.modal', function () {
                            $(document).off('focusin.modal');
                        });
                    }
                    else {
                        swal("You have 0 stopped vehicles.", "", "warning")
                    }

                    var url = '/gobolt/trip/un-sch-reasons/';
                    $http.get(url).then(function (return_data) {
                        console.log('reason_list', return_data);
                        if (return_data.data.code.status === 200) {
                            $scope.reasons = return_data.data.reason_list;
                        }
                    })



                };

                try {
                    /**
                     * Firebase configuration
                     * Written By: Prafull
                     * Dated: 11/06/2019
                     */
                    $scope.buzzSiren = false;

                    var firebaseConfig = {
                        apiKey: "AIzaSyAFKI5wFobjhhrgSOFJLQf4N9vAD7CF724",
                        authDomain: "applied-tractor-172109.firebaseapp.com",
                        databaseURL: "https://applied-tractor-172109.firebaseio.com",
                        projectId: "applied-tractor-172109",
                        storageBucket: "applied-tractor-172109.appspot.com",
                        messagingSenderId: "1020989094457",
                        appId: "1:1020989094457:web:a7e7a0b76304a23d"
                    };

                    // Initialize Firebase
                    firebase.initializeApp(firebaseConfig);
                    const messaging = firebase.messaging();
                    messaging.requestPermission()
                        .then(function () {
                            console.log('Have Permission To Access Firebase.');
                            return messaging.getToken()
                        })
                        .then(function (token) {
                            console.log('Token Value', token);
                            var token_obj = {
                                'token': token
                            };
                            var url = '/gobolt/trip/reg-web-device/';
                            $http.post(url, token_obj).then(function (return_data) {
                                console.log(return_data);
                                if (return_data.data.code.status !== 200) {
                                    swal("FCM Token not registered. Unable to send notifications.", "", "error")
                                }
                            })

                        })
                        .catch(function (reason) {
                            console.log('Error', reason)
                        });



                    messaging.onMessage(function (payLoad) {
                        console.log('onMessage', payLoad);
                        if (payLoad) {
                            if (payLoad.data.alert) {
                                $scope.data_list = [];


                                // now call post api and get data there vehicle
                                var url = '/gobolt/trip/notification/get_data/';
                                $http.post(url, payLoad.data.data_list).then(function (return_data) {


                                    // localStorage block


                                    //set badge value
                                    localStorage.setItem("badgeValue", return_data.data.data.length);
                                    console.log(return_data);
                                    var str_obj = payLoad.data.data_list;
                                    $scope.data_list = eval('(' + str_obj + ')');

                                    localStorage.setItem("alertDataList", JSON.stringify(return_data.data.data));
                                    $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));
                                    $scope.badgeValue = localStorage.getItem("badgeValue");
                                    if ($scope.badgeValue === 0) {
                                        document.getElementById('noti').pause();
                                        swal("Updated!", "All alerts updated successfully.", "success")
                                    }
                                    else {
                                        swal({
                                            title: "Stop alert!",
                                            text: "Vehicles have been stopped.",
                                            type: "warning",
                                            timer: 300
                                        },
                                            function () {
                                                // document.getElementById('xyz').play().loop = true;
                                                document.getElementById('noti').play()
                                            });
                                    }

                                });
                                //  document.getElementById('xyz').play().loop = true;



                            }
                        }
                    });
                }
                catch (err) {
                    console.log(err)
                }


                //  $scope.badgeValue = $scope.data_list.length;

                $scope.changeState();

                $scope.openAlertBox = function (item) {
                    $scope.index = $scope.data_list.indexOf(item);
                    $scope.item = item;
                    console.log('$scope.index', $scope.index);
                    $('#updateReason').modal();
                    $('#updateReason').on('shown.bs.modal', function () {
                        $(document).off('focusin.modal');
                    });
                    /**
                     * swal({
                            title: "Vehicle number " + item.v_no + " is stopped for " + item.duration / 60 + 'Min.',
                            text: "Current location is " + item.location.current_location,
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "Yes, update it!",
                            cancelButtonText: "No, cancel !",
                            closeOnConfirm: false,
                            closeOnCancel: false
                        },
                     function (isConfirm) {
                            if (isConfirm) {
                                swal({
                                        title: "Enter!",
                                        text: "Reason for delay:",
                                        type: "input",
                                        showCancelButton: true,
                                        closeOnConfirm: false,
                                        animation: "slide-from-top",
                                        inputPlaceholder: "Reason"
                                    },
                                    function (inputValue) {
                                        if (inputValue === false) return false;

                                        var inputV = inputValue.trim();
                                        if (inputV === "") {
                                            swal.showInputError("You need to write the reason!");
                                            return false
                                        }
                                        $scope.data_list.splice($scope.index, 1);
                                        $scope.badgeValue = $scope.data_list.length;
                                        console.log('data_list', $scope.data_list)
                                        var url = '/gobolt/trip/update-unschedule-via-noti/'
                                        var data_obj = {
                                            'item': item,
                                            'return_data_list': $scope.data_list,
                                            'reason': inputV
                                        };
                                        $http.post(url, data_obj).then(function (return_data) {
                                            console.log(return_data);
                                            if (return_data.data.code.status === 200) {
                                                $scope.data_list = return_data.data.data_list
                                                $scope.badgeValue = $scope.data_list.length;
                                                swal("Updated!", "Your data has been updated", "success")
                                            }
                                        });
                                        // biltyServices.rejectTrip(id, inputV).then(function (response) {
                                        //     if (response.data.code.status === 200) {
                                        //         $scope.orderAssigns.splice(index, 1);
                                        //         swal("Deleted!", "Your data has been deleted.", "success");
                                        //     }
                                        //     else {
                                        //         swal("Can't Cancel!", response.data.code.message, "error")
                                        //     }
                                        // })
                                        if ($scope.badgeValue === 0) {
                                            $('#myModal').modal('hide');
                                            document.getElementById('xyz').pause()
                                        }
                                        try {
                                            $scope.$apply()
                                        }
                                        catch (ew) {

                                        }

                                    })
                            } else {
                                swal("Cancelled", "Your data is safe :)", "error");
                            }
                        });
                     */

                };


                $scope.submitReason = function (item) {
                    $scope.data_list.splice($scope.index, 1);

                    //set updated badge value
                    localStorage.setItem("badgeValue", $scope.data_list.length);

                    //get updated stored badge value
                    $scope.badgeValue = localStorage.getItem("badgeValue");

                    console.log('reason')
                    var url = '/gobolt/trip/update-unschedule-via-noti/'
                    var data_obj = {
                        'item': item,
                        'return_data_list': $scope.data_list,
                        'reason': item.reason.reason
                    };
                    $http.post(url, data_obj).then(function (return_data) {
                        console.log(return_data);
                        if (return_data.data.code.status === 200) {

                            // Update data_list in localstorage
                            localStorage.setItem("alertDataList", JSON.stringify(return_data.data.data_list));

                            // Get data list from localstorage
                            $scope.data_list = JSON.parse(localStorage.getItem("alertDataList"));

                            // Update badgeValue in localstorage
                            localStorage.setItem("badgeValue", $scope.data_list.length);

                            // Get badgeValue from localstorage
                            $scope.badgeValue = localStorage.getItem("badgeValue");
                            swal("Updated!", "Your data has been updated", "success")
                        }
                    });
                }


            }])
        })();

    </script>
    <script>
        var pic = "{{ pic }}";
    </script>
    <!-- The core Firebase JS SDK is always required and must be listed first -->
    <script src="https://www.gstatic.com/firebasejs/6.1.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/6.1.1/firebase-messaging.js"></script>


    <!-- TODO: Add SDKs for Firebase products that you want to use
         https://firebase.google.com/docs/web/setup#config-web-app -->
</body>
{% endif %}

</html>