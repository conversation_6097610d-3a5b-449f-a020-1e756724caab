import logging
import uuid
from customer_care.models import GoboltBusinessApp, GoboltBusinessTripDocuments
from customer_care.models import Order
from datetime import datetime, timezone, date, time
from trip.utils import round_decimals_up
from trip.py_mongo_service import pymongo_crud
from finance.models import FinancePayable
from gobolt_business.utils import convert_hr_to_minutes

logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)


class GoboltBusinessUpdate:

    def update_payment_due(self, order_obj, payable_payment_due, payment_status=None):
        try:
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    if payment_status:
                        gobolt_app_obj.payment_status = payment_status
                    gobolt_app_obj.balance_due = payable_payment_due
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business payment due - {str(e)}", exc_info=True)

    def update_broker_advance_payment(self, order_obj, broker_advance):
        try:
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.broker_advance = broker_advance
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business broker advance - {str(e)}", exc_info=True)

    def update_broker_fuel_advance(self, order_obj, fuel_advance):
        try:
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.fuel_advance = fuel_advance
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business broker fuel advance - {str(e)}", exc_info=True)

    def update_pod_status(self, order_id, pod_status):
        try:
            order_obj = Order.objects.get(id=order_id)
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.pod_status = pod_status
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business pod status update - {str(e)}", exc_info=True)

    def pod_data_update(self, trip_obj):
        try:
            order_obj = Order.objects.get(id=trip_obj.order_id)
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.pod_status = trip_obj.pod_data["pod_status"]
                    gobolt_app_obj.front_pod_pic = trip_obj.pod_data.get("front_pod_pic")
                    gobolt_app_obj.back_pod_pic = trip_obj.pod_data.get("back_pod_pic")
                    gobolt_app_obj.pod_reject_remark = None
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business pod update - {str(e)}", exc_info=True)

    def reject_pod(self, trip_obj, order_obj, remark):
        try:
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.pod_status = "Rejected"
                    gobolt_app_obj.front_pod_pic = None
                    gobolt_app_obj.back_pod_pic = None
                    gobolt_app_obj.pod_reject_remark = remark
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while bololt business reject pod - {str(e)}", exc_info=True)

    def eta_final_delay_duration_update(self, trip_obj, delay_status_backend):
        try:
            eta_delay_duration = convert_hr_to_minutes(delay_status_backend)
            LOGGER.info(f"eta_final_delay_duration_update trip - {trip_obj['order_id']} delay_status_backend - {delay_status_backend}")
            LOGGER.info(f"eta_final_delay_duration_update trip - {trip_obj['order_id']} eta_delay_duration - {eta_delay_duration}")
            order_obj = Order.objects.get(id=trip_obj["order_id"])
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.eta_delay_duration = eta_delay_duration
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business eta delay duration update - {str(e)}", exc_info=True)

    def gobolt_business_update_trip_documents(self, order_id, documents_data, user_name):
        try:
            order_obj = Order.objects.filter(id=order_id).first()
            invoice = dict(
                uuid=str(uuid.uuid4()),
                document_size=1,
                document_name="invoice",
                document_link=documents_data.get('front_invoice'),
                lr_number=None,
                e_way_bill_number=None,
                invoice_number=documents_data.get('invoice_number'),
                created_at=datetime.now(timezone.utc).isoformat(),
                created_by=user_name
            )
            e_way_bill = dict(
                uuid=str(uuid.uuid4()),
                document_size=1,
                document_name="e_way_bill",
                document_link=documents_data["e_way_bill_data"]["pic_url"],
                lr_number=None,
                e_way_bill_number=documents_data["e_way_bill_data"]["e_way_bill_number"],
                invoice_number=None,
                created_at=datetime.now(timezone.utc).isoformat(),
                created_by=user_name
            )
            invoice_back = dict(
                uuid=str(uuid.uuid4()),
                document_size=1,
                document_name="invoice_back",
                document_link=documents_data.get('back_invoice'),
                lr_number=None,
                e_way_bill_number=None,
                invoice_number=None,
                created_at=datetime.now(timezone.utc).isoformat(),
                created_by=user_name
            )
            new_documents = [invoice, e_way_bill, invoice_back]
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.updated_by = user_name
                    gobolt_app_obj.updated_at = datetime.now(timezone.utc).isoformat()
                    gobolt_app_obj.save()
                    documents = GoboltBusinessTripDocuments.objects.using("gobolt_business").filter(
                        trip_id=gobolt_app_obj.id
                    ).all()
                    if documents:
                        documents_name_list = [x.get("document_name") for x in new_documents]
                        LOGGER.info(f"update_documents documents_name_list - {documents_name_list}")
                        for doc in documents:
                            if doc.document_name in documents_name_list:
                                doc.delete()
                    for doc in new_documents:
                        doc["trip_id"] = gobolt_app_obj.id
                        LOGGER.info(f"gobolt_business_update_trip_documents doc ------------{doc}")
                        GoboltBusinessTripDocuments.objects.using("gobolt_business").create(**doc)

        except Exception as e:
            LOGGER.info(f"Exception while update bololt business app details - {str(e)}",
                        exc_info=True)

    def update_trip_datetime(self, trip_obj, vehicle_placement_datetime, trip_start_datetime,
                             trip_end_datetime, unloading_datetime):
        try:
            order_obj = Order.objects.get(id=trip_obj.order_id)
            if order_obj.float_to_app is True:
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if gobolt_app_obj:
                    gobolt_app_obj.vehicle_placement_datetime = vehicle_placement_datetime
                    gobolt_app_obj.trip_start_datetime = trip_start_datetime
                    if trip_end_datetime:
                        gobolt_app_obj.vehicle_reported_datetime = trip_end_datetime
                    if unloading_datetime:
                        gobolt_app_obj.trip_end_datetime = unloading_datetime
                    gobolt_app_obj.save()
        except Exception as e:
            LOGGER.info(f"Exception while update bololt business trip datetime - {str(e)}", exc_info=True)


def gobolt_business_in_transit_data():
    order_object = Order.objects.filter(current_status__in=["Trip Started", "Vehicle Reported"],
                                        float_to_market=True,
                                        float_to_app=False)
    LOGGER.info(f"gobolt_business_in_transit_data order_object len - {len(order_object)}")
    if order_object:
        for order_obj in order_object:
            trip = pymongo_crud.db["trip"]
            query = {"order_id": order_obj.id}
            trip_obj = trip.find_one(query)
            if trip_obj:
                vehicle_req_datetime = datetime.combine(
                    order_obj.indent.date_vehicle_required,
                    order_obj.indent.time_vehicle_required,
                )
                if trip_obj.get("trip_start_date"):
                    trip_start_date = trip_obj.get("trip_start_date")
                    trip_start_time = trip_obj.get("trip_start_time")
                    if isinstance(trip_start_date, str):
                        trip_start_date = date.fromisoformat(trip_start_date)
                    elif isinstance(trip_start_date, datetime):
                        trip_start_date = trip_start_date.date()

                    if isinstance(trip_start_time, str):
                        trip_start_time = time.fromisoformat(trip_start_time)
                    elif isinstance(trip_start_time, datetime):
                        trip_start_time = trip_start_time.time()

                    trip_start_datetime = datetime.combine(
                        trip_start_date,
                        trip_start_time,
                    )
                else:
                    trip_start_datetime = None

                if trip_obj.get("vehicle_placement_date"):
                    vehicle_placement_date = trip_obj.get("vehicle_placement_date")
                    vehicle_placement_time = trip_obj.get("vehicle_placement_time")
                    if isinstance(vehicle_placement_date, str):
                        vehicle_placement_date = date.fromisoformat(vehicle_placement_date)
                    elif isinstance(vehicle_placement_date, datetime):
                        vehicle_placement_date = vehicle_placement_date.date()

                    if isinstance(vehicle_placement_time, str):
                        vehicle_placement_time = time.fromisoformat(vehicle_placement_time)
                    elif isinstance(vehicle_placement_time, datetime):
                        vehicle_placement_time = vehicle_placement_time.time()

                    vehicle_placement_datetime = datetime.combine(
                        vehicle_placement_date,
                        vehicle_placement_time,
                    )
                else:
                    vehicle_placement_datetime = None

                if trip_obj.get("trip_end_date"):
                    trip_end_date = trip_obj.get("trip_end_date")
                    trip_end_time = trip_obj.get("trip_end_time")
                    if isinstance(trip_end_date, str):
                        trip_end_date = date.fromisoformat(trip_end_date)
                    elif isinstance(trip_end_date, datetime):
                        trip_end_date = trip_end_date.date()

                    if isinstance(trip_end_time, str):
                        trip_end_time = time.fromisoformat(trip_end_time)
                    elif isinstance(trip_end_time, datetime):
                        trip_end_time = trip_end_time.time()

                    trip_end_datetime = datetime.combine(
                        trip_end_date,
                        trip_end_time,
                    )
                else:
                    trip_end_datetime = None
                LOGGER.info(f"trip_start_datetime - {trip_start_datetime}")
                LOGGER.info(f"vehicle_placement_datetime - {vehicle_placement_datetime}")
                LOGGER.info(f"trip_end_datetime - {trip_end_datetime}")
                if order_obj.current_status == "Trip Started":
                    status = "IN_TRANSIT"
                else:
                    status = "REPORTED"
                _tds = float(order_obj.tds) if order_obj.tds else 0
                _broker_rate = float(order_obj.broker_rate) if order_obj.broker_rate else 0
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if not gobolt_app_obj:
                    gb_obj = GoboltBusinessApp.objects.using("gobolt_business").create(
                        broker_id=order_obj.broker_id,
                        broker_code=order_obj.broker.broker_code,
                        broker_name=order_obj.broker.broker_name,
                        driver_code=order_obj.driver.driver_code,
                        driver_name=order_obj.driver.driver_name,
                        driver_mobile_number=order_obj.driver.mobile_number,
                        vehicle_number=order_obj.vehicle.vehicle_registration_number,
                        vehicle_code=order_obj.vehicle.code,
                        order_code=order_obj.order_code,
                        order_id=order_obj.id,
                        origin_name=order_obj.indent.start_point['location_name'],
                        origin_code=order_obj.indent.start_point['location_code'],
                        destination_name=order_obj.indent.destination_point['location_name'],
                        destination_code=order_obj.indent.destination_point['location_code'],
                        customer_name=order_obj.indent.customer.customer_name,
                        customer_code=order_obj.indent.customer.customer_code,
                        status=status,
                        order_status=order_obj.current_status,
                        payment_status=trip_obj.get("payment_status", {}).get("payment_status_name"),
                        broker_rate=_broker_rate,
                        tat=order_obj.indent.contract.tat,
                        total_distance=order_obj.indent.contract.lane_distance,
                        vehicle_required_datetime=vehicle_req_datetime,
                        vehicle_type=order_obj.indent.vehicle_type.name,
                        created_by="ops",
                        created_at=datetime.now(),
                        updated_by="ops",
                        updated_at=datetime.now(),
                        ops_assigned_vehicle=False,
                        tds=round_decimals_up((_broker_rate * _tds) / 100),
                        trip_id=str(trip_obj.get("_id")),
                        trip_code=trip_obj.get("trip_code"),
                        pod_status=trip_obj["pod_data"].get("pod_status"),
                        vehicle_placement_datetime=vehicle_placement_datetime,
                        trip_start_datetime=trip_start_datetime,
                        trip_end_datetime=trip_end_datetime,
                        trip_status=trip_obj["trip_status"]["status_name"],
                    )
                    gobolt_business_update_documents(gb_obj, trip_obj)
                order_obj.float_to_app = True
                order_obj.save()


def gobolt_business_completed_data():
    order_object = Order.objects.filter(current_status="Completed",
                                        float_to_market=True,
                                        float_to_app=False,
                                        # broker__broker_code='VND01718',
                                        created_date_time__gte="2024-01-01 00:00:00.000000")
    # order_object = Order.objects.filter(order_id="O|RIVI|23|0422|353580")
    order_ids = [x.id for x in order_object]
    LOGGER.info(f"gobolt_business_in_transit_data order_object len - {len(order_object)}")
    LOGGER.info(f"gobolt_business_in_transit_data order_ids - {order_ids}")
    if order_object:
        for order_obj in order_object:
            trip = pymongo_crud.db["trip"]
            query = {"order_id": order_obj.id}
            trip_obj = trip.find_one(query)
            if trip_obj:
                vehicle_req_datetime = datetime.combine(
                    order_obj.indent.date_vehicle_required,
                    order_obj.indent.time_vehicle_required,
                )
                if trip_obj.get("trip_start_date"):
                    trip_start_date = trip_obj.get("trip_start_date")
                    trip_start_time = trip_obj.get("trip_start_time")
                    if isinstance(trip_start_date, str):
                        trip_start_date = date.fromisoformat(trip_start_date)
                    elif isinstance(trip_start_date, datetime):
                        trip_start_date = trip_start_date.date()

                    if isinstance(trip_start_time, str):
                        trip_start_time = time.fromisoformat(trip_start_time)
                    elif isinstance(trip_start_time, datetime):
                        trip_start_time = trip_start_time.time()

                    trip_start_datetime = datetime.combine(
                        trip_start_date,
                        trip_start_time,
                    )
                else:
                    trip_start_datetime = None

                if trip_obj.get("vehicle_placement_date"):
                    vehicle_placement_date = trip_obj.get("vehicle_placement_date")
                    vehicle_placement_time = trip_obj.get("vehicle_placement_time")
                    if isinstance(vehicle_placement_date, str):
                        vehicle_placement_date = date.fromisoformat(vehicle_placement_date)
                    elif isinstance(vehicle_placement_date, datetime):
                        vehicle_placement_date = vehicle_placement_date.date()

                    if isinstance(vehicle_placement_time, str):
                        vehicle_placement_time = time.fromisoformat(vehicle_placement_time)
                    elif isinstance(vehicle_placement_time, datetime):
                        vehicle_placement_time = vehicle_placement_time.time()

                    vehicle_placement_datetime = datetime.combine(
                        vehicle_placement_date,
                        vehicle_placement_time,
                    )
                else:
                    vehicle_placement_datetime = None

                if trip_obj.get("trip_end_date"):
                    trip_end_date = trip_obj.get("trip_end_date")
                    trip_end_time = trip_obj.get("trip_end_time")
                    if isinstance(trip_end_date, str):
                        trip_end_date = date.fromisoformat(trip_end_date)
                    elif isinstance(trip_end_date, datetime):
                        trip_end_date = trip_end_date.date()

                    if isinstance(trip_end_time, str):
                        trip_end_time = time.fromisoformat(trip_end_time)
                    elif isinstance(trip_end_time, datetime):
                        trip_end_time = trip_end_time.time()

                    trip_end_datetime = datetime.combine(
                        trip_end_date,
                        trip_end_time,
                    )
                else:
                    trip_end_datetime = None
                if trip_obj.get("unloading_date"):
                    unloading_date = trip_obj.get("trip_end_date")
                    unloading_time = trip_obj.get("unloading_time")
                    if isinstance(unloading_date, str):
                        unloading_date = date.fromisoformat(unloading_date)
                    elif isinstance(unloading_date, datetime):
                        unloading_date = unloading_date.date()

                    if isinstance(unloading_time, str):
                        unloading_time = time.fromisoformat(unloading_time)
                    elif isinstance(unloading_time, datetime):
                        unloading_time = unloading_time.time()

                    unloading_datetime = datetime.combine(
                        unloading_date,
                        unloading_time,
                    )
                else:
                    unloading_datetime = None
                LOGGER.info(f"trip_start_datetime - {trip_start_datetime}")
                LOGGER.info(f"vehicle_placement_datetime - {vehicle_placement_datetime}")
                LOGGER.info(f"trip_end_datetime - {trip_end_datetime}")
                fp_object = FinancePayable.objects.filter(trip_id=str(trip_obj.get("_id"))).first()
                _tds = float(order_obj.tds) if order_obj.tds else 0
                _broker_rate = float(order_obj.broker_rate) if order_obj.broker_rate else 0
                gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                    order_code=order_obj.order_code
                ).exclude(status="CANCELED").first()
                if not gobolt_app_obj:
                    gb_obj = GoboltBusinessApp.objects.using("gobolt_business").create(
                        broker_id=order_obj.broker_id,
                        broker_code=order_obj.broker.broker_code,
                        broker_name=order_obj.broker.broker_name,
                        driver_code=order_obj.driver.driver_code,
                        driver_name=order_obj.driver.driver_name,
                        driver_mobile_number=order_obj.driver.mobile_number,
                        vehicle_number=order_obj.vehicle.vehicle_registration_number,
                        vehicle_code=order_obj.vehicle.code,
                        order_code=order_obj.order_code,
                        order_id=order_obj.id,
                        origin_name=order_obj.indent.start_point['location_name'],
                        origin_code=order_obj.indent.start_point['location_code'],
                        destination_name=order_obj.indent.destination_point['location_name'],
                        destination_code=order_obj.indent.destination_point['location_code'],
                        customer_name=order_obj.indent.customer.customer_name,
                        customer_code=order_obj.indent.customer.customer_code,
                        status="COMPLETED",
                        order_status=order_obj.current_status,
                        payment_status=trip_obj.get("payment_status", {}).get("payment_status_name"),
                        broker_rate=_broker_rate,
                        tat=order_obj.indent.contract.tat,
                        total_distance=order_obj.indent.contract.lane_distance,
                        vehicle_required_datetime=vehicle_req_datetime,
                        vehicle_type=order_obj.indent.vehicle_type.name,
                        created_by="ops",
                        created_at=datetime.now(),
                        updated_by="ops",
                        updated_at=datetime.now(),
                        ops_assigned_vehicle=False,
                        tds=round_decimals_up((_broker_rate * _tds) / 100),
                        trip_id=str(trip_obj.get("_id")),
                        trip_code=trip_obj.get("trip_code"),
                        pod_status=trip_obj["pod_data"].get("pod_status"),
                        vehicle_placement_datetime=vehicle_placement_datetime,
                        trip_start_datetime=trip_start_datetime,
                        trip_end_datetime=unloading_datetime,
                        vehicle_reported_datetime=trip_end_datetime,
                        trip_status=trip_obj["trip_status"]["status_name"],
                        balance_due=fp_object.payment_due,
                        broker_advance=order_obj.broker_advance,
                        fuel_advance=order_obj.fuel_advance,
                        front_pod_pic=trip_obj["pod_data"]["front_pod_pic"],
                        back_pod_pic=trip_obj["pod_data"]["back_pod_pic"],
                    )
                    gobolt_business_update_documents(gb_obj, trip_obj)
                order_obj.float_to_app = True
                order_obj.save()


def gobolt_business_update_documents(gb_obj, trip_obj):
    if trip_obj["invoice_pic"].get("front_invoice"):
        invoice = dict(
            trip_id=gb_obj.id,
            uuid=str(uuid.uuid4()),
            document_size=1,
            document_name="invoice",
            document_link=trip_obj["invoice_pic"].get("front_invoice"),
            lr_number=None,
            e_way_bill_number=None,
            invoice_number=None,
            created_at=datetime.now(timezone.utc).isoformat(),
            created_by="ops"
        )
        GoboltBusinessTripDocuments.objects.using("gobolt_business").create(**invoice)
    if trip_obj["invoice_pic"].get("back_invoice"):
        invoice_back = dict(
            trip_id=gb_obj.id,
            uuid=str(uuid.uuid4()),
            document_size=1,
            document_name="invoice_back",
            document_link=trip_obj["invoice_pic"].get("back_invoice"),
            lr_number=None,
            e_way_bill_number=None,
            invoice_number=None,
            created_at=datetime.now(timezone.utc).isoformat(),
            created_by="ops"
        )
        GoboltBusinessTripDocuments.objects.using("gobolt_business").create(**invoice_back)


