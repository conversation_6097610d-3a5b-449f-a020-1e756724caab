""" Written by <PERSON>  """
# Import here
import logging
from django.db.models import Q as Qp
import csv
import uuid
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required, user_passes_test
from administrator.models import UserManagement, City, LocationManagement
from django.shortcuts import render_to_response, render
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse
from django.utils import timezone
from broker.models import Broker, BrokerAccount, BrokerRateChangeLog
from customer_care.models import Customer, Contract, ContractByMonth, Indent, Order, IndentOrderLogs, GoboltBusinessApp
from vehicle.models import VehicleType, Vehicles
import json
from trip import external_methods, global_objects
from trip.auto_data_creation import create_code, create_code_location
from customer_care.serializers import CustomerDataSerializer
from rest_framework_mongoengine.serializers import DocumentSerializer
from registration.models import Driver
import datetime
from account_manager import code_veriable
from finance.models import FinanceData, FinancePayable, TransactionLogs, TripRecovery, TripAdvanceRecovery
from mongoengine.queryset.visitor import Q
from finance.finance_external_method import payment_due_calculation, ReturnDueDate
from administrator.serializers import UserManagementSerializer
from vehicle.models import EntityPayments
import codecs
from trip.models import TripStatusMaster
from fuel_pump_module.fuel_pump import FuelPump
from django.db import transaction
from trip.py_mongo_service import pymongo_crud
from bson.objectid import ObjectId
from trip.utils import round_decimals_up, floor_round_decimals

logging.basicConfig(level=logging.DEBUG)
LOGGER = logging.getLogger(__name__)


# Serializer
class LocatioDataSerializer(DocumentSerializer):
    class Meta:
        model = LocationManagement
        fields = ('id', 'location_code', 'location_name', 'location_point')


broker_header_field = ['first_name', 'last_name', 'mobile_number', 'company', 'broker_code', 'tds_structure',
                       'address', 'location_broker', 'percentage', 'kyc_status', 'account_list', 'email']

customer_header_field = ['first_name', 'last_name', 'company_name', 'customer_code', 'phone_number', 'address',
                         'account_manager', 'bilty_boy', 'sourcing_agent', 'email']

location_header_field = ['location_name', 'location_address', 'location_pin', 'location_state', 'city_code',
                         'location_radius', 'lat_long', 'is_hub', 'c_location', 'is_toll_plaza',
                         'is_refuel', 'is_route', 'customer']

contract_header_field = ['customer', 'start_point', 'destination_point', 'customer_detention_rate',
                         'broker_detention_rate', 'contract_rate',
                         'tat', 'loading_charges', 'unloading_charges', 'vehicle_type', 'billing_cycle']

monthly_contract_header_field = ['customer', 'start_point', 'destination_point', 'customer_detention_rate',
                                 'broker_detention_rate', 'contract_rate',
                                 'tat', 'loading_charges', 'unloading_charges', 'vehicle_type', 'billing_cycle',
                                 'new_contract_rate', 'month', 'year']

market_driver_header_field = ['driver_name', 'driver_no', 'driver_father', 'driver_add', 'driver_dl_no',
                              'driver_dl_ex_date']
market_vehicle_header_field = ['vehicle_no', 'vehicle_type', 'manufacture_date', 'insurance_plc_no', 'insurance_s_date',
                               'insurance_e_date', 'fitness_c_no', 'fitness_s_date', 'fitness_e_date', 'permit_no',
                               'vehicle_owner_name', 'vehicle_owner_pan_card', 'vehicle_owner_address',
                               'vehicle_owner_contact_no']
market_vehicle_header_field_data = ['id', 'vehicle_no', 'vehicle_type', 'insurance_data', 'fitness_data', 'rc_data',
                                    'permit_data', 'vehicle_owner_data']

market_driver_header_field_data = ['id', 'driver_name', 'driver_no', 'driver_father', 'driver_add', 'driver_dl_no',
                                   'driver_dl_ex_date']
vehicle_type_header_field_data = ['id', 'name', 'is_delete']

indent_create_header_field_data = ['customer', 'vehicle_type', 'start_point', 'destination_point',
                                   'vehicle_required_on_date', 'vehicle_required_on_time', 'no_of_vehicle',
                                   'order_date', 'order_time', 'is_float_to_market', 'vehicle_no', 'driver_no',
                                   'broker_code', 'broker_rate', 'cash_advance', 'broker_advance', 'deductions',
                                   'broker_account',
                                   'vehicle_placement_date', 'vehicle_placement_time', 'lr_no', 'start_trip_date',
                                   'start_trip_time', 'payment_ref_cash', 'payment_ref_broker', 'reporting_date',
                                   'reporting_time', 'unloading_date', 'unloading_time', 'pod_tat',
                                   'pod_detentions_days', 'pod_reporting_date', 'pod_reporting_time',
                                   'pod_unloading_date', 'pod_unloading_time', 'pod_customer_taxes', 'pod_customer_tds',
                                   'pod_customer_loading_charges', 'pod_customer_unloading_charges',
                                   'pod_customer_damage', 'pod_customer_detentions', 'pod_customer_damage_comment',
                                   'pod_broker_taxes', 'pod_broker_tds', 'pod_broker_loading_charges',
                                   'pod_broker_unloading_charges', 'pod_broker_damage', 'pod_broker_detentions',
                                   'pod_broker_damage_comment', 'b_f_pay_date', 'b_f_pay_time', 'b_f_pay_ref',
                                   'b_payment_type', 'b_payment_made', 'bill_no', 'c_pay_date', 'c_pay_time',
                                   'c_ref_no', 'c_payment_type', 'c_payment_due', 'c_bill_generated_date',
                                   'c_bill_generated_time']

location_change_header_field = ['order_code', 'changed_source', 'changed_destination']

vehicle_change_header_field = ['order_code', 'vehicle_number']

trip_delete_list = ['trip_code']
hub_closer_list = [
    'trip_code', 'order_code', 'actual_for_toll', 'actual_fuel_ltr', 'actual_for_fuel', 'actual_for_entry',
    'actual_for_meals', 'actual_for_police', 'bonus', 'penality', 'challan', 'balance_comment', 'repairs']

broker_rate_update = ['order_code', 'trip_code', 'broker_rate',
                      'full_advance_payment_amount', 'full_advance_payment_flag']


##############################################################################################################


###############################################################################################################
#
#
################################################################################################################
def create_broker_import(*args):
    if args[0][9] == '0':
        kyc_status = False
    else:
        kyc_status = True
    if args[0][9] == '':
        return False, 'KYC status is not valid'
    user_obj = User.objects.filter(username__iexact=args[0][-1])
    password = 1234
    broker_name = args[0][0] + ' ' + args[0][1]
    # broker_acc = JSONParser().parse(args[0][10])
    try:
        broker_acc = json.loads(args[0][10])
    except Exception as e:
        return False, 'Json Error'
    if type(broker_acc) is list:
        pass
    else:
        return False, 'broker_list'
    if not len(user_obj):
        user = User.objects.create(username=args[0][-1])
        user.set_password(password)
        user.first_name = args[0][0]
        user.last_name = args[0][1]
        user.email = args[0][-1]
        user.save()
        UserManagement(user_id=user.id, is_broker=True).save()
        broker = Broker.objects.create(broker_name=broker_name, user_id=user.id,
                                       mobile_number=args[0][2],
                                       company=args[0][3], broker_code=args[0][4],
                                       tds_structure=args[0][5],
                                       address=args[0][6], location_broker=args[0][7],
                                       percentage=args[0][8], kyc_status=kyc_status)
        for i in range(0, len(broker_acc)):
            try:
                BrokerAccount(broker_id=broker.id, ifsc_code=broker_acc[i]['ifsc'],
                              bank_acc_no=broker_acc[i]['account_no'],
                              pan_no=broker_acc[i]['pan_no'],
                              account_holder_name=broker_acc[i]['account_name']).save()
            except:
                pass
        return True, 'Successfully imported'
    else:
        return True, 'Data already register'


################################################################
# Name - broker_export_import_page
#
##############################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def broker_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_broker': True})


##################################################################################
# Name - market_vehicle_export_import_page
#
##################################################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def market_vehicle_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_market_vehicle': True})


#####################################################################################
# Name - vehicle_type_export_import_page
#
#####################################################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def vehicle_type_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'vehicle_type': True})


##################################################################################
# Name - market_driver_export_import_page
#
##################################################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def market_driver_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_market_driver': True})


#######################################################################################################################
# Name - indent_import_export_page
#
#
######################################################################################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def indent_import_export_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_indent': True})


@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def broker_rate_c_page(request):
    if request.method == 'GET':
        return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_broker_rate': True})
    elif request.method == 'POST':
        file_data = request.FILES['import_csv']
        reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
        count = 0
        code_list = list()
        rate_list = list()
        full_advance_payment_flag_list = list()
        full_advance_payment_amount_list = list()
        for row in reader:
            count += 1
            if count == 1:
                if row == broker_rate_update:
                    # header_length = len(row)
                    continue
                else:
                    return HttpResponse("Headers are incorrect")
            else:
                code_list.append(row[1])
                rate_list.append(row[2])
                full_advance_payment_amount_list.append(row[3])
                if row[4]:
                    if row[4].capitalize() == 'False':
                        full_advance_payment_flag_list.append(False)
                    else:
                        full_advance_payment_flag_list.append(True)
                else:
                    full_advance_payment_flag_list.append(False)
            LOGGER.info(f"full_advance_payment_flag_list - {full_advance_payment_flag_list}")
            t_obj = Trip.objects.filter(trip_code__in=code_list)
            for idx, value in enumerate(code_list):
                if not rate_list[idx]:
                    return render(request, 'import_export/form_page.html',
                                  {'error': "Error in Trip Code - {} - (Broker rate required)".format(value),
                                   'count': '', 'is_broker_rate': True})
                broker_rate = float(rate_list[idx])
                print(full_advance_payment_flag_list[idx])
                if not full_advance_payment_flag_list[idx]:
                    LOGGER.info(f"trip_code --- {value}")
                    trip_obj = t_obj.filter(trip_code=value,
                                            payment_status__payment_status_name__ne="Balance Paid").first()
                    LOGGER.info(f"order_id --- {trip_obj.order_id}")
                    try:
                        if FinancePayable.objects.filter(trip_id=str(trip_obj.id), is_approved=2):
                            return render(request, 'import_export/form_page.html',
                                          {
                                              'error': "Error in Trip Code - {} - (Already Approved for Balance Paid)".format(
                                                  value), 'count': '', 'is_broker_rate': True})
                    except:
                        pass

                    try:
                        o_obj = Order.objects.get(id=trip_obj.order_id)
                    except:
                        return render(request, 'import_export/form_page.html',
                                      {'error': "Error in Trip Code - {} - (Already Balance Paid)".format(value),
                                       'count': '', 'is_broker_rate': True})
                    if trip_obj.invalid:
                        return render(request, 'import_export/form_page.html',
                                      {
                                          'error': "Error in Trip Code - {} - (Already Invalid)".format(
                                              value), 'count': '', 'is_broker_rate': True})

                    old_broker_rate = float(o_obj.broker_rate)
                    o_obj.broker_rate = broker_rate

                    try:
                        with transaction.atomic():
                            f_p_obj = FinancePayable.objects.filter(
                                trip_id=external_methods.return_trip_id(t_obj.filter(trip_code=value)[0].id)).first()
                            data_d = f_p_obj.payable
                            payment_due = float(f_p_obj.payment_due) if f_p_obj.payment_due else 0
                            recovery_amount = f_p_obj.recovery_amount if f_p_obj.recovery_amount else 0
                            LOGGER.info(f"recovery_amount --- {recovery_amount}")
                            LOGGER.info(f"payment_due --- {payment_due}")
                            new_payment_due = 0
                            recovery_amount_new = 0
                            if payment_due < 0:
                                if recovery_amount:
                                    raise Exception(f"Error in Trip Code - {value} - "
                                                    f"(Can't change broker rate, recovery already done. )")
                                else:
                                    # recovery given

                                    trip_advance_recovery_given = TripAdvanceRecovery.objects.filter(
                                        recent_trip_order_code=o_obj.order_id)

                                    # already recovery done
                                    trip_advance_rec_amount = 0
                                    trip_advance_recovery = TripAdvanceRecovery.objects.filter(
                                        recovery_order_code=o_obj.order_id)

                                    if trip_advance_recovery_given.exists() or trip_advance_recovery.exists():
                                        if old_broker_rate >= broker_rate:
                                            raise Exception(
                                                f"Error in Trip Code - {value} - (Can't process if new broker rate less than equal"
                                                f"old broker rate. )")

                                    if old_broker_rate < broker_rate:
                                        # recovery start here
                                        # create recovery here if recovery not happen and recovery given advance
                                        advance_recovery_done_amount, advance_recovery_given_amount, used_broker_amount = advance_recovery_operation(
                                            trip_advance_recovery_given, trip_advance_recovery, old_broker_rate,
                                            broker_rate, o_obj)
                                        # LOGGER.info(f"use recovery --- {use_recovery}")
                                        use_recovery = 0

                                        # o_obj.previous_trip_deduction = round(o_obj.previous_trip_deduction - use_recovery, 2)
                                        fuel_advance = o_obj.fuel_advance if o_obj.fuel_advance else 0

                                        fuel_advance = fuel_advance - advance_recovery_done_amount
                                        # 7082.62 - (49000 - 44278)
                                        # 7082.62 - 5722
                                        # 1360.62
                                        # Fuel given
                                        # Recovery b done
                                        #  old broker rate - 55k, new 57k
                                        # fuel - 550490.59k
                                        tds = o_obj.tds if o_obj.tds else 0
                                        tds_percent = round_decimals_up((float(1 - tds / 100)), 2)
                                        if advance_recovery_given_amount:
                                            # 55000 after tds 54450
                                            # advance_recovery_given_amount = 20789.14
                                            # fuel = 50490.59
                                            # credit_limit_used  = 50490.59-(54450-20789.14)= 16829.73

                                            # New broker rate = 56430-20789.14 = 50490.59-35640.86= 14849.73

                                            credit_limit_used = fuel_advance - (
                                                        round_decimals_up((old_broker_rate * tds_percent),
                                                                          2) - advance_recovery_given_amount)
                                        else:
                                            credit_limit_used = fuel_advance - round_decimals_up(
                                                (old_broker_rate * tds_percent), 2)

                                        if fuel_advance > 0 and credit_limit_used > 0:
                                            # broker rate after tds
                                            broker_rate = round_decimals_up((broker_rate * tds_percent), 2)
                                            broker_rate = round(broker_rate - used_broker_amount, 2)

                                            if broker_rate > round_decimals_up((old_broker_rate * tds_percent), 2):
                                                new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj,
                                                                                                 broker_rate)
                                                credit_limit_used_new_broker_rate = round(
                                                    (float(fuel_advance) - (
                                                                float(broker_rate) - advance_recovery_given_amount)), 2)
                                                # +tive, -tive , zero
                                                # -5949.41
                                                if credit_limit_used_new_broker_rate == 0 or credit_limit_used_new_broker_rate < 0:
                                                    credit_limit_used = credit_limit_used
                                                    recovery_amount_new = credit_limit_used
                                                else:
                                                    credit_limit_used = credit_limit_used - credit_limit_used_new_broker_rate
                                                    recovery_amount_new = credit_limit_used
                                                broker_obj = Broker.objects.select_for_update(nowait=True).get(
                                                    id=o_obj.broker_id)
                                                used_credit_limit = broker_obj.used_credit_limit if broker_obj.used_credit_limit else 0
                                                remaining_credit_limit = broker_obj.remaining_credit_limit if broker_obj.remaining_credit_limit else 0
                                                remaining_credit_limit = remaining_credit_limit + credit_limit_used
                                                used_credit_limit = used_credit_limit - credit_limit_used
                                                if remaining_credit_limit > broker_obj.working_capital:
                                                    remaining_credit_limit = 0
                                                    used_credit_limit = 0

                                                broker_obj.remaining_credit_limit = round(remaining_credit_limit, 2)
                                                broker_obj.used_credit_limit = round(used_credit_limit, 2)
                                                broker_obj.save()
                                                if new_payment_due < 0:
                                                    data_d["broker_rate"] = o_obj.broker_rate
                                                    f_p_obj.payable = data_d
                                                    # f_p_obj.payment_due = new_payment_due
                                                    f_p_obj.payment_due = new_payment_due
                                                    o_obj.updated_at = timezone.now()
                                                    o_obj.updated_by = request.user.username
                                                    f_p_obj.save()
                                                    o_obj.save()
                                                    gobolt_business_update_details(o_obj, f_p_obj)
                                                    BrokerRateChangeLog.objects.create(order_code=o_obj.order_id,
                                                                                       new_broker_rate=o_obj.broker_rate,
                                                                                       old_broker_rate=old_broker_rate,
                                                                                       trip_id=f_p_obj.trip_id,
                                                                                       created_at = timezone.now(),
                                                                                       created_by=request.user.username)

                                                    try:
                                                        if trip_obj.pod_data["pod_status"] == "Completed":
                                                            try:
                                                                broker_loading_charge = trip_obj.pod_data["pod_broker_data"]["loading_charges"]
                                                                broker_unloading_charge = trip_obj.pod_data["pod_broker_data"]["unloading_charges"]
                                                            except:
                                                                broker_loading_charge = 0
                                                                broker_unloading_charge = 0
                                                            value_var = ReturnDueDate.update_finance_data(
                                                                trip_obj,
                                                                trip_id=f_p_obj.trip_id,
                                                                broker_loading_charge=broker_loading_charge,
                                                                broker_unloading_charge=broker_unloading_charge,
                                                                username=request.user.username,
                                                            )
                                                            if value_var:
                                                                print(" ======> Finance Data Updated.")
                                                            else:
                                                                print(" ====> Finance Data not Updated.")
                                                    except Exception as e:
                                                        pass                                    
                                                    try:
                                                        TransactionLogs.objects.filter(type='Broker Balance',
                                                                                       reference_no__own=
                                                                                       f_p_obj.paid_ref[
                                                                                           'ref_no']).update(
                                                            payment_money=f_p_obj.payment_due,
                                                            updated_at=timezone.now(),
                                                            updated_by=request.user.username)

                                                    except Exception as e:
                                                        pass


                                        else:
                                            broker_rate = round_decimals_up(broker_rate - used_broker_amount, 2)
                                            new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj,
                                                                                             broker_rate)
                                            if new_payment_due < 0:
                                                data_d["broker_rate"] = o_obj.broker_rate
                                                f_p_obj.payable = data_d
                                                # f_p_obj.payment_due = new_payment_due
                                                f_p_obj.payment_due = new_payment_due
                                                f_p_obj.updated_by = request.user.username
                                                f_p_obj.updated_at = timezone.now()
                                                o_obj.updated_by = request.user.username
                                                o_obj.updated_at = timezone.now()
                                                f_p_obj.save()
                                                o_obj.save()
                                                gobolt_business_update_details(o_obj, f_p_obj)
                                                BrokerRateChangeLog.objects.create(order_code=o_obj.order_id,
                                                                                   new_broker_rate=o_obj.broker_rate,
                                                                                   old_broker_rate=old_broker_rate,
                                                                                   trip_id=f_p_obj.trip_id,
                                                                                   created_at = timezone.now(),
                                                                                   created_by = request.user.username)
                                                try:
                                                    if trip_obj.pod_data["pod_status"] == "Completed":
                                                        try:
                                                            broker_loading_charge = trip_obj.pod_data["pod_broker_data"]["loading_charges"]
                                                            broker_unloading_charge = trip_obj.pod_data["pod_broker_data"]["unloading_charges"]
                                                        except:
                                                            broker_loading_charge = 0
                                                            broker_unloading_charge = 0
                                                        value_var = ReturnDueDate.update_finance_data(
                                                            trip_obj,
                                                            trip_id=f_p_obj.trip_id,
                                                            broker_loading_charge=broker_loading_charge,
                                                            broker_unloading_charge=broker_unloading_charge,
                                                            username=request.user.username,
                                                        )
                                                        if value_var:
                                                            print(" ======> Finance Data Updated.")
                                                        else:
                                                            print(" ====> Finance Data not Updated.")
                                                except Exception as e:
                                                    pass                                    
                                                try:
                                                    TransactionLogs.objects.filter(type='Broker Balance',
                                                                                   reference_no__own=f_p_obj.paid_ref[
                                                                                       'ref_no']).update(
                                                        payment_money=f_p_obj.payment_due, updated_at=timezone.now(),
                                                        updated_by=request.user.username)

                                                except Exception as e:
                                                    pass

                                        if advance_recovery_given_amount > 0 and used_broker_amount > 0:
                                            credit_limit_used = used_broker_amount
                                            broker_obj = Broker.objects.select_for_update(nowait=True).get(
                                                id=o_obj.broker_id)
                                            used_credit_limit = broker_obj.used_credit_limit if broker_obj.used_credit_limit else 0
                                            remaining_credit_limit = broker_obj.remaining_credit_limit if broker_obj.remaining_credit_limit else 0
                                            remaining_credit_limit = remaining_credit_limit + credit_limit_used
                                            used_credit_limit = used_credit_limit - credit_limit_used
                                            if used_credit_limit >= 0:
                                                if remaining_credit_limit > broker_obj.working_capital:
                                                    remaining_credit_limit = broker_obj.working_capital
                                                    used_credit_limit = 0

                                                broker_obj.remaining_credit_limit = round_decimals_up(
                                                    remaining_credit_limit, 2)
                                                broker_obj.used_credit_limit = round_decimals_up(used_credit_limit, 2)
                                                broker_obj.save()
                                            t_a_r_g = trip_advance_recovery_given.first()
                                            t_a_r_g.recovery_amount = round_decimals_up(
                                                t_a_r_g.recovery_amount + used_broker_amount, 2)
                                            t_a_r_g.balance_recovery_amount = round_decimals_up(
                                                t_a_r_g.balance_recovery_amount + used_broker_amount, 2)
                                            t_a_r_g.save()
                                            new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj,
                                                                                             broker_rate)
                                            if new_payment_due < 0:
                                                data_d["broker_rate"] = o_obj.broker_rate
                                                f_p_obj.payable = data_d
                                                # f_p_obj.payment_due = new_payment_due
                                                f_p_obj.payment_due = new_payment_due
                                                o_obj.updated_by = request.user.username
                                                o_obj.updated_at = timezone.now()
                                                f_p_obj.save()
                                                o_obj.save()
                                                gobolt_business_update_details(o_obj, f_p_obj)
                                                BrokerRateChangeLog.objects.create(order_code=o_obj.order_id,
                                                                                   new_broker_rate=o_obj.broker_rate,
                                                                                   old_broker_rate=old_broker_rate,
                                                                                   trip_id=f_p_obj.trip_id,
                                                                                   created_at = timezone.now(),
                                                                                   created_by=request.user.username
                                                                                   )
                                                try:
                                                    if trip_obj.pod_data["pod_status"] == "Completed":
                                                        try:
                                                            broker_loading_charge = trip_obj.pod_data["pod_broker_data"]["loading_charges"]
                                                            broker_unloading_charge = trip_obj.pod_data["pod_broker_data"]["unloading_charges"]
                                                        except:
                                                            broker_loading_charge = 0
                                                            broker_unloading_charge = 0
                                                        value_var = ReturnDueDate.update_finance_data(
                                                            trip_obj,
                                                            trip_id=f_p_obj.trip_id,
                                                            broker_loading_charge=broker_loading_charge,
                                                            broker_unloading_charge=broker_unloading_charge,
                                                            username=request.user.username,
                                                        )
                                                        if value_var:
                                                            print(" ======> Finance Data Updated.")
                                                        else:
                                                            print(" ====> Finance Data not Updated.")
                                                except Exception as e:
                                                    pass                                           
                                                try:
                                                    TransactionLogs.objects.filter(type='Broker Balance',
                                                                                   reference_no__own=f_p_obj.paid_ref[
                                                                                       'ref_no']).update(
                                                        payment_money=f_p_obj.payment_due, updated_at=timezone.now(),
                                                        updated_by=request.user.username)

                                                except Exception as e:
                                                    pass

                                    else:
                                        # can't process if broker rate is less than previous broker rate
                                        raise Exception(f"Error in Trip Code - {value} - (Can't process if broker "
                                                        f"rate is less than previous broker rate. )")
                            elif payment_due >= 0:
                                if recovery_amount:
                                    if old_broker_rate < broker_rate:
                                        new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj, broker_rate)
                                        new_payment_due = round_decimals_up((new_payment_due - abs(recovery_amount)), 2)
                                    else:
                                        # can't process if broker rate is less than previous broker rate
                                        raise Exception(
                                            f"Error in Trip Code - {value} - (Can't process if broker rate is "
                                            "less than previous broker rate. )")
                                else:
                                    new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj, broker_rate)
                                    if new_payment_due < 0:
                                        raise Exception(f"Error in Trip Code - {value} - (Can't process broker balance "
                                                        f"can’t be changed to negative.)")

                            if new_payment_due >= 0:
                                data_d["broker_rate"] = o_obj.broker_rate
                                f_p_obj.payable = data_d
                                # f_p_obj.payment_due = new_payment_due
                                f_p_obj.payment_due = new_payment_due
                                f_p_obj.updated_by = request.user.username
                                o_obj.updated_by = request.user.username
                                f_p_obj.save()
                                o_obj.save()
                                gobolt_business_update_details(o_obj, f_p_obj)
                                BrokerRateChangeLog.objects.create(order_code=o_obj.order_id, new_broker_rate=o_obj.broker_rate,
                                                                   old_broker_rate=old_broker_rate,
                                                                   trip_id=f_p_obj.trip_id,
                                                                   created_at = timezone.now(),
                                                                   created_by = request.user.username)
                                try:
                                    if trip_obj.pod_data["pod_status"] == "Completed":
                                        try:
                                            broker_loading_charge = trip_obj.pod_data["pod_broker_data"]["loading_charges"]
                                            broker_unloading_charge = trip_obj.pod_data["pod_broker_data"]["unloading_charges"]
                                        except:
                                            broker_loading_charge = 0
                                            broker_unloading_charge = 0
                                        value_var = ReturnDueDate.update_finance_data(
                                            trip_obj,
                                            trip_id=f_p_obj.trip_id,
                                            broker_loading_charge=broker_loading_charge,
                                            broker_unloading_charge=broker_unloading_charge,
                                            username=request.user.username,
                                        )
                                        if value_var:
                                            print(" ======> Finance Data Updated.")
                                        else:
                                            print(" ====> Finance Data not Updated.")
                                except Exception as e:
                                    pass                                                                                                        
                                try:
                                    TransactionLogs.objects.filter(type='Broker Balance',
                                                                   reference_no__own=f_p_obj.paid_ref['ref_no']).update(
                                        payment_money=f_p_obj.payment_due, updated_at=timezone.now(),updated_by = request.user.username)

                                except Exception as e:
                                    pass
                    except Exception as e:
                        LOGGER.info(f"broker_rate_c_page exception - {str(e)}", exc_info=True)
                        return render(request, 'import_export/form_page.html',
                                      {'error': "{}".format(str(e)), 'count': '',
                                       'is_broker_rate': True})
                    # Now  here to check transaction log
                    # o_obj.save()
                else:
                    trip = pymongo_crud.db['trip']
                    payment_status_master = PaymentStatus.objects
                    trip_obj = t_obj.filter(trip_code=value,
                                            payment_status__payment_status_name__in=["PENDING_FOR_ADV_APPROVAL",
                                                                                     "Approval Awaited"],
                                            trip_status__status_name="Completed").first()

                    if trip_obj:
                        o_obj = Order.objects.get(id=trip_obj.order_id)
                        broker_obj = Broker.objects.get(id=o_obj.broker_id)
                        f_p_obj = FinancePayable.objects.filter(
                            trip_id=trip_obj.id).first()
                        data_d = f_p_obj.payable
                        full_advance_payment_amount = float(full_advance_payment_amount_list[idx]) if \
                            full_advance_payment_amount_list[idx] else 0
                        if broker_obj.fuel_allowed is True:
                            # checking in Fuel Module
                            fuel_obj = FuelPump(
                                order_code=o_obj.order_id,
                                vehicle_number=trip_obj.order_data["vehicle_no"]
                            )
                            fuel_req_dict = fuel_obj.get_fuel_request_details_by_order_code()
                            if fuel_req_dict.get("fuelRequestStatus", ""):
                                return render(request, 'import_export/form_page.html',
                                              {'error': f"Error in Trip Code - {value} - (There is a fuel "
                                                        f"request created in fuel pump module.)",
                                               'count': '', 'is_broker_rate': True})
                            advance_limit = round(((broker_rate * broker_obj.cash_advance_limit) / 100), 2)
                        else:
                            advance_limit = broker_rate
                        trip_advance_recovery_given = TripAdvanceRecovery.objects.filter(
                            recent_trip_order_code=o_obj.order_id)

                        # already recovery done

                        trip_advance_rec_amount = 0
                        trip_advance_recovery = TripAdvanceRecovery.objects.filter(
                            recovery_order_code=o_obj.order_id)

                        if trip_advance_recovery_given.exists() or trip_advance_recovery.exists():
                            raise Exception(f"Error in Trip Code - {value} - (Can't process if Advance "
                                            f"recovery done. )")

                        LOGGER.info(f"advance_limit - {advance_limit}")
                        if full_advance_payment_amount:
                            if full_advance_payment_amount > advance_limit:
                                return render(request, 'import_export/form_page.html',
                                              {'error': f"Error in Trip Code - {value} - (Advance payment amount "
                                                        f"can't be greater than advance limit of {advance_limit})",
                                               'count': '', 'is_broker_rate': True})
                            broker_adv = full_advance_payment_amount
                        else:
                            tds = float(broker_obj.tds_structure) if broker_obj.tds_structure else 0
                            tds_value = (broker_rate * tds) / 100
                            deductions = o_obj.deductions if o_obj.deductions else 0
                            advance_amount = round((broker_rate - tds_value - deductions), 2)
                            print(advance_amount)
                            if advance_amount > advance_limit:
                                return render(request, 'import_export/form_page.html',
                                              {'error': f"Error in Trip Code - {value} - (Advance payment amount "
                                                        f"can't be greater than advance limit of {advance_limit})",
                                               'count': '', 'is_broker_rate': True})
                            broker_adv = advance_amount

                        status_payment = get_payment_status_master(
                            payment_status_master, "Approval Awaited"
                        )
                        query = {"_id": ObjectId(trip_obj.id)}
                        set_dict = dict(payment_status=status_payment)
                        set_values = {"$set": set_dict}
                        trip.update(query, set_values)
                        LOGGER.info(f"broker advance - {broker_adv}, broker_rate - {broker_rate}")
                        new_payment_due = broker_payment_deu_calculation(trip_obj, f_p_obj, broker_rate)
                        LOGGER.info(f"new_payment_due - {new_payment_due}")
                        data_d["broker_rate"] = broker_rate
                        f_p_obj.payable = data_d
                        f_p_obj.updated_by = request.user.username
                        f_p_obj.updated_at = timezone.now()
                        # f_p_obj.payment_due = new_payment_due
                        f_p_obj.payment_due = new_payment_due
                        o_obj.broker_rate = broker_rate
                        o_obj.broker_advance = broker_adv
                        o_obj.payment_status = status_payment.get("payment_status_name", "")
                        o_obj.updated_by = request.user.username
                        o_obj.updated_at = timezone.now()
                        f_p_obj.save()
                        o_obj.save()
                        gobolt_business_update_details(o_obj, f_p_obj)
                        try:
                            if trip_obj.pod_data["pod_status"] == "Completed":
                                try:
                                    broker_loading_charge = trip_obj.pod_data["pod_broker_data"]["loading_charges"]
                                    broker_unloading_charge = trip_obj.pod_data["pod_broker_data"]["unloading_charges"]
                                except:
                                    broker_loading_charge = 0
                                    broker_unloading_charge = 0
                                value_var = ReturnDueDate.update_finance_data(
                                    trip_obj,
                                    trip_id=f_p_obj.trip_id,
                                    broker_loading_charge=broker_loading_charge,
                                    broker_unloading_charge=broker_unloading_charge,
                                    username=request.user.username,
                                )
                                if value_var:
                                    print(" ======> Finance Data Updated.")
                                else:
                                    print(" ====> Finance Data not Updated.")
                        except Exception as e:
                            pass 
                    else:
                        return render(request, 'import_export/form_page.html',
                                      {'error': "Error in Trip Code - {} - (Trip not completed or not in "
                                                "PENDING_FOR_ADV_APPROVAL state)".format(value),
                                       'count': '', 'is_broker_rate': True})

        error = 'Successfully Operation done'
        return render(request, 'import_export/form_page.html',
                      {'is_broker_rate': True, 'error': error, 'count': ''})


def gobolt_business_update_details(order_obj, finance_payable_obj):
    # update gobolt business app details
    try:
        if order_obj.float_to_app is True:
            gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                order_code=order_obj.order_code
            ).exclude(status="CANCELED").first()
            if gobolt_app_obj:
                tds = float(order_obj.tds) if order_obj.tds else 0
                broker_rate = float(order_obj.broker_rate) if order_obj.broker_rate else 0
                tds_amount = float(round_decimals_up((broker_rate * tds) / 100))
                gobolt_app_obj.broker_rate = broker_rate
                gobolt_app_obj.tds = tds_amount
                gobolt_app_obj.balance_due = finance_payable_obj.payment_due
                gobolt_app_obj.save()
    except Exception as e:
        LOGGER.info(f"Exception while update bololt business app details - {str(e)}", exc_info=True)


def gobolt_business_cancel_order(order_obj, user_id):
    # update gobolt business app details
    try:
        if order_obj.float_to_app is True:
            gobolt_app_obj = GoboltBusinessApp.objects.using("gobolt_business").filter(
                order_code=order_obj.order_code
            ).exclude(status="CANCELED").first()
            if gobolt_app_obj:
                gobolt_app_obj.status = "CANCELED"
                gobolt_app_obj.order_status = order_obj.current_status
                gobolt_app_obj.canceled_at = datetime.datetime.now().isoformat()
                gobolt_app_obj.canceled_by = user_id
                gobolt_app_obj.save()
    except Exception as e:
        LOGGER.info(f"Exception while update bololt business app details - {str(e)}", exc_info=True)


def advance_recovery_operation(advance_recovery_given, advance_recovery_done, old_broker_rate, new_broker_rate, o_obj):
    tds = o_obj.tds if o_obj.tds else 0
    tds_percent = round_decimals_up((float(1 - tds / 100)), 2)
    virtual_broker_rate = round_decimals_up((new_broker_rate * tds_percent), 2)
    # 14700
    # 9800
    # 4900
    difference_broker_rate = virtual_broker_rate - round_decimals_up((old_broker_rate * tds_percent), 2)
    if difference_broker_rate <= 0:
        raise Exception(f"New broker rate not valid after tds deduction it is less than old broker rate")
    advance_recovery_done_amount = 0
    advance_recovery_given_amount = 0
    balance_recovery_amount = 0
    advance_recovery_given_last_order_amount = 0
    used_broker_amount = 0
    if advance_recovery_done.exists():
        for i in advance_recovery_done:
            advance_recovery_done_amount += abs(i.last_order_amount)
            return round_decimals_up(advance_recovery_done_amount, 2), round_decimals_up(advance_recovery_given_amount,
                                                                                         2), round_decimals_up(
                used_broker_amount, 2)

    if advance_recovery_given.exists():
        for i in advance_recovery_given:
            # balance recovery amount always, 0 or negative
            balance_recovery_amount += i.balance_recovery_amount
            advance_recovery_given_amount += i.recovery_amount
            advance_recovery_given_last_order_amount += abs(i.last_order_amount)
            if balance_recovery_amount < 0:
                if (difference_broker_rate - abs(balance_recovery_amount)) >= 0:
                    used_broker_amount = abs(balance_recovery_amount)
                else:
                    used_broker_amount = difference_broker_rate

        return round_decimals_up(advance_recovery_done_amount, 2), round_decimals_up(advance_recovery_given_amount,
                                                                                     2), round_decimals_up(
            used_broker_amount, 2)

    if not advance_recovery_done.exists() and not advance_recovery_given.exists():
        return round_decimals_up(advance_recovery_done_amount, 2), round_decimals_up(advance_recovery_given_amount,
                                                                                     2), round_decimals_up(
            used_broker_amount, 2)


def broker_payment_deu_calculation(trip_obj, f_p_obj, new_broker_rate=None):
    try:
        surch = trip_obj.pod_data['pod_broker_data']['b_surcharge']
    except Exception as e:
        surch = 0

    try:
        if trip_obj.pod_data['pod_broker_data']['ent_load_pay_id']:
            loading_charge = 0.0
        else:
            loading_charge = trip_obj.pod_data['pod_broker_data']['loading_charge']
    except:
        loading_charge = trip_obj.pod_data['pod_broker_data']['loading_charge']
    try:
        if trip_obj.pod_data['pod_broker_data']['ent_unload_pay_id']:
            unloading_charge = 0.0
        else:
            unloading_charge = trip_obj.pod_data['pod_broker_data']['unloading_charge']
    except:
        unloading_charge = trip_obj.pod_data['pod_broker_data']['unloading_charge']

    if new_broker_rate:
        broker_rate_for_balance = new_broker_rate
    else:
        broker_rate_for_balance = int(f_p_obj.payable['broker_rate'])

    payment_due = payment_due_calculation(broker_rate_for_balance,
                                          broker=(
                                              f_p_obj.payable['advance_deduction'],
                                              loading_charge,
                                              unloading_charge,
                                              trip_obj.pod_data['pod_broker_data']['taxes'],
                                              trip_obj.pod_data['pod_broker_data']['detention'],
                                              surch,
                                              trip_obj.pod_data['pod_broker_data']['tds'],
                                              trip_obj.pod_data['pod_broker_data'][
                                                  'deduction_value'],
                                              f_p_obj.payable['finance_data_paid'][
                                                  'broker_paid']['broker_advance_paid'],
                                              f_p_obj.payable['finance_data_paid'][
                                                  'driver_paid']['driver_advance_paid'],
                                              f_p_obj.payable.get('fuel_advance', 0),
                                          ),
                                          customer=None, order_code=Order.objects.get(id=trip_obj.order_id).order_id)
    return payment_due


def get_data_from_trip_recovery(order_code):
    recovered_amount_, recover_amount, recovery_amount_used_by, total_recovery_given = 0, 0, 0, 0

    obj = TripAdvanceRecovery.objects.filter(recovery_order_code=order_code)
    if obj.exists():
        recovered_amount_, recover_amount = abs(obj[0].last_order_amount), obj[0].recovery_amount

    obj = TripAdvanceRecovery.objects.filter(recent_trip_order_code=order_code)
    if obj.exists():
        recovery_amount_used_by = obj[0].recovery_amount
        total_recovery_given = abs(obj[0].last_order_amount)
    return recovered_amount_, recover_amount, recovery_amount_used_by, total_recovery_given


@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def trip_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_trip': True})


@csrf_exempt
def t_o_import(request):
    # return render(request, 'import_export/form_page.html',
    #               {'is_trip': True, 'error': "Functionality removed", 'count': ''})
    # HttpResponse("Method is not allowed.")
    try:
        error = 'Please check your input data or data not exists '
        code_list = list()
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            # reader = csv.reader(file_data)
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == trip_delete_list:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    code_list.append(row[0])
            # apdg = Advance Pending
            # aw_ap = Approval Awaited
            t_obj = Trip.objects.filter(
                Q(trip_code__in=code_list) & (
                        Q(payment_status__id=global_objects.global_payment_master(apdg='apdg')['id']) | Q(
                    payment_status__id=global_objects.global_payment_master(p_f_a_a='p_f_a_a')['id']) | Q(
                    payment_status__id=global_objects.global_payment_master(aw_ap='aw_ap')['id'])))
            LOGGER.info(f"t_obj ---- {t_obj}")
            if t_obj:
                t_ids = map(lambda x: external_methods.return_trip_id(x.id), t_obj)
                order_ids = map(lambda x: x.order_id, t_obj)

                # delete in last trip object

                # Getting order
                i_obj = Indent.objects.all()
                order_objs = Order.objects.filter(id__in=order_ids)
                # get entity payment list based on order code
                order_codes = list(
                    set(EntityPayments.objects.filter(order_code__in=map(lambda x: x.order_id, order_objs),
                                                      id_disable=False).values_list('order_code', flat=True)))
                LOGGER.info(f"order_codes ---- {order_codes}")
                # check if any advance recovery exists

                adv_recovery_order_codes = list(
                    set(TripAdvanceRecovery.objects.filter(
                        recovery_order_code__in=map(lambda x: x.order_id, order_objs)
                    ).values_list('recovery_order_code')))
                LOGGER.info(f"adv_recovery_order_codes ---- {adv_recovery_order_codes}")
                if adv_recovery_order_codes:
                    LOGGER.info(f"Advance Recovery Exists for orders - {adv_recovery_order_codes} !")
                    error = f"Advance Recovery Exists for orders - {adv_recovery_order_codes} !"

                recovery_order_codes = list(
                    set(TripAdvanceRecovery.objects.filter(
                        recent_trip_order_code__in=map(lambda x: x.order_id, order_objs)
                    ).values_list('recent_trip_order_code')))
                LOGGER.info(f"recovery_order_codes ---- {recovery_order_codes}")
                if recovery_order_codes:
                    LOGGER.info(f"Advance Recovery Exists for orders - {recovery_order_codes} !")
                    error = f"Advance Recovery Exists for orders - {recovery_order_codes} !"

                if not order_codes:
                    if not adv_recovery_order_codes and not recovery_order_codes:
                        orders = []
                        for i in order_objs:
                            vehicle_obj = Vehicles.objects.get(id=i.vehicle_id)
                            # checking in Fuel Module
                            fuel_obj = FuelPump(
                                order_code=i.order_id,
                                vehicle_number=vehicle_obj.vehicle_registration_number
                            )
                            fuel_req_dict = fuel_obj.get_fuel_request_details_by_order_code()
                            if fuel_req_dict.get("fuelRequestStatus", ""):
                                orders.append(i.order_id)
                        LOGGER.info(f"orders ---- {orders}")
                        if not orders:
                            for i in order_objs:

                                k = i_obj.filter(id=i.indent_id)
                                k_o = Order.objects.filter(indent_id=k[0].id)
                                for j in k_o:
                                    if j.id == i.id:
                                        i.cancellation_type = 'Wrong Order'
                                        i.current_status = 'Cancelled'
                                        i.cancellation_date = datetime.datetime.now()
                                        i.save()
                                        k.update(number_of_vehicle=(k[0].number_of_vehicle - 1))
                                        if k[0].number_of_vehicle == 0:
                                            k.update(is_disable=True, cancellation_type='Wrong Order',
                                                     cancellation_date=datetime.datetime.now(),
                                                     updated_at=timezone.now(),
                                                     updated_by=request.user.username)
                                        gobolt_business_cancel_order(i, request.user.id)
                            FinanceData.objects.filter(trip_id__in=t_ids).delete()
                            FinancePayable.objects.filter(trip_id__in=t_ids).delete()

                            tsm_obj = TripStatusMaster.objects.get(status_name="Deleted", is_disable=False)
                            psm_obj = PaymentStatus.objects.filter(payment_status_name="Deleted", is_disable=False)
                            if not psm_obj:
                                LOGGER.info("Deleted is not a valid status!")
                                HttpResponse("Deleted is not a valid status!")
                            if not tsm_obj:
                                LOGGER.info("Deleted is not a valid status!")
                                HttpResponse("Deleted is not a valid status!")
                            status_name = tsm_obj.status_name
                            status_id = str(tsm_obj.id)
                            payment_status = {'payment_status_name': psm_obj[0].payment_status_name,
                                              'id': str(psm_obj[0].id)}

                            t_obj.update(
                                trip_status={
                                    "id": status_id,
                                    "status_name": status_name
                                },
                                payment_status=payment_status,
                                modified_date=datetime.datetime.now()
                            )
                            error = 'Successfully Operation done'


                        else:
                            error = f"These order code {orders} has fuel request!"
                else:
                    error = f"These order code{order_codes} in payment mode!"
            return render(request, 'import_export/form_page.html',
                          {'is_trip': True, 'error': error, 'count': ''})
        else:
            HttpResponse("Method is not allowed.")
    except Exception as err:
        LOGGER.error("Error occured while deleting trip", exc_info=True)


################################################################
# Name - broker_export_import_export
#
##############################################################
def broker_export_import_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=broker.csv'
    writer = csv.DictWriter(response, fieldnames=broker_header_field)
    writer.writeheader()
    return response


################################################################
# Name - broker_export_import_export
#
##############################################################
@csrf_exempt
def broker_export_import_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            # reader = csv.reader(file_data)
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == broker_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_broker_import(row)
                    if not done:
                        return render(request, 'import_export/form_page.html',
                                      {'brker_import': 'import_broker', 'error': error, 'count': count,
                                       'is_broker': True})

            return render(request, 'import_export/form_page.html',
                          {'brker_import': 'import_broker', 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/form_page.html',
                          {'brker_import': 'import_broker', 'error': 'Method is not allowed', 'count': '',
                           'is_broker': True})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


################################################################
# Name - customer_export_import_page
#
##############################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def customer_export_import_page(request):
    return render_to_response('import_export/form_page.html', {'error': '', 'count': '', 'is_customer': True})


################################################################
# Name - customer_export_import_export
#
##############################################################

def customer_export_import_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=customer.csv'
    writer = csv.DictWriter(response, fieldnames=customer_header_field)
    writer.writeheader()
    return response


################################################################
# Name - customer_export_import_export
#
##############################################################
@csrf_exempt
def customer_export_import_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == customer_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_customer_import(row)
                    if not done:
                        return render(request, 'import_export/customer_im_ex.html',
                                      {'custmer_import': 'import_customer', 'error': error, 'count': count})

            return render(request, 'import_export/customer_im_ex.html', {'custmer_import': 'import_customer',
                                                                         'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/customer_im_ex.html',
                          {'custmer_import': 'import_customer', 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


################################################################
# Name - customer_export_import_export
#
################################################################


def create_customer_import(*args):
    print('------------>', args[0])
    ###############################################################
    # getting account manager, bilty boy and sourcing agent
    try:
        user_mgt_obj = UserManagement.objects.all()
        account_manager = user_mgt_obj.filter(user_code__iexact=args[0][6], is_account_manager=True)
        if len(account_manager):
            account_manager_id = account_manager[0].id
        else:
            return False, 'Account manager not found.'

        bilty_boy = user_mgt_obj.filter(user_code__iexact=args[0][7], is_bilty_boy=True)
        if len(bilty_boy):
            bilty_boy_id = bilty_boy[0].id
        else:
            return False, 'Bilty boy not found'

        sourcing_agent = user_mgt_obj.filter(user_code__iexact=args[0][8], is_sourcing_agent=True)
        if len(sourcing_agent):
            sourcing_agent_id = sourcing_agent[0].id
        else:
            return False, 'Sourcing Agent not found'
    except Exception as e:
        print(e)
        return False, 'Data not found.'

    user_obj = User.objects.filter(username__iexact=args[0][-1])
    password = 1234
    customer_name = args[0][0] + ' ' + args[0][1]
    if not len(user_obj):
        user = User.objects.create(username=args[0][-1])
        user.set_password(password)
        user.first_name = args[0][0]
        user.last_name = args[0][1]
        user.email = args[0][-1]
        user.save()
        customer_code = args[0][3]
        UserManagement(user_id=user.id, is_customer=True).save()
        cus_obj = Customer.objects.filter(customer_code__iexact=customer_code)
        if len(cus_obj):
            return False, 'Customer Code should be unique.'
        else:
            Customer.objects.create(customer_name=customer_name, user_id=user.id,
                                    company_name=args[0][2],
                                    customer_code=args[0][3], phone_number=args[0][4],
                                    address=args[0][5],
                                    account_manager_id=account_manager_id, bilty_boy_id=bilty_boy_id,
                                    sourcing_agent_id=sourcing_agent_id)
            return True, 'Successfully imported'
    else:
        return False, 'Data already register'


################################################################
# Name - location_export_import_page
#
##############################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def location_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_location': True})


################################################################
# Name - location_export_import_export
#
##############################################################
def location_export_import_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=location.csv'
    writer = csv.DictWriter(response, fieldnames=location_header_field)
    writer.writeheader()
    return response


################################################################
# Name - location_export_import_export
#
##############################################################
@csrf_exempt
def location_export_import_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']

            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == location_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_loaction_import(row)
                    if not done:
                        return render(request, 'import_export/location_im_ex.html',
                                      {'loction_import': 'import_location', 'error': 'Error occoured.', 'count': count})

            return render(request, 'import_export/location_im_ex.html',
                          {'loction_import': 'import_location', 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/location_im_ex.html',
                          {'loction_import': 'import_location', 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


################################################################
# Name - location_export_import_export
# Creation will here.
################################################################
def create_loaction_import(*args):
    print('------------>', args[0])
    # is all
    if args[0][7] == '0':
        is_hub = False
    else:
        is_hub = True
    if args[0][8] == '0':
        c_location = False
    else:
        c_location = True
    if args[0][9] == '0':
        is_toll_plaza = False
    else:
        is_toll_plaza = True
    if args[0][10] == '0':
        is_refuel = False
    else:
        is_refuel = True
    if args[0][11] == '0':
        is_route = False
    else:
        is_route = True
    location_name = args[0][0]
    location_address = args[0][1]
    location_pin = args[0][2]
    location_state = args[0][3]
    city_code = args[0][4]
    customer_code = args[0][12]
    customer_data = {}

    if not is_hub and not is_refuel and not is_route and not is_toll_plaza and not c_location:
        return False, 'Data is not completed.'
    else:
        city_obj = City.objects.filter(city_code__iexact=city_code)
        if len(city_obj):
            city_json = {'name': city_obj[0].name, 'city_code': city_obj[0].city_code, 'id': city_obj[0].id}
        else:
            return False, HttpResponse("Data not found.")

        location_radius = args[0][5]
        lat_long = args[0][6]
        location_code = None
        lat_long = lat_long.split(',')
        lat = float(lat_long[0])
        longi = float(lat_long[1])
        location_point = [longi, lat]
        # user_mgt_obj = UserManagement.objects.all()

        if is_hub:
            location_code = create_code_location(city_json['city_code'], 'H', None, city_obj[0].id)

        if c_location:
            customer_obj = Customer.objects.filter(customer_code__iexact=customer_code)
            customer = CustomerDataSerializer(customer_obj, many=True).data

            if len(customer):
                customer_data['id'] = customer[0]['id']
                customer_data['customer_code'] = customer[0]['customer_code']
                customer_data['company_name'] = customer[0]['company_name']
                customer_data['customer_name'] = customer[0]['customer_name']
                customer_id = customer[0]['id']
                location_code = create_code_location(city_json['city_code'], 'C', customer_data, city_obj[0].id)

            else:
                return False, 'Customer not found.'
                # location_code = create_code_location(city_json['city_code'], 'C', customer, city_obj[0].id)

        else:
            customer_id = None
        if is_toll_plaza:
            location_code = create_code_location(city_json['city_code'], 'T', None, city_obj[0].id)
        if is_refuel and not c_location and not is_hub and not is_toll_plaza:
            location_code = create_code_location(city_json['city_code'], 'R', None, city_obj[0].id)
        if is_route and not is_toll_plaza and not is_refuel and not is_hub and not c_location:
            location_code = create_code_location(city_json['city_code'], 'L', None, city_obj[0].id)

        try:
            LocationManagement(location_name=location_name, location_code=location_code,
                               location_address=location_address, location_pin=location_pin,
                               location_state=location_state,
                               location_radius=location_radius,
                               is_hub=is_hub, location_point=location_point, location_city=city_json,
                               customer_data=customer_data,
                               c_location=c_location, is_toll_plaza=is_toll_plaza, is_refuel=is_refuel,
                               is_route=is_route, customer_id=customer_id).save()

            return True, 'Successfully imported'
        except Exception as e:
            print(e)
            return False, 'Something went wrong'


################################################################
# Name - contract_export_import_page
#
##############################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def contract_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_contract': True})


################################################################
# Name - import_export_index_page
#
##############################################################
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def import_export_index_page(request):
    total_trip = Trip.objects.all().count()
    broker_count = Broker.objects.all().count()
    location_count = LocationManagement.objects.all().count()
    customer_count = Customer.objects.all().count()
    contract_count = Contract.objects.all().count()
    indent_count = Indent.objects.all().count()
    vehicle_type_count = VehicleType.objects.all().order_by('-id').count()
    market_driver_count = Driver.objects.filter(is_market_driver=True).count()
    market_vehicle_count = Vehicles.objects.filter(is_market_vehicle=True).count()
    return render(request, 'import_export/index.html', dict(zip(
        ['broker_count', 'location_count', 'customer_count', 'contract_count', 'market_driver_count',
         'market_vehicle_count', 'indent_count', 'vehicle_type_count', 'total_trip'],
        [broker_count, location_count, customer_count, contract_count, market_driver_count, market_vehicle_count,
         indent_count, vehicle_type_count, total_trip])))


################################################################
# Name - contract_export_import_export
#
##############################################################
def contract_export_import_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=contract.csv'
    writer = csv.DictWriter(response, fieldnames=contract_header_field)
    writer.writeheader()
    return response


################################################################
# Name - contract_export_import_export
#
##############################################################
@csrf_exempt
def contract_export_import_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == contract_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_contract_import(row)
                    if not done:
                        return render(request, 'import_export/contract_im_ex.html',
                                      {'contrct_import': 'import_contract', 'error': error, 'count': count})

            return render(request, 'import_export/contract_im_ex.html',
                          {'contrct_import': 'import_contract', 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/contract_im_ex.html',
                          {'contrct_import': 'import_contract', 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


################################################################
# Name - location_export_import_export
# Creation will here.
################################################################
def create_contract_import(*args):
    customer = {}
    customer_code = args[0][0]
    customer_obj = Customer.objects.get(customer_code__iexact=customer_code)
    customer_id = customer_obj.id
    start_point = args[0][1]
    destination_point = args[0][2]
    # Code Here
    start_point_obj = LocationManagement.objects.filter(location_code__iexact=start_point, customer_id=customer_id)
    destination_point_obj = LocationManagement.objects.filter(location_code__iexact=destination_point,
                                                              customer_id=customer_id)
    customer_detention_rate = args[0][3]
    broker_detention_rate = args[0][4]
    contract_rate = args[0][5]
    tat = args[0][6]
    loading_charges = args[0][7]
    unloading_charges = args[0][8]
    vehicle_type_id = args[0][9]
    billing_cycle = args[0][10]
    vehicle_type = {}
    print('------------>', args[0])
    try:
        # user_mgt_obj = UserManagement.objects.all()
        customer_obj = Customer.objects.filter(customer_code__iexact=customer_code)
        customer = CustomerDataSerializer(customer_obj, many=True).data
    except Exception as e:
        print('Data is not found.')
    if len(customer):

        customer_id = customer[0]['id']
    else:
        return False, 'Customer not found.'
    if len(vehicle_type_id):
        pass
        # vehicle_type['id'] = vehicle_type[0].id
    else:
        return False, 'Vehicle not found'
    start_point_dict = {'id': external_methods.return_trip_id(start_point_obj[0].id),
                        'location_code': start_point_obj[0].location_code,
                        'location_name': start_point_obj[0].location_name}
    destination_point_dict = {'id': external_methods.return_trip_id(destination_point_obj[0].id),
                              'location_code': destination_point_obj[0].location_code,
                              'location_name': destination_point_obj[0].location_name}

    if len(Contract.objects.filter(customer_id=customer_id, start_point__id=start_point_dict['id'],
                                   destination_point__id=destination_point_dict['id'],
                                   vehicle_type_id=vehicle_type_id)):
        return True, 'Already in system'
    else:
        try:
            Contract(customer_id=customer_id, start_point=start_point_dict, destination_point=destination_point_dict,
                     customer_detention_rate=customer_detention_rate, broker_detention_rate=broker_detention_rate,
                     contract_rate=contract_rate, tat=tat,
                     loading_charges=loading_charges, unloading_charges=unloading_charges,
                     vehicle_type_id=vehicle_type_id, billing_cycle=billing_cycle).save()
            return True, 'Successfully imported'

        except Exception as e:
            print(e)


################################################################
# Name - monthly contract_export_import_page
#
##############################################################
def monthly_contract_import_page(request):
    return render(request, 'import_export/monthly_contract_im_ex.html', {'error': '', 'count': ''})


################################################################
# Name - monthly-contract_export_import_export
#
##############################################################
def monthly_contract_export_import_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=monthly_contract.csv'
    writer = csv.DictWriter(response, fieldnames=monthly_contract_header_field)
    writer.writeheader()
    return response


################################################################
# Name - monthly_contract_export_import_export
#
##############################################################
@csrf_exempt
def monthly_contract_export_import_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == monthly_contract_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = monthly_create_contract_import(row)
                    if not done:
                        return render(request, 'import_export/monthly_contract_im_ex.html',
                                      {'monthly_contrct_import': 'import_monthly_contract', 'error': error,
                                       'count': count})

            return render(request, 'import_export/contract_im_ex.html',
                          {'monthly_contrct_import': 'import_monthly_contract', 'error': 'Successfully imported',
                           'count': ''})
        else:
            return render(request, 'import_export/contract_im_ex.html',
                          {'monthly_contrct_import': 'import_monthly_contract', 'error': 'Method is not allowed',
                           'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


################################################################
# Name - monthly_contract_export_import_export
# Creation will here.
################################################################
def monthly_create_contract_import(*args):
    customer = {}
    customer_code = args[0][0]
    customer_obj = Customer.objects.get(customer_code__iexact=customer_code)
    customer_id = customer_obj.id
    start_point = args[0][1]
    destination_point = args[0][2]
    # Code Here
    start_point_obj = LocationManagement.objects.filter(location_code__iexact=start_point, customer_id=customer_id)
    destination_point_obj = LocationManagement.objects.filter(location_code__iexact=destination_point,
                                                              customer_id=customer_id)
    new_contract_rate = args[0][11]
    vehicle_type_id = args[0][9]
    month = args[0][12]
    year = args[0][13]
    vehicle_type = {}
    print('------------>', args[0])
    try:
        # user_mgt_obj = UserManagement.objects.all()
        customer_obj = Customer.objects.filter(customer_code__iexact=customer_code)
        customer = CustomerDataSerializer(customer_obj, many=True).data
    except Exception as e:
        print('Data is not found.')
    if len(customer):

        customer_id = customer[0]['id']
    else:
        return False, 'Customer not found.'
    if len(vehicle_type_id):
        pass
        # vehicle_type['id'] = vehicle_type[0].id
    else:
        return False, 'Vehicle not found'
    start_point_dict = {'id': external_methods.return_trip_id(start_point_obj[0].id),
                        'location_code': start_point_obj[0].location_code,
                        'location_name': start_point_obj[0].location_name}
    destination_point_dict = {'id': external_methods.return_trip_id(destination_point_obj[0].id),
                              'location_code': destination_point_obj[0].location_code,
                              'location_name': destination_point_obj[0].location_name}
    contract_data = Contract.objects.get(customer_id=customer_id, vehicle_type_id=vehicle_type_id,
                                         start_point__id=start_point_dict['id'],
                                         destination_point__id=destination_point_dict['id'])
    contract_data_id = contract_data.id

    if contract_data_id:
        if len(ContractByMonth.objects.filter(contract_obj_id=contract_data_id, month=month, year=year)):
            return False, 'Monthly Contract is already created. Please edit on listing'
        else:
            try:
                ContractByMonth(new_contract_rate=new_contract_rate, month=month, year=year,
                                contract_obj_id=contract_data_id).save()
                return True, 'Successfully imported'

            except Exception as e:
                print(e)
    else:
        print('Try Again')


################################################################
# Name - market_vehicle_export_data
#
##############################################################
def market_vehicle_export_data(request):
    vehicle_obj = Vehicles.objects.filter(is_market_vehicle=True).order_by('id')
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=market_vehicle_data.csv'
    writer = csv.DictWriter(response, fieldnames=market_vehicle_header_field_data)
    writer.writeheader()
    x = iter(vehicle_obj)
    while True:
        try:
            z = next(x)
            writer.writerow(
                {'id': z.id, 'vehicle_no': z.vehicle_registration_number, 'vehicle_type': z.vehicle_type.name,
                 'insurance_data': z.insurance_data, 'fitness_data': z.fitness_data, 'rc_data': z.rc_data,
                 'permit_data': z.permit_data, 'vehicle_owner_data': z.vehicle_owner_data})
        except StopIteration:
            break
    return response


def market_vehicle_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=market_vehicle.csv'
    writer = csv.DictWriter(response, fieldnames=market_vehicle_header_field)
    writer.writeheader()
    return response


########################################################################################################################
# Name - market_driver_export_data
#
########################################################################################################################
def market_driver_export_data(request):
    driver_obj = Driver.objects.filter(is_market_driver=True).order_by('id')
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=market_driver_data.csv'
    writer = csv.DictWriter(response, fieldnames=market_driver_header_field_data)
    writer.writeheader()
    x = iter(driver_obj)
    while True:
        try:
            z = next(x)
            writer.writerow(
                {'id': z.id, 'driver_name': z.driver_name, 'driver_no': z.mobile_number,
                 'driver_father': z.father_name, 'driver_add': z.street, 'driver_dl_no': z.license_number,
                 'driver_dl_ex_date': z.license_expiry_date})
        except StopIteration:
            break
    return response


########################################################################################################################
# Name - vehicle_type_export_data_func
#
########################################################################################################################
def vehicle_type_export_data_func(request):
    vehicle_obj = VehicleType.objects.all().order_by('id')
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=vehicle_type_data.csv'
    writer = csv.DictWriter(response, fieldnames=vehicle_type_header_field_data)
    writer.writeheader()
    x = iter(vehicle_obj)
    while True:
        try:
            z = next(x)
            writer.writerow(
                {'id': z.id, 'name': z.name, 'is_delete': z.is_delete})
        except StopIteration:
            break
    return response


################################################################
# Name - market_driver_export
#
##############################################################
def market_driver_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=market_driver.csv'
    writer = csv.DictWriter(response, fieldnames=market_driver_header_field)
    writer.writeheader()
    return response


#######################################################################################################################
# Name - location_export_import_export
# Creation will here.
######################################################################################################################
def create_driver_market_import_function(*args):
    driver_name = args[0][0]
    mobile_number = args[0][1]
    from trip.auto_data_creation import market_driver_code
    if not len(Driver.objects.filter(Qp(driver_name__iexact=driver_name) & Qp(mobile_number__iexact=mobile_number))):
        try:
            license_expiry_date = datetime.datetime.strptime(args[0][5], '%Y-%m-%d')
        except:
            license_expiry_date = None
        driver_obj = Driver(driver_name=driver_name, mobile_number=mobile_number, driver_code=market_driver_code(),
                            is_market_driver=True, father_name=args[0][2], license_number=args[0][4],
                            license_expiry_date=license_expiry_date, street=args[0][3])
        driver_obj.save()
        return True, 'Successfully imported'
    else:
        return False, 'Already Data Exits'


########################################################################################################################
# Name - market_driver_import
# By Vishnu Badal
#
########################################################################################################################
@csrf_exempt
def market_driver_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            #  reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == market_driver_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_driver_market_import_function(row)
                    if not done:
                        return render(request, 'import_export/form_page.html',
                                      {'is_market_driver': True, 'error': error, 'count': count})

            return render(request, 'import_export/form_page.html',
                          {'is_market_driver': True, 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/form_page.html',
                          {'is_market_driver': True, 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


#######################################################################################################################
# Name - create_vehicle_market_import_function
# Creation will here.
######################################################################################################################
def create_vehicle_market_import_function(*args):
    vehicle_number = args[0][0]
    from trip.auto_data_creation import create_code_vehicle
    if not len(Vehicles.objects.filter(Qp(vehicle_registration_number__iexact=vehicle_number))):
        try:
            insurance_data = dict(
                zip(('insurance_no', 'start_date', 'end_date', 'pic_url'), [args[0][3], args[0][4], args[0][5], '']))

        except:
            insurance_data = {'insurance_no': '', 'pic_url': '', 'end_date': '', 'start_date': ''}

        try:
            fitness_data = {'certificate_no': args[0][6], 'pic_url': '', 'end_date': args[0][8],
                            'start_date': args[0][7]}
        except:
            fitness_data = {'certificate_no': '', 'pic_url': '', 'end_date': '', 'start_date': ''}

        try:
            permit_data = {'permit_no': args[0][9], 'pic_url': ''}
        except:
            permit_data = {'permit_no': '', 'pic_url': ''}
        try:
            rc_data = {'manufacturing_date': args[0][2], 'pic_url': ''}
        except:
            rc_data = {'manufacturing_date': '', 'pic_url': ''}
        try:
            vehicle_owner_data = {'contact_no': args[0][13], 'pan_card': args[0][11], 'name': args[0][10],
                                  'address': args[0][12]}
        except:
            vehicle_owner_data = {'contact_no': '', 'pan_card': '', 'name': '', 'address': ''}

        vehicle_obj = Vehicles(vehicle_registration_number=vehicle_number,
                               code=create_code_vehicle(code_veriable.market_vehicle_prefix, vehicle_number),
                               is_market_vehicle=True, insurance_data=insurance_data, fitness_data=fitness_data,
                               rc_data=rc_data, vehicle_owner_data=vehicle_owner_data,
                               vehicle_type_id=args[0][1], permit_data=permit_data)
        vehicle_obj.save()
        return True, 'Successfully imported'
    else:
        # pass
        return True, 'Already Data Exits'


########################################################################################################################
# Name - market_vehicle_import
# By Vishnu Badal
#
########################################################################################################################
@csrf_exempt
def market_vehicle_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == market_vehicle_header_field:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = create_vehicle_market_import_function(row)
                    if not done:
                        return render(request, 'import_export/form_page.html',
                                      {'is_market_vehicle': True, 'error': error, 'count': count})

            return render(request, 'import_export/form_page.html',
                          {'is_market_vehicle': True, 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/form_page.html',
                          {'is_market_vehicle': True, 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


######################################################################################################################
#
#
#
################################################################################################################
from trip.models import PaymentStatus, Trip
from trip.external_methods import get_payment_status_master
from trip.trip_external_operation import hub_closer_operations


@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def hub_export_import_page(request):
    return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'hub_closer': True})


@csrf_exempt
@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def hub_csv_close(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            payment_status_master = PaymentStatus.objects
            status_payment = get_payment_status_master(payment_status_master, 'Balance Paid')
            count = 0
            for row in reader:
                count += 1
                if count == 1:
                    if row == hub_closer_list:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:
                    done, error = hub_closer_operations(row=row, b=status_payment)
                    if not done:
                        return render(request, 'import_export/form_page.html',
                                      {'is_market_vehicle': True, 'error': error, 'count': count})

            return render(request, 'import_export/form_page.html',
                          {'hub_closer': True, 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/form_page.html',
                          {'hub_closer': True, 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        print(e)
    return HttpResponse("Try Again ")


########################################################################################################################
# Name - indent_create_import
# By - Vishnu
#
########################################################################################################################
from customer_care import import_export_used_module


@csrf_exempt
def indent_create_import(request):
    try:
        if request.method == 'POST':
            file_data = request.FILES['import_csv']
            reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
            # reader = csv.reader(file_data)
            count = 0
            for row in reader:
                count += 1
                if count == 1:

                    if row == indent_create_header_field_data:
                        # header_length = len(row)
                        continue
                    else:
                        return HttpResponse("Headers are incorrect")
                else:

                    objs = import_export_used_module.CompleteDataCreationImport(indent=row, count=count)
                    customer_obj, location_s, location_d, contarct = objs._check_customer_contract()
                    if not len(contarct):
                        continue
                    objs._create_indent(customer=customer_obj, s_point=location_s, d_point=location_d,
                                        contract=contarct[0])

                    done, error = 1, ''
                    if not done:
                        return render(request, 'import_export/form_page.html',
                                      {'is_indent': True, 'error': error, 'count': count})

            return render(request, 'import_export/form_page.html',
                          {'is_indent': True, 'error': 'Successfully imported', 'count': ''})
        else:
            return render(request, 'import_export/form_page.html',
                          {'is_indent': True, 'error': 'Method is not allowed', 'count': ''})
    except Exception as e:
        pass

    return render(request, 'import_export/form_page.html', {'is_indent': True, 'error': 'Required Field', 'count': ''})


def location_change_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=location_change.csv'
    writer = csv.DictWriter(response, fieldnames=location_change_header_field)
    writer.writeheader()
    return response


@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def location_change_page(request):
    if request.method == 'GET':
        return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_location_change': True})
    elif request.method == 'POST':
        file_data = request.FILES['import_csv']
        reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
        # reader = csv.reader(file_data)
        count = 0
        for row in reader:
            count += 1
            if count == 1:
                if row == location_change_header_field:
                    # header_length = len(row)
                    continue
                else:
                    return HttpResponse("Headers are incorrect")
            else:
                order_code = row[0].replace(' ', '')
                changed_source = row[1]
                changed_destination = row[2]
                order_obj = Order.objects.filter(order_id=order_code)
                if len(order_obj):
                    order_obj = order_obj.first()
                    company_name = order_obj.indent.customer.company_name
                    if changed_source != '':
                        changed_source_loc_obj = LocationManagement.objects.filter(location_name=changed_source,
                                                                                   customer_data__company_name=company_name)
                        if len(changed_source_loc_obj):
                            changed_source_loc_obj = changed_source_loc_obj.first()
                            start_point = {'location_code': changed_source_loc_obj.location_code,
                                           'location_name': changed_source_loc_obj.location_name,
                                           'location_point': {'type': 'Point',
                                                              'coordinates': changed_source_loc_obj.location_point},
                                           'location_city': changed_source_loc_obj.location_city,
                                           'id': str(changed_source_loc_obj.id)}
                        else:
                            error = 'Location not found of {} for customer {}'.format(changed_source, company_name)
                            return render(request, 'import_export/form_page.html',
                                          {'is_broker_rate': True, 'error': error, 'count': ''})
                    if changed_destination != '':
                        changed_dest_loc_obj = LocationManagement.objects.filter(location_name=changed_destination,
                                                                                 customer_data__company_name=company_name)
                        if len(changed_dest_loc_obj):
                            changed_dest_loc_obj = changed_dest_loc_obj.first()
                            destination_point = {'location_code': changed_dest_loc_obj.location_code,
                                                 'location_name': changed_dest_loc_obj.location_name,
                                                 'location_point': {'type': 'Point',
                                                                    'coordinates': changed_dest_loc_obj.location_point},
                                                 'location_city': changed_dest_loc_obj.location_city,
                                                 'id': str(changed_dest_loc_obj.id)}
                        else:
                            error = 'Location not found of {} for customer {}'.format(changed_destination, company_name)
                            return render(request, 'import_export/form_page.html',
                                          {'is_broker_rate': True, 'error': error, 'count': ''})

                    #  Change in Indent table

                    indent_obj = Indent.objects.filter(id=order_obj.indent_id)
                    for indent in indent_obj:
                        if changed_source != '':
                            indent.start_point = start_point
                            source_name = start_point['location_name']
                        else:
                            source_name = indent.start_point['location_name']
                        if changed_destination != '':
                            indent.destination_point = destination_point
                            destination_name = destination_point['location_name']
                        else:
                            destination_name = indent.destination_point['location_name']
                        contract_obj = Contract.objects.filter(start_point__location_name=source_name,
                                                               destination_point__location_name=destination_name,
                                                               vehicle_type_id=indent.vehicle_type_id)
                        if len(contract_obj):
                            indent.contract_id = contract_obj.first().id
                            indent.save()
                        else:

                            error = 'Contract not found of source {} and destination {} of vehicle type {}'.format(
                                source_name, destination_name, VehicleType.objects.get(id=indent.vehicle_type_id).name)
                            return render(request, 'import_export/form_page.html',
                                          {'is_broker_rate': True, 'error': error, 'count': ''})

                    # Change in trip order data

                    trip_obj = Trip.objects.get(order_id=order_obj.id)
                    if changed_source != '':
                        trip_obj.order_data['origin'] = start_point
                    if changed_destination != '':
                        trip_obj.order_data['destination'] = destination_point
                    trip_obj.save()

                    # Change in IndentOrderLogs

                    # Skipped as discussed with yogi sir.

                    # order_log_obj = IndentOrderLogs.objects.filter(order_id_id=order_obj.id)
                    # for i in  order_log_obj:
                    #     if i.order_data is not None:
                    #         if changed_source != '':
                    #             print i.order_data['origin']
                    #         if changed_destination != '':
                    #             print i.order_data['destination']
                    #

                    # Change in FinanceData

                    financedata_obj = FinanceData.objects.filter(trip_id=str(trip_obj.id), is_billing_generated=False)
                    if len(financedata_obj):
                        financedata_obj = financedata_obj.first()
                        if changed_source != '':
                            financedata_obj.general_info['origin'] = {
                                'location_code': changed_source_loc_obj.location_code,
                                'location_name': changed_source_loc_obj.location_name}
                        if changed_destination != '':
                            financedata_obj.general_info['destination'] = {
                                'location_code': changed_dest_loc_obj.location_code,
                                'location_name': changed_dest_loc_obj.location_name}

                        financedata_obj.save()

                    # Change in Financepayable table

                    financepayable_obj = FinancePayable.objects.filter(trip_id=str(trip_obj.id),
                                                                       is_billing_generated=False)
                    if len(financepayable_obj):
                        financepayable_obj = financepayable_obj.first()
                        if changed_source != '':
                            financepayable_obj.general_info['origin'] = {
                                'location_code': changed_source_loc_obj.location_code,
                                'location_name': changed_source_loc_obj.location_name}
                        if changed_destination != '':
                            financepayable_obj.general_info['destination'] = {
                                'location_code': changed_dest_loc_obj.location_code,
                                'location_name': changed_dest_loc_obj.location_name}
                        financepayable_obj.save()

                    # Change in Bill generation

                    # Skipped as discussed with vishnu sir.

        error = 'Successfully Operation done'
        return render(request, 'import_export/form_page.html',
                      {'is_broker_rate': True, 'error': error, 'count': ''})


def vehicle_change_export(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=vehicle_change.csv'
    writer = csv.DictWriter(response, fieldnames=vehicle_change_header_field)
    writer.writeheader()
    return response


@login_required(login_url='/')
@user_passes_test(lambda u: u.is_superuser, login_url='/')
def vehicle_change_page(request):
    if request.method == 'GET':
        return render(request, 'import_export/form_page.html', {'error': '', 'count': '', 'is_vehicle_change': True})
    elif request.method == 'POST':
        file_data = request.FILES['import_csv']
        reader = csv.reader(codecs.iterdecode(file_data, 'utf-8'))
        # reader = csv.reader(file_data)
        count = 0
        for row in reader:
            count += 1
            if count == 1:
                if row == vehicle_change_header_field:
                    continue
                else:
                    return HttpResponse("Headers are incorrect")
            else:
                order_code = row[0].replace(' ', '')
                vehicle_number = row[1]

                # Check category of both vehicle number and order code either market or own.

                order_obj = Order.objects.filter(order_id=order_code)
                old_vehicle = order_obj.first().vehicle.vehicle_registration_number
                trip_obj = Trip.objects.filter(order_id=order_obj.first().id)
                if trip_obj.first().trip_status['status_name'] == 'Completed':
                    vehicle_obj = Vehicles.objects.filter(vehicle_registration_number=vehicle_number)
                    if len(order_obj) and len(vehicle_obj):
                        if order_obj.first().float_to_market == True and vehicle_obj.first().is_market_vehicle == True:
                            pass
                        elif (
                                order_obj.first().float_to_market == False and vehicle_obj.first().is_market_vehicle == False):
                            pass
                        else:
                            error = 'Both Order code {} or Vehicle number {} should belong to same category. Either Market or Own.'.format(
                                order_code, vehicle_number)
                            return render(request, 'import_export/form_page.html',
                                          {'is_vehicle_change': True, 'error': error, 'count': ''})

                        # Change in order table.
                        order_obj.update(vehicle_id=vehicle_obj.first().id)
                        if len(trip_obj):
                            trip_obj = trip_obj.first()
                            trip_obj.order_data['vehicle_no'] = vehicle_number
                            trip_obj.save()
                            trip_id = trip_obj.id

                            # Change in finance data.
                            finance_data_obj = FinanceData.objects.filter(trip_id=trip_id)
                            if len(finance_data_obj):
                                finance_data_obj = finance_data_obj.first()
                                finance_data_obj.general_info['vehicle_no'] = vehicle_number
                                finance_data_obj.save()

                            # Change in finance payable data.
                            finance_pay_obj = FinancePayable.objects.filter(trip_id=trip_id)
                            if len(finance_pay_obj):
                                finance_pay_obj = finance_pay_obj.first()
                                finance_pay_obj.general_info['vehicle_no'] = vehicle_number
                                finance_pay_obj.save()

                            # Change in Entitypayment
                            ent_pay_obj = EntityPayments.objects.filter(trip_code__code=trip_obj.trip_code)
                            if len(ent_pay_obj):
                                ent_pay_obj.update(vehicle_id=vehicle_obj.first().id)

                            # Change in Transaction log
                            trans_log = TransactionLogs.objects.filter(trip_code__code=trip_obj.trip_code)
                            if len(trans_log):
                                # Check if context contains vehicle number
                                if len(Vehicles.objects.filter(vehicle_registration_number=trans_log.first().context)):
                                    trans_log.update(context=vehicle_number, updated_at=timezone.now(), updated_by = request.user.username)

                            # Create Indent log of vehicle change

                            try:
                                user_obj = UserManagement.objects.filter(user_id=request.user.id)
                                user_ser = UserManagementSerializer(user_obj, many=True).data
                                user_code = user_ser[0]['user_code']
                                user_first_name = user_ser[0]['user_manage_data']['first_name']
                                user_email = user_ser[0]['user_manage_data']['email']
                                action_data = {'previous_assigned_vehicle': old_vehicle,
                                               'modified_vehicle': vehicle_number}
                                IndentOrderLogs.objects.create(
                                    action='vehicle_changed',
                                    user={'name': user_first_name, 'code': user_code, 'email': user_email},
                                    action_data=action_data, timestamp=timezone.now(), order_id_id=order_obj.first().id,
                                    indent_id=order_obj.first().indent_id,
                                    created_at = timezone.now(),
                                    created_by = request.user.username
                                    )
                            except Exception as e:
                                pass



                else:
                    error = 'Order code {} or Vehicle number {} not found.'.format(order_code, vehicle_number)
                    return render(request, 'import_export/form_page.html',
                                  {'is_vehicle_change': True, 'error': error, 'count': ''})

        error = 'Successfully Operation done'
        return render(request, 'import_export/form_page.html',
                      {'is_vehicle_change': True, 'error': error, 'count': ''})
