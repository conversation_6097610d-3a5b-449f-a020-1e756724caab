angular.module('myApp')
    .controller('menuCtrl', ['$scope', 'AdminServices', '$filter', '$timeout', function ($scope, AdminServices, $filter, $timeout) {


            $scope.getSourceList = function () {
            AdminServices.getSourceList().then(function (response) {
            console.log("Source response ", response)
            $scope.trip_source_type = response.data
            $scope.placementType = ["Scheduled", "Adhoc"]

            },
            function (err) {
                        swal(err, "Error", "error");
                    })

            }


            $scope.podCalculation = function(data){
            // console.log("==>", data)

            if (data.pod_broker_data){

            if (data.pod_broker_data.detention != ""){
            var detention = parseFloat(data.pod_broker_data.detention)

            }
            else{
            var detention = 0

            }

            if (data.pod_broker_data.unloading_charge != ""){
            var unloading_charge = parseFloat(data.pod_broker_data.unloading_charge)

            }
            else{
            var unloading_charge = 0

            }


            if (data.pod_broker_data.taxes != ""){
            var taxes = parseFloat(data.pod_broker_data.taxes)

            }
            else{
            var taxes = 0

            }

            if (data.pod_broker_data.b_surcharge != ""){
            var b_surcharge = parseFloat(data.pod_broker_data.b_surcharge)

            }
            else{
            var b_surcharge = 0

            }

            if (data.pod_broker_data.loading_charge != ""){
            var loading_charge = parseFloat(data.pod_broker_data.loading_charge)

            }
            else{
            var loading_charge = 0

            }
            if (data.pod_broker_data.deduction_value != ""){
            var deduction_value = data.pod_broker_data.deduction_value

            }
            else{
            var deduction_value = 0

            }

            if (loading_charge == undefined){
            loading_charge = 0
            }
            if (b_surcharge == undefined){
            b_surcharge = 0
            }
            if (taxes == undefined){
            taxes = 0
            }
            if (unloading_charge == undefined){
            unloading_charge = 0
            }

            if (detention == undefined){
            detention = 0
            }
            if (deduction_value == undefined){
            deduction_value = 0
            }

            return (loading_charge+b_surcharge+taxes+unloading_charge+detention)-deduction_value





//                console.log("++>  ", data)

//                {{ trip.pod_data.pod_broker_data.detention * 1 }} +
//                                                {{ trip.pod_data.pod_broker_data.unloading_charge  * 1 }} +
//                                                {{ trip.pod_data.pod_broker_data.taxes * 1 }} +
//                                                {{ trip.pod_data.pod_broker_data.b_surcharge * 1 }} +
//                                                {{ trip.pod_data.pod_broker_data.loading_charge * 1 }} -
//                                                {{ trip.pod_data.pod_broker_data.deduction_value * 1 }}"

            }
            }

        /**TripFilter
         * functionName:getAdminTripMenu
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */

        $scope.checkIfCustRecon = function (data) {
            if (data) {
            }
            else {
                swal("Oops", 'Please select customer First.', "warning")
            }
        };

        $scope.destinationBasedOriginRecon = function (data) {
            $scope.destination = [];
            if (data === undefined || data === null) {
                $scope.filterData.destination = null;
            }
            if (data !== undefined && data !== null) {
                angular.forEach($scope.y, function (value, key) {
                    if (data.location_name === value.origin_data.location_name) {
                        $scope.destination.push({'location_name': value.destination_data.location_name})
                    }
                });
            }
            else {
                angular.forEach($scope.y, function (value, key) {
                    $scope.destination.push({'location_name': value.destination_data.location_name});
                });
            }
            var newArr = [];
            angular.forEach($scope.destination, function (value, key) {
                var exists = false;
                if (newArr.length) {
                    angular.forEach(newArr, function (val2, key) {
                        if (angular.equals(value.location_name, val2.location_name)) {
                            exists = true
                        }
                    });
                    if (exists === false) {
                        newArr.push(value);
                    }
                }
                else {
                    newArr.push(value);
                }

            });
            $scope.destination = newArr;
        };

        $scope.destinationLocationRecon = function (data, data_1) {
            if (data === undefined || data === null) {
                $scope.filterData.origin = null;
                $scope.origin = [];
                if (data_1 !== undefined && data_1 !== null) {
                    angular.forEach($scope.y, function (value, key) {
                        if (data_1.location_name === value.destination_data.location_name) {
                            $scope.origin.push({'location_name': value.origin_data.location_name})
                        }
                    });
                }

                if (data_1 === undefined || data_1 == null) {
                    $scope.filterData.origin = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.origin.push({'location_name': value.origin_data.location_name});
                    });
                }
                var newArr = [];
                angular.forEach($scope.origin, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value.location_name, val2.location_name)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    }
                    else {
                        newArr.push(value);
                    }
                });

                $scope.origin = newArr;
            }
            else {
                if (data_1 === undefined || data_1 == null) {
                    $scope.origin = [];
                    $scope.filterData.origin = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.origin.push({'location_name': value.origin_data.location_name});
                    });
                    var newArr = [];
                    angular.forEach($scope.origin, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value.location_name, val2.location_name)) {
                                    exists = true
                                }

                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        }
                        else {
                            newArr.push(value);
                        }

                    });
                    $scope.origin = newArr;
                    $scope.destination = [];
                    $scope.filterData.destination = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.destination.push({'location_name': value.destination_data.location_name});
                    });
                    var newArr = [];
                    angular.forEach($scope.destination, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value.location_name, val2.location_name)) {
                                    exists = true
                                }

                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        }
                        else {
                            newArr.push(value);
                        }
                    });
                    $scope.destination = newArr;
                }
            }
        };

        $scope.getCustomerListNewRecon = function () {
            $scope.customerName = [];
            AdminServices.getAllCustomerListsNew().then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.customerName = response.data.customer;
                } else {
                    if (response.data.code.status === 100) {
                        angular.forEach(response.data.customer, function (value, key) {
                            $scope.customerName.push(value._source)
                        });
                    }
                    else {
                        swal("Oops", response.data.code.message, "error");
                    }
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', "error")
            })
        };

        $scope.getLocationNewRecon = function (customerName) {
            $scope.origin = [];
            $scope.destination = [];
            if(Array.isArray(customerName)){
                $scope.filterData.origin = null;
                $scope.filterData.destination = null;
                if(customerName.length>1){
                    return;
                }
                customerName = customerName[0];
            }else{
                $scope.filterData.origin = null;
                $scope.filterData.destination = null;
            }

            AdminServices.getLocationListsNew(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.locationNmae = response.data.location;
                } else {
                    if (response.data.code.status === 100) {
                        $scope.y = response.data.location;
                        angular.forEach(response.data.com.origin_data, function (value, key) {
                            $scope.origin.push({'location_name': value});
                        });
                        angular.forEach(response.data.com.destination_data, function (value, key) {
                            $scope.destination.push({'location_name': value});
                        });
                    }
                    else {
                        swal("Oops", response.data.code.message, "error");
                    }
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };

        $scope.TripDataList = {};
        $scope.trip_list = [];
        $scope.adminTripMenu = function (count) {
            $scope.loading = true;
            AdminServices.getAdminTripMenu(count).then(function (response) {
                $scope.loading = false;
                console.log(response);
                if (response.data.results.code.status === 200) {
                    $scope.TripDataList.count = response.data.count;
                    $scope.trip_list = response.data.results.trip_data;
                    $scope.wf = response.data.results.wf;
                } else {
                    swal('oops!', response.results.code.message)
                }
            })
        };

        $scope.dataBindD = function (data_0, data_2, data_3) {
            var add = ' ---> ';
            angular.forEach(data_2, function (value, key) {
                add += value.destination_point.location_name + ' ---> '
            });
            return data_0 + add + data_3
        };

        $scope.filterData = {
            vehicle_type: '',
            customer: '',
            origin: '',
            destination: '',
            broker: '',
            placement_date: '',
            to_placement_date: '',
            s_from_date: '',
            s_to_date: '',
            placement_type: ''

        };

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([
                //function (callback) {
                //    AdminServices.getAllCustomerLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.customerName = response.data.customer;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},

                //function (callback) {
                //    //request no 3
                //    AdminServices.getAllLocationLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.locationNmae = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},

                //function (callback) {
                //    //request no 4
                //    AdminServices.getAllBrokerList().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.brokerName = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},

                function (callback) {
                    //request no 5
                    AdminServices.getVehicleType().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleType = response.data.vehicle_type;
                            console.log('$scope.vehicleType', $scope.vehicleType)
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }

                //function (callback) {
                //    //request no 5
                //    AdminServices.getAllVehicleLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.vehicleNo = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //}

            ])
        };

        $scope.getLocation = function (customerData) {
            AdminServices.getLocationLists(customerData).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.origin_location = response.data.location;
                    $scope.destination_location = response.data.location;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        $scope.filter_applied = false;
        $scope.checkCustomerSelected = function (customerName) {
            $scope.customerData = customerName;
            if (customerName != '' && customerName != undefined) {
                if ($scope.filter_applied === false) {
                    $scope.getLocation($scope.customerData);
                }

            }
            else {
                //swal("Oops", 'Select Customer First!', "error")
                $scope.showAllBroker();
                $scope.showAllVehicles();
            }
        };


        $scope.disable_uiselect = false;
        $scope.getFilterData = function (customer_data, origin_data, destination_data) {

            $scope.disable_uiselect = true;
            if (origin_data === undefined) {
                origin_data = '';
            }
            if (destination_data === undefined) {
                destination_data = '';
            }

            if (customer_data === undefined) {
                customer_data = '';
            }

            var filter_data_obj = {
                'customer_data': customer_data,
                'origin_data': origin_data,
                'destination_data': destination_data
            };

            AdminServices.getFilterDataService(filter_data_obj).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.filter_applied = true;
                    $scope.destination_location = response.data.dest_location;
                    $scope.origin_location = response.data.origin_location;
                    $scope.brokerName = response.data.broker_list;
                    $scope.vehicleNo = response.data.vehicle_numbers;
                    console.log('$scope.vehicleNo', $scope.vehicleNo)
                    $scope.disable_uiselect = false;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })

        };

        $scope.showAllLocationCustomer = function (customerData) {
            if (customerData != '' && customerData != undefined) {
                AdminServices.getLocationLists(customerData).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.origin_location = response.data.location;
                        $scope.destination_location = response.data.location;
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            }
            else {
                swal("Oops", 'Select Customer First!', "error")
            }


        };

        $scope.getAllDriversList = function () {
            AdminServices.getAllDriversListForTripFilters().then(function(response){
                if (response.data.code.status === 200) {
                    $scope.trip_reconciliations_driver_filter_list = response.data.result.drivers;
                }
            })
        }

        $scope.setTripReconFilters = function (customer) {
            let customerFilterObject = {
                vehicle_type: '',
                customer: customer,
                origin: '',
                destination: '',
                broker: '',
                placement_date: '',
                to_placement_date: '',
                s_from_date: '',
                s_to_date: ''
            }
            if(!customer){
                $scope.filterData = Object.assign({}, customerFilterObject);
            }
            if (customer) {
                if(Array.isArray(customer)){
                    if(customer.length<=1){
                        $scope.filterData = Object.assign({}, customerFilterObject);
                    }else{
                        $scope.filterData['customer'] = customer;
                        $scope.filterData.origin = null;
                        $scope.filterData.destination = null;
                    }
                }else{
                    $scope.filterData = Object.assign({}, customerFilterObject);
                }
            } 
        }

        $scope.getBrokerNewRecon = function (customer) {
            $scope.setTripReconFilters(customer);
            $scope.brokerName = [];
            var id = {'customer_id': null};
            if (customer) {
                if(Array.isArray(customer)){
                    if(customer.length===1) id['customer_id'] = customer[0].id;
                }else{
                    id['customer_id'] = customer.id;
                }
            }
            AdminServices.getAllBrokerListNew(id).then(function (response) {
                if ([100, 200].includes(response.data.code.status)) {
                    $scope.brokerName = response.data.data_list;
                    if (customer) {
                        var newArr = [];
                        $scope.brokx = response.data.data_list;
                        angular.forEach($scope.brokerName, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value._source.broker_data.broker_code, val2._source.broker_data.broker_code)) {
                                        exists = true
                                    }
                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            }
                            else {
                                newArr.push(value);
                            }
                        });
                        $scope.brokerName = newArr;
                    }
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.getVehNoNewRecon = function (customer) {
            console.log('cust------>', customer);
            $scope.setTripReconFilters(customer);
            $scope.vehicleNo = [];
            var id = null;
            if (customer) {
                if(Array.isArray(customer)){
                    if(customer.length===1) id = customer[0].id;
                }else{
                    id = customer.id;
                }
            }
            AdminServices.getAllVehNoListNew(id).then(function (response) {
                console.log('response_vehno', response);
                if ([100, 200].includes(response.data.code.status)) {
                    $scope.vehicleNo = response.data.data_list;
                    if (customer) {
                        var newArr = [];
                        $scope.vehnox = response.data.data_list;
                        angular.forEach($scope.vehicleNo, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                        exists = true
                                    }
                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            }
                            else {
                                newArr.push(value);
                            }
                        });
                        $scope.vehicleNo = newArr;
                    }
                    console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.oDBasedBroker = function (customer) {
            var newArr = [];
            if ($scope.filterData.origin && $scope.filterData.destination) {
                angular.forEach($scope.brokx, function (value, key) {
                    if ((angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) &&
                        (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name))) {
                        newArr.push(value);
                    }
                });
            }
            else if ($scope.filterData.origin) {
                angular.forEach($scope.brokx, function (value, key) {
                    if (angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) {
                        newArr.push(value);
                    }
                });

            }
            else if ($scope.filterData.destination) {
                angular.forEach($scope.brokx, function (value, key) {
                    if (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name)) {
                        newArr.push(value);
                    }
                });
            }
            else if (!$scope.filterData.origin && !$scope.filterData.destination) {
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    placement_date: '',
                    to_placement_date: '',
                    s_from_date: '',
                    s_to_date: ''

                };
                $scope.brokerName = [];
                angular.forEach($scope.brokx, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value._source.broker_data.broker_code, val2._source.broker_data.broker_code)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    }
                    else {
                        newArr.push(value);
                    }
                });
            }
            $scope.brokerName = newArr;
        };

        $scope.oDBasedVehNo = function (customer) {
            //console.log('filterdata', $scope.filterData);
            var newArr = [];
            if ($scope.filterData.origin && $scope.filterData.destination) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if ((angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) &&
                        (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name))) {
                        newArr.push(value);
                    }
                });
            }
            else if ($scope.filterData.origin) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if (angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) {
                        newArr.push(value);
                    }
                });

            }
            else if ($scope.filterData.destination) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name)) {
                        newArr.push(value);
                    }
                });
            }
            else if (!$scope.filterData.origin && !$scope.filterData.destination) {
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    placement_date: '',
                    to_placement_date: '',
                    s_from_date: '',
                    s_to_date: ''

                };
                $scope.vehicleNo = [];
                angular.forEach($scope.vehnox, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    }
                    else {
                        newArr.push(value);
                    }
                });
            }
            $scope.vehicleNo = newArr;
            //console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
        };


        $scope.showAllVehicles = function () {
            AdminServices.getAllVehicleLists().then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.vehicleNo = response.data.data_list;
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.originLoading = false;
        $scope.destinationLoading = false;
        $scope.originDebounceTimer = null;
        $scope.destinationDebounceTimer = null;

        $scope.fetchOriginLocation = function (originStr) {
            if (!$scope.filterData.customer?.[0]?.id) {
                swal("Oops", 'Please select customer First.', "warning")
                return Promise.resolve([]);
            }

            if (!originStr || originStr.length < 2) {
                return Promise.resolve([]);
            }

            if ($scope.originDebounceTimer) {
                $timeout.cancel($scope.originDebounceTimer);
            }

            // Return a promise that resolves with the data
            return new Promise(function (resolve) {
                $scope.originDebounceTimer = $timeout(function () {
                    $scope.originLoading = true;
                    AdminServices.fetchCustomerLocation($scope.filterData.customer?.[0]?.id, originStr, '')
                        .then(function (response) {
                            $scope.originLoading = false;
                            if (response.data.code.status === 200) {
                                resolve(response.data?.com?.origin_data || []);
                            } else {
                                resolve([]);
                            }
                        })
                        .catch(function (error) {
                            $scope.originLoading = false;
                            console.error('Origin API error:', error);
                            resolve([]);
                        });
                }, 500);
            });
        };

        $scope.fetchDestinationLocation = function (destStr) {
            if (!$scope.filterData.customer?.[0]?.id) {
                swal("Oops", 'Please select customer First.', "warning")
                return Promise.resolve([]);
            }

            if (!destStr || destStr.length < 2) {
                return Promise.resolve([]);

            }

            if ($scope.destinationDebounceTimer) {
                $timeout.cancel($scope.destinationDebounceTimer);
            }

            return new Promise(function (resolve) {
                $scope.destinationDebounceTimer = $timeout(function () {
                    $scope.destinationLoading = true;
                    AdminServices.fetchCustomerLocation($scope.filterData.customer?.[0]?.id, '', destStr)
                        .then(function (response) {
                            $scope.destinationLoading = false;
                            if (response.data.code.status === 200) {
                                resolve(response.data?.com?.destination_data || []);
                            } else {
                                resolve([]);
                            }
                        })
                        .catch(function (error) {
                            $scope.destinationLoading = false;
                            console.error('Destination API error:', error);
                            resolve([]);
                        });
                }, 500);
            });
        };

        /**
         * functionName:TripFilter
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/08/2017
         * testerName:
         * testDate:
         */

        $scope.pagination = {};
        $scope.filterData = {
            vehicle_type: '',
            customer: '',
            origin: '',
            destination: '',
            broker: '',
            placement_date: '',
            to_placement_date: '',
            is_market: '',
            s_from_date: '',
            s_to_date: '',
            vr_from_date: '',
            vr_to_date: '',
            vehicle_number: '',
            order_trip_code: '',
            placement_type: '',
            payment_status_id: '',
        };
        $scope.TripFilter = function (count, filterData) {
            console.log('filterdata', filterData);
            $scope.loading = true;
            $scope.vehicle_type = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.placement_date = '';
            $scope.to_placement_date = '';
            $scope.s_from_date = '';
            $scope.s_to_date = '';
            $scope.broker_id = '';
            $scope.customer_id = '';
            $scope.is_market = '';
            $scope.vr_from_date = '';
            $scope.vr_to_date = '';
            $scope.vehicle_no = '';
            $scope.trip_type = '';
            $scope.order_trip_code = '';
            $scope.source_name = '';
            $scope.placement_type = '';
            $scope.trip_recon_primary_driver = '';
            $scope.trip_recon_secondary_driver = '';
            $scope.payment_status_id = '';

            if (filterData.source_name){

                $scope.source_name = filterData.source_name.name;
            }


            if (filterData.vehicle_type)
                $scope.vehicle_type = filterData.vehicle_type.name;
            if (filterData.origin)
                $scope.origin_name = filterData.origin;
            if (filterData.destination)
                $scope.destination_name = filterData.destination;
            if (filterData.placement_date)
                $scope.placement_date = filterData.placement_date;
            if (filterData.to_placement_date)
                $scope.to_placement_date = filterData.to_placement_date;
            if (filterData.s_from_date)
                $scope.s_from_date = filterData.s_from_date;
            if (filterData.s_to_date)
                $scope.s_to_date = filterData.s_to_date;
            if (filterData.vr_from_date)
                $scope.vr_from_date = filterData.vr_from_date;
            if (filterData.vr_to_date)
                $scope.vr_to_date = filterData.vr_to_date;
            if (filterData?.broker && filterData.broker?._source?.broker_data?.id)
                $scope.broker_id = filterData.broker._source.broker_data.id;
            if (filterData.trip_type)
                $scope.trip_type = filterData.trip_type;
            if (filterData?.customer && filterData.customer?.id)
                $scope.customer_id = filterData.customer.id;
            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;
             if (filterData.placement_type)
                $scope.placement_type = filterData.placement_type;
            if (filterData.is_market) {
                if (filterData.is_market == 'Own Trip') {
                    $scope.is_market = false;
                }
                if (filterData.is_market == 'Market Trip') {
                    $scope.is_market = true;
                }
            }
            if (filterData.order_trip_code)
                $scope.order_trip_code = filterData.order_trip_code;
            
            if (filterData.payment_status)
                $scope.payment_status_id = filterData.payment_status.id;

            // Deny filter if date range is more then 30 days.
            if (filterData.s_from_date && filterData.s_to_date){
                var sfd = filterData.s_from_date.split('-');
                var std = filterData.s_to_date.split('-');
                //1/1/2000
                //2019-03-07
                var start_from_date = new Date(sfd[0], sfd[1]-1, sfd[2]);
                var start_end_date = new Date(std[0], std[1]-1, std[2]);
                var diff = Math.round((start_end_date-start_from_date)/(1000*60*60*24));
                if (diff > 181){
                    swal("Oops", 'Filter can not applied. Date range should not be exceed then 6 months.', "error");
                    $scope.loading = false;
                    return false
                }
            }

            if (filterData.placement_date && filterData.to_placement_date){
                var pd = filterData.placement_date.split('-');
                var tpd = filterData.to_placement_date.split('-');
                //1/1/2000
                //2019-03-07
                var placement_start_date = new Date(pd[0], pd[1]-1, pd[2]);
                var placement_end_date = new Date(tpd[0], tpd[1]-1, tpd[2]);
                var diff_place = Math.round((placement_end_date - placement_start_date)/(1000*60*60*24));
                if (diff_place > 181){
                    swal("Oops", 'Filter can not applied. Date range should not be exceed then 6 months.', "error");
                    $scope.loading = false;
                    return false
                }
            }

            if (filterData.vr_from_date && filterData.vr_to_date){
                var vfd = filterData.vr_from_date.split('-');
                var vtd = filterData.vr_to_date.split('-');
                //1/1/2000
                //2019-03-07
                var required_start_date = new Date(vfd[0], vfd[1]-1, vfd[2]);
                var required_end_date = new Date(vtd[0], vtd[1]-1, vtd[2]);
                var diff_req = Math.round((required_end_date - required_start_date)/(1000*60*60*24));
                if (diff_req > 181){
                    swal("Oops", 'Filter can not applied. Date range should not be exceed then 6 months.', "error");
                    $scope.loading = false;
                    return false
                }
            }

            if (filterData.primary_driver){
                $scope.trip_recon_primary_driver = filterData.primary_driver.driver_code;
            }

            if (filterData.secondary_driver){
                $scope.trip_recon_secondary_driver = filterData.secondary_driver.driver_code;
            }
            var brokerIdList = [],  customerIdList = [];
            if (filterData?.broker)
                brokerIdList = filterData.broker.map(el=>el._source.broker_data.id);
            if (filterData?.customer)
                customerIdList = filterData.customer.map(el=>el.id);

            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_type: $scope.vehicle_type,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.placement_date,
                to_date: $scope.to_placement_date,
                s_from_date: $scope.s_from_date,
                s_to_date: $scope.s_to_date,
                broker: $scope.broker_id,
                is_market: $scope.is_market,
                vr_from_date: $scope.vr_from_date,
                vr_to_date: $scope.vr_to_date,
                vehicle_no: $scope.vehicle_no,
                trip_type: $scope.trip_type,
                order_trip_code: $scope.order_trip_code,
                source_name: $scope.source_name,
                placement_type: $scope.placement_type,
                pd_code: $scope.trip_recon_primary_driver,
                sd_code: $scope.trip_recon_secondary_driver,
                payment_status_id: $scope.payment_status_id,
                broker_ids: brokerIdList,
                customer_ids: customerIdList
            };

            console.log('$scope.filterDataObjec', $scope.filterDataObject);
            AdminServices.TripFilterService(count, $scope.filterDataObject).then(function (response) {
                console.log('response', response);
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {
                    $scope.trip_list = response.data.results.trip_data;
                    $scope.wf = response.data.results.wf;
                    $scope.TripDataList.count = response.data.count;
                } else {
                    alert(response.data.results.code.message)
                }
            })
        };
        $scope.clearFilter = function () {
            $scope.searchText = '';
            $scope.pagination.current = 1;
            $scope.adminTripMenu();
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                s_from_date: '',
                s_to_date: '',
                vehicle_no: '',
                vr_from_date: '',
                vr_to_date: '',
                placement_type: '',
                primary_driver: '',
                secondary_driver: ''
            };
        };


        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.trip_list, query).length;
            });
        };

        $scope.updateTripCsvCurrent = function () {
            $scope.csvUpdateLoading = true;
            AdminServices.updateTripCurrentCSV().then(function (response) {
                $scope.csvUpdateLoading = false;
                if (response.data.code.status === 200) {
                    swal("Good job!", response.data.code.message, "success");
                }
                else {
                    swal("Oops!", response.data.code.message, "error")
                }
            })
        };

        $scope.completedExportData = function (filterData) {
            $scope.vehicle_type = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.placement_date = '';
            $scope.to_placement_date = '';
            $scope.broker_id = '';
            $scope.customer_id = '';
            $scope.is_market = '';
            $scope.s_from_date = '';
            $scope.s_to_date = '';
            $scope.vr_from_date = '';
            $scope.vr_to_date = '';
            $scope.vehicle_no = '';
            $scope.trip_type = '';
            $scope.order_trip_code = '';
            $scope.placement_type = '';


            if (filterData.vehicle_type)
                $scope.vehicle_type = filterData.vehicle_type.name;
            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;
            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;
            if (filterData.placement_date)
                $scope.placement_date = filterData.placement_date;
            if (filterData.to_placement_date)
                $scope.to_placement_date = filterData.to_placement_date;
            if (filterData.s_from_date)
                $scope.s_from_date = filterData.s_from_date;
            if (filterData.s_to_date)
                $scope.s_to_date = filterData.s_to_date;
            if (filterData.broker)
                $scope.broker_id = filterData.broker._source.broker_data.id;
            if (filterData.trip_type)
                $scope.trip_type = filterData.trip_type;
            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;
            if (filterData.is_market) {
                if (filterData.is_market == 'Own Trip') {
                    $scope.is_market = false;
                }
                if (filterData.is_market == 'Market Trip') {
                    $scope.is_market = true;
                }
            }
            if (filterData.placement_type) {
                $scope.placement_type = filterData.placement_type;
            }
            if (filterData.vr_from_date)
                $scope.vr_from_date = filterData.vr_from_date;
            if (filterData.vr_to_date)
                $scope.vr_to_date = filterData.vr_to_date;
            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;
            if (filterData.order_trip_code)
                $scope.order_trip_code = filterData.order_trip_code;


            // Deny filter if date range is more then 30 days.
            if (filterData.s_from_date && filterData.s_to_date){
                var sfd = filterData.s_from_date.split('-');
                var std = filterData.s_to_date.split('-');
                //1/1/2000
                //2019-03-07
                var start_from_date = new Date(sfd[0], sfd[1]-1, sfd[2]);
                var start_end_date = new Date(std[0], std[1]-1, std[2]);
                var diff = Math.round((start_end_date-start_from_date)/(1000*60*60*24));
                if (diff > 30){
                    swal("Oops", 'Export data can not applied. Date range should be less then 30 days.', "error");
                    $scope.loading = false;
                    return false
                }
            }

            if (filterData.placement_date && filterData.to_placement_date){
                var pd = filterData.placement_date.split('-');
                var tpd = filterData.to_placement_date.split('-');
                //1/1/2000
                //2019-03-07
                var placement_start_date = new Date(pd[0], pd[1]-1, pd[2]);
                var placement_end_date = new Date(tpd[0], tpd[1]-1, tpd[2]);
                var diff_place = Math.round((placement_end_date - placement_start_date)/(1000*60*60*24));
                if (diff_place > 30){
                    swal("Oops", 'Export data can not applied. Date range should be less then 30 days.', "error");
                    $scope.loading = false;
                    return false
                }
            }

            if (filterData.vr_from_date && filterData.vr_to_date){
                var vfd = filterData.vr_from_date.split('-');
                var vtd = filterData.vr_to_date.split('-');
                //1/1/2000
                //2019-03-07
                var required_start_date = new Date(vfd[0], vfd[1]-1, vfd[2]);
                var required_end_date = new Date(vtd[0], vtd[1]-1, vtd[2]);
                var diff_req = Math.round((required_end_date - required_start_date)/(1000*60*60*24));
                if (diff_req > 30){
                    swal("Oops", 'Export data can not applied. Date range should be less then 30 days.', "error");
                    $scope.loading = false;
                    return false
                }
            }
            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_type: $scope.vehicle_type,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.placement_date,
                to_date: $scope.to_placement_date,
                s_from_date: $scope.s_from_date,
                s_to_date: $scope.s_to_date,
                broker: $scope.broker_id,
                is_market: $scope.is_market,
                vr_from_date: $scope.vr_from_date,
                vr_to_date: $scope.vr_to_date,
                trip_type: $scope.trip_type,
                vehicle_no: $scope.vehicle_no,
                order_trip_code: $scope.order_trip_code,
                placement_type: $scope.placement_type
            };
            $scope.csvloading = true;
            AdminServices.postCompletedExportData($scope.filterDataObject).then(function (response) {
                console.log('menu', response);
                $scope.csvloading = false;
                if (response.data.code.status === 200) {
                    $scope.completedExport = response.data.trip_data;
                    $scope.downloadCompletedCsv = [];
                    var total_cost = '';
                    var total_rev = '';
                    var p = '';
                    var trip_t = 'Valid';
                    angular.forEach($scope.completedExport, function (value, key) {
                        var waypoints = '';
                        if (value.order_data.multiple_stopage) {
                            angular.forEach(value.order_data.multiple_stopage, function (value, key) {
                                console.log('s_location', value.destination_point.location_name);
                                waypoints = waypoints + value.destination_point.location_name + '-->';
                            })
                        }
                        $scope.lr_no = "";
                        angular.forEach(value.lr.lr_data, function (value, key) {
                            $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                        });
                        $scope.lr_no = $scope.lr_no.slice(0, -1);
                        // Gross Cost calculation
                        var gross_cost = '';
                        if (value.pod_data.pod_broker_data.b_surcharge) {
                            b_surcharge = value.pod_data.pod_broker_data.b_surcharge;
                        }
                        else {
                            b_surcharge = 0.0
                        }

                        trip_t = value.trip_validity;

                        if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                            gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                            gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    value.pod_data.pod_broker_data.unloading_charge * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1 +
                                    value.pod_data.pod_broker_data.loading_charge * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                            gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.unloading_charge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                            gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1)) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.loading_charge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {

                            //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod

                            //gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) +
                            //    ((value.broker_data.broker_rate * 1 * value.broker_data.broker_tds) / 100)
                            gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)

                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {

                            gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                                (((value.broker_data.broker_rate * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1 ) *
                                    value.pod_data.pod_broker_data.tds) / 100 )
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                            gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.unloading_charge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100 )
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                            gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.loading_charge * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {

                            gross_cost = (value.broker_data.balance * 1) +
                                (((value.broker_data.broker_rate * 1 +
                                    value.pod_data.pod_broker_data.unloading_charge * 1 +
                                    b_surcharge * 1 +
                                    value.pod_data.pod_broker_data.loading_charge * 1 +
                                    value.pod_data.pod_broker_data.detention * 1 +
                                    value.pod_data.pod_broker_data.taxes * 1 ) *
                                    value.pod_data.pod_broker_data.tds) / 100)
                        }

                        if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {
                            //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod
                            //gross_cost = (value.broker_data.balance * 1) + ((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1) / 100)

                            gross_cost = (value.broker_data.balance * 1)
                        }


                        if (value.is_market == true && value.trip_status.status_name != "Completed") {

                            gross_cost = (value.broker_data.broker_rate * 1)


                            //gross_cost = (value.broker_data.broker_rate * 1) +
                            //    (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100)
                        }

                        if (value.is_market == false) {
                            gross_cost = (value.actual_data.total_all * 1)
                        }


                        //calculation of tds
                        var tds = 'N.A.';
                        if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == true) {
                            tds = ((((value.broker_data.broker_rate * 1) +
                                (value.pod_data.pod_broker_data.detention * 1) +
                                (b_surcharge * 1) +
                                (value.pod_data.pod_broker_data.taxes * 1 )) *
                                (value.pod_data.pod_broker_data.tds)) / 100 + ((
                                (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                (value.pod_data.pod_broker_data.loading_charge * 1)) / 100))
                        }

                        if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == false) {

                            tds = ((((value.broker_data.broker_rate * 1) +
                                (value.pod_data.pod_broker_data.detention * 1) +
                                (b_surcharge * 1) +
                                (value.pod_data.pod_broker_data.loading_charge * 1) +
                                (value.pod_data.pod_broker_data.taxes * 1 )) *
                                (value.pod_data.pod_broker_data.tds)) / 100 + (
                                (value.pod_data.pod_broker_data.unloading_charge * 1) / 100))
                        }

                        if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == true) {

                            tds = ((((value.broker_data.broker_rate * 1) +
                                (value.pod_data.pod_broker_data.detention * 1) +
                                (b_surcharge * 1) +
                                (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                (value.pod_data.pod_broker_data.taxes * 1 )) *
                                (value.pod_data.pod_broker_data.tds)) / 100 + (
                                (value.pod_data.pod_broker_data.loading_charge * 1) / 100))
                        }

                        if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == false) {

                            tds = ((((value.broker_data.broker_rate * 1) +
                                (value.pod_data.pod_broker_data.detention * 1) +
                                (b_surcharge * 1) +
                                (value.pod_data.pod_broker_data.loading_charge * 1) +
                                (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                (value.pod_data.pod_broker_data.taxes * 1 )) *
                                (value.pod_data.pod_broker_data.tds)) / 100)
                        }

                        if (value.pod_status == 'Pending' && value.is_market != false) {
                            tds = (( value.broker_data.broker_rate * 1) * value.broker_data.broker_tds) / 100
                        }

                        if (value.trip_status.status_name == "Completed") {
                            total_rev = value.customer_pay

                        }
                        if (value.trip_status.status_name != "Completed") {
                            total_rev = value.contract
                        }

                        var advance_deductions = 0;
                        if (value.advance_deductions == null) {
                            advance_deductions = 0
                        }
                        else {
                            advance_deductions = value.advance_deductions
                        }

                        if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                            p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1)
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                            p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                            p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                            p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                        }


                        if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                            p = (value.customer_pay * 1) - (value.broker_data.balance * 1 )
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                            p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                            p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                        }
                        if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                            p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                        }


                        if (value.is_market == true && value.trip_status.status_name != "Completed") {
                            p = (value.contract * 1) - ((value.broker_data.broker_rate * 1) - (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100) - advance_deductions)
                        }
                        if (value.is_market == false && value.trip_status.status_name == "Completed") {
                            p = (value.customer_pay * 1) - (value.actual_data.total_all * 1)
                        }
                        if (value.is_market == false && value.trip_status.status_name != "Completed") {
                            p = (value.contract * 1) - (value.actual_data.total_all * 1)
                        }
                        if (value.trip_end_date === null) {
                            $scope.trip_end_date = ''
                        } else {
                            $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                        }

                        if (value.is_market === true) {
                            $scope.market = 'yes';
                            total_cost = parseFloat(gross_cost * 1) - parseFloat(tds * 1);
                            $scope.total_adv = value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1;

                            console.log("==> total_cost", total_cost)
                        } else {
                            $scope.market = 'no';
                            total_cost = gross_cost;
                            try {
                                $scope.total_adv = value.adv_data.total_adv;
                            }
                            catch (err) {
                                $scope.total_adv = 'N.A.'
                            }
                        }
                        if (value.lane.lane_code === undefined) {
                            $scope.route = ''
                        } else {
                            $scope.route = value.lane.lane_code
                        }
                        var s_date;
                        var s_time;
                        var t_code;
                        try {
                            s_date = value.trip_start_date.split('T')[0]
                        }
                        catch (err) {
                            s_date = ''
                        }
                        try {
                            s_time = value.trip_start_time.split('T')[1].match(/.{5}/g)[0]
                        }
                        catch (err) {
                            s_time = ''
                        }
                        if (value.trip_code == null) {
                            t_code = 'N/A'
                        }
                        else {
                            t_code = value.trip_code
                        }


                        if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                            origin_c = 'Flipkart Luhari'
                        }
                        else {
                            origin_c = (value.order_data.origin.location_name).replace('<', ' ').replace('>', '')
                        }
                        if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                            des_c = 'Flipkart Ludhiana'
                        }
                        else {
                            des_c = value.order_data.destination.location_name.replace('<', ' ').replace('>', '')
                        }

                        if (value.broker_bal_paid_date != null) {
                            var broker_bal_paid_date = value.broker_bal_paid_date.split('T')[0];
                        }
                        else {
                            broker_bal_paid_date = '';
                        }

                        // Calculate time taken in between operational events

                        try {
                            if (value.indent_datetime != null && value.vehicle_placement_datetime != null) {
                                var diff = (new Date(value.vehicle_placement_datetime).getTime() - new Date(value.indent_datetime).getTime()) / 1000;
                                console.log('value.vehicle_req_datetime', value.vehicle_req_datetime);
                                console.log('value.vehicle_placement_datetime', value.vehicle_placement_datetime);
                                diff /= 60;
                                var total_min = Math.round(diff);
                                var minutes = total_min % 60;
                                var hours = (total_min - minutes) / 60;
                                var placement_indent = hours + ":" + Math.abs(minutes)
                            }
                            else {
                                placement_indent = value.trip_status.status_name;
                            }

                        }
                        catch (err) {
                            placement_indent = 'Unable to calculate'
                        }

                        try {
                            if (value.trip_reported_datetime != null && value.trip_start_datetime != null) {
                                var diff_intransit = (new Date(value.trip_reported_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                                console.log('value.trip_reported_datetime', value.trip_reported_datetime)
                                console.log('value.trip_start_datetime', value.trip_start_datetime)
                                diff_intransit /= 60;
                                var total_min_intransit = Math.round(diff_intransit);
                                var minutes_intransit = total_min_intransit % 60;
                                var hours_intransit = (total_min_intransit - minutes_intransit) / 60;
                                var intransit_time = hours_intransit + ":" + Math.abs(minutes_intransit);
                            }
                            else {
                                intransit_time = value.trip_status.status_name;
                            }

                        }
                        catch (err) {
                            intransit_time = 'Unable to calculate'
                        }

                        try {
                            if (value.trip_start_datetime != null && value.vehicle_placement_datetime != null) {
                                var diff_loading = (new Date(value.trip_start_datetime).getTime() - new Date(value.vehicle_placement_datetime).getTime()) / 1000;
                                diff_loading /= 60;
                                var total_min_loading = Math.round(diff_loading);
                                var minutes_loading = total_min_loading % 60;
                                var hours_loading = (total_min_loading - minutes_loading) / 60;
                                var loading_time = hours_loading + ":" + Math.abs(minutes_loading);
                            }
                            else {
                                loading_time = value.trip_status.status_name;
                            }

                        }
                        catch (err) {
                            loading_time = 'Unable to calculate'
                        }

                        try {
                            if (value.trip_reported_datetime != null && value.trip_unload_datetime != null) {
                                var diff_unloading = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_reported_datetime).getTime()) / 1000;
                                console.log('diff_unloading', diff_unloading)
                                diff_unloading /= 60;
                                var total_min_unloading = Math.round(diff_unloading);
                                var minutes_unloading = total_min_unloading % 60;
                                var hours_unloading = (total_min_unloading - minutes_unloading) / 60;
                                var unloading_time = hours_unloading + ":" + Math.abs(minutes_unloading);
                            }
                            else {
                                unloading_time = value.trip_status.status_name;
                            }

                        }
                        catch (err) {
                            loading_time = 'Unable to calculate'
                        }


                        try {
                            if (value.trip_unload_datetime!= null && value.trip_start_datetime != null) {
                                var time_taken = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                                time_taken /= 60;
                                var total_min_taken = Math.round(time_taken);
                                var minutes_taken = total_min_taken % 60;
                                var hours_taken = (total_min_taken - minutes_taken) / 60;
                                var total_time_taken = hours_taken + ":" + Math.abs(minutes_taken);
                            }
                            else {
                                total_time_taken = value.trip_status.status_name;
                            }

                        }
                        catch (err) {
                            total_time_taken = 'Unable to calculate'
                        }

                        if (value.trip_unload_datetime != null) {
                            var vehicle_unload_date = value.trip_unload_datetime.split('T')[0];
                            var vehicle_unload_time = value.trip_unload_datetime.split('T')[1];
                        }
                        else {
                            vehicle_unload_date = '';
                            vehicle_unload_time = '';
                        }

                        if (value.is_market == true && value.trip_status.status_name != "Completed") {
                            var gross_cost_updated = gross_cost
                        }
                        else {
                            gross_cost_updated = parseFloat(gross_cost + advance_deductions).toFixed(2)
                        }

                        $scope.completedExportValue = {
                            "Trip Code": value.trip_code,
                            "Order Code": value.order_code,
                            "Placement Type": value.placement_type,
                            "Market": $scope.market,
                            "Lr No": $scope.lr_no,
                            "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                            "Origin": origin_c,
                            "Waypoints": waypoints,
                            "Destination": des_c,
                            "Driver Name": value.order_data.driver_name + ' / ' + value.order_data.driver_no,
                            "Second Driver": value.order_data.co_driver_name + ' / ' + value.order_data.co_driver_no,
                            "Vehicle No": value.order_data.vehicle_no,
                            "Route": $scope.route,
                            "Trip Status": value.trip_status.status_name,
                            "Indent Date": value.indent_datetime.split('T')[0],
                            "Indent Time": (value.indent_datetime.split('T')[1]).split('.')[0],
                            "Placement Date": value.vehicle_placement_date,
                            "Placement Time": value.vehicle_placement_time,
                            "Start_Date": s_date,
                            "Start Time": s_time,
                            "Reporting Date": $scope.trip_end_date,
                            "Reporting Time": $scope.trip_end_date,
                            "Vehicle Unloading Date": vehicle_unload_date,
                            "Vehicle Unload Time": vehicle_unload_time,
                            "TAT": value.tat,
                            "Total time consumed": total_time_taken,
                            "Placement Lead Time": placement_indent,
                            "Intransit Time": intransit_time,
                            "Loading Time": loading_time,
                            "Unloading Time": unloading_time,
                            "Buyer/hm": value.buyer_data.name + ' / ' + value.buyer_data.code,
                            "Vehicle Type": value.vehicle_type,
                            "Broker Name": value.broker_data.company,
                            "Broker Phone": value.broker_data.mobile_no,
                            "Broker Pan": value.broker_data.broker_bank.pan_no,
                            "Broker Rate": value.broker_data.broker_rate * 1,
                            "Total Advance": $scope.total_adv,
                            "Broker Advance Date": value.broker_adv_timestamp.split(' ')[0],
                            "Cash Advance Date": value.driver_adv_timestamp.split(' ')[0],
                            "Broker Balance": value.broker_data.balance * 1,
                            "Broker Balance Date": broker_bal_paid_date,
                            "Contract Rate": value.contract * 1,
                            "Contract ID": value.contract_id,
                            "Payment Status": value.payment_status.payment_status_name,
                            "Advance Pay RefNo": value.broker_adv_ref,
                            "Balance Pay RefNo": value.broker_bal_ref,
                            "Broker Taxes": value.pod_data.pod_broker_data.taxes * 1,
                            "Broker TDS": value.pod_data.pod_broker_data.tds * 1,
                            "Broker Loading Charge": value.pod_data.pod_broker_data.loading_charge * 1,
                            "Broker Unloading Charge": value.pod_data.pod_broker_data.unloading_charge * 1,
                            "Broker Detention": value.pod_data.pod_broker_data.detention * 1,
                            "Broker Deduction_value": value.pod_data.pod_broker_data.deduction_value * 1,
                            "Broker Deduction Comment": value.pod_data.pod_broker_data.deduction_comment,
                            "Broker Surcharge": b_surcharge,
                            "Customer Taxes": value.pod_data.pod_customer_data.taxes * 1,
                            "Customer TDS": value.pod_data.pod_customer_data.tds * 1,
                            "Customer Loading Charge": value.pod_data.pod_customer_data.loading_charges * 1,
                            "Customer Unloading Charge": value.pod_data.pod_customer_data.unloading_charges * 1,
                            "Customer Detention": value.pod_data.pod_customer_data.detentions * 1,
                            "Customer Deduction Value": value.pod_data.pod_customer_data.deduction_value,
                            "Customer Deduction Comment": value.pod_data.pod_customer_data.deduction_comment,
                            "Customer Surcharge": value.pod_data.pod_customer_data.surcharge,
                            'Gross Cost': gross_cost_updated * 1,
                            "TDS": tds * 1,
                            'Net Cost': total_cost * 1,
                            'Total Revenue': total_rev * 1,
                            'Profit/Loss': p * 1,
                            "POD": value.pod_status,
                            "POD Uploaded On": value.pod_uploaed_on,
                            "POD Uploaded BY": value.pod_uploaded_by,
                            "POD Adjustments": value.pod_data.pod_broker_data.detention * 1 +
                            value.pod_data.pod_broker_data.unloading_charge * 1 +
                            b_surcharge * 1 +
                            value.pod_data.pod_broker_data.taxes * 1 +
                            value.pod_data.pod_broker_data.loading_charge * 1 -
                            value.pod_data.pod_broker_data.deduction_value * 1,
                            "Deductions": advance_deductions,
                            "Trip Type": trip_t
                        };
                        $scope.downloadCompletedCsv.push($scope.completedExportValue)
                    });
                    var mystyle = {
                        headers: true,
                        column: {style: {Font: {Bold: "1"}}}
                    };
                    alasql('SELECT * INTO XLS("TripReconciliations.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'Start_Date', reverse = true)]);

                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            });
        };



        $scope.completedExportDataUpdated = function (filterData) {
            $scope.progress_bar_per = 0;
            $scope.vehicle_type = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.placement_date = '';
            $scope.to_placement_date = '';
            $scope.broker_id = '';
            $scope.customer_id = '';
            $scope.is_market = '';
            $scope.s_from_date = '';
            $scope.s_to_date = '';
            $scope.vr_from_date = '';
            $scope.vr_to_date = '';
            $scope.vehicle_no = '';
            $scope.trip_type = '';
            $scope.order_trip_code = '';
            $scope.source_name = '';
            $scope.placement_type = '';
            $scope.unpaid_advance = '0';
            $scope.trip_recon_primary_driver = '';
            $scope.trip_recon_secondary_driver = '';
            $scope.payment_status_id= '';



            if (filterData.vehicle_type)
                $scope.vehicle_type = filterData.vehicle_type.name;

            if (filterData.source_name)
                $scope.source_name = filterData.source_name.name;
            if (filterData.origin)
                $scope.origin_name = filterData.origin;
            if (filterData.destination)
                $scope.destination_name = filterData.destination;
            if (filterData.placement_date)
                $scope.placement_date = filterData.placement_date;
            if (filterData.to_placement_date)
                $scope.to_placement_date = filterData.to_placement_date;
            if (filterData.s_from_date)
                $scope.s_from_date = filterData.s_from_date;
            if (filterData.s_to_date)
                $scope.s_to_date = filterData.s_to_date;
            if (filterData?.broker && filterData.broker?._source?.broker_data?.id)
                $scope.broker_id = filterData.broker._source.broker_data.id;
            if (filterData.trip_type)
                $scope.trip_type = filterData.trip_type;
            if (filterData?.customer && filterData.customer?.id)
                $scope.customer_id = filterData.customer.id;
            if (filterData.is_market) {
                if (filterData.is_market == 'Own Trip') {
                    $scope.is_market = false;
                }
                if (filterData.is_market == 'Market Trip') {
                    $scope.is_market = true;
                }
            }
            if (filterData.order_trip_code){
                $scope.order_trip_code = filterData.order_trip_code;
            }

            if (filterData.vr_from_date)
                $scope.vr_from_date = filterData.vr_from_date;
            if (filterData.vr_to_date)
                $scope.vr_to_date = filterData.vr_to_date;
            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;

            if (filterData.placement_type)
                $scope.placement_type = filterData.placement_type;

            if (filterData.primary_driver){
                $scope.trip_recon_primary_driver = filterData.primary_driver.driver_code;
            }

            if (filterData.secondary_driver){
                $scope.trip_recon_secondary_driver = filterData.secondary_driver.driver_code;
            }

            if (filterData.payment_status)
                $scope.payment_status_id = filterData.payment_status.id;

            var brokerIdList = [],  customerIdList = [];
            if (filterData?.broker)
                brokerIdList = filterData.broker.map(el=>el._source.broker_data.id);
            if (filterData?.customer)
                customerIdList = filterData.customer.map(el=>el.id);

            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_type: $scope.vehicle_type,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.placement_date,
                to_date: $scope.to_placement_date,
                s_from_date: $scope.s_from_date,
                s_to_date: $scope.s_to_date,
                broker: $scope.broker_id,
                is_market: $scope.is_market,
                vr_from_date: $scope.vr_from_date,
                vr_to_date: $scope.vr_to_date,
                trip_type: $scope.trip_type,
                vehicle_no: $scope.vehicle_no,
                order_trip_code: $scope.order_trip_code,
                source_name:$scope.source_name,
                placement_type: $scope.placement_type,
                pd_code: $scope.trip_recon_primary_driver,
                sd_code: $scope.trip_recon_secondary_driver,
                payment_status_id: $scope.payment_status_id,
                broker_ids: brokerIdList,
                customer_ids: customerIdList
            };
            $scope.csvloading = true;

            var num_of_pages = 1;
            var completedExport = [];

            var generate_trip_csv = function(completedExport){
                $scope.csvloading = false;
                $scope.completedExport = completedExport;
                $scope.downloadCompletedCsv = [];
                var total_cost = '';
                var total_rev = '';
                var p = '';
                var trip_t = 'Valid';
                angular.forEach($scope.completedExport, function (value, key) {

                    var waypoints = '';
                    if (value.order_data.multiple_stopage) {
                        angular.forEach(value.order_data.multiple_stopage, function (value, key) {
                            console.log('s_location', value.destination_point.location_name);
                            waypoints = waypoints + value.destination_point.location_name + '-->';
                        })
                    }
                    $scope.lr_no = "";
                    if (value.lr && value.lr.lr_data) {
                        angular.forEach(value.lr.lr_data, function (value, key) {
                            $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                        });
                    }
                    $scope.lr_no = $scope.lr_no.slice(0, -1);
                    // Gross Cost calculation
                    var gross_cost = '';
                    if (value.pod_data.pod_broker_data.b_surcharge) {
                        b_surcharge = value.pod_data.pod_broker_data.b_surcharge;
                    }
                    else {
                        b_surcharge = 0.0
                    }
                    if (value.pod_data.pod_broker_data.taxes === undefined) {
                        value.pod_data.pod_broker_data.taxes = 0.0;
                    }

                    try{
                        if(value.pod_data.pod_customer_data === undefined){
                            value.pod_data.pod_customer_data =  {
                                "taxes": 0.0,
                                "tds": 0.0,
                                "loading_charges": 0.0,
                                "toll": 0.0,
                                "unloading_charges": 0.0,
                                "detentions": 0.0,
                                "deduction_value": 0.0,
                                "deduction_comment": 0.0,
                                "surcharge": 0.0,
                            }

                        }
                    }
                    catch{
                        value.pod_data = {"pod_customer_data": {
                            "taxes": 0.0,
                            "tds": 0.0,
                            "loading_charges": 0.0,
                            "toll": 0.0,
                            "unloading_charges": 0.0,
                            "detentions": 0.0,
                            "deduction_value": 0.0,
                            "deduction_comment": 0.0,
                            "surcharge": 0.0,
                        }}

                    }
                    try{
                        if (value.pod_data.pod_customer_data.taxes === undefined){
                            value.pod_data.pod_customer_data.taxes = 0.0
                        }

                    }
                    catch{
                        value.pod_data.pod_customer_data.taxes = 0.0

                    }

                    trip_t = value.trip_validity;

                    if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                        gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1) *
                                value.pod_data.pod_broker_data.tds) / 100)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {


                        gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                value.pod_data.pod_broker_data.unloading_charge * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1 +
                                value.pod_data.pod_broker_data.loading_charge * 1) *
                                value.pod_data.pod_broker_data.tds) / 100)

                                console.log("broker gross cost", gross_cost)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                        gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.unloading_charge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1) *
                                value.pod_data.pod_broker_data.tds) / 100)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                        gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1)) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.loading_charge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1) *
                                value.pod_data.pod_broker_data.tds) / 100)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {

                        //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod

                        //gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) +
                        //    ((value.broker_data.broker_rate * 1 * value.broker_data.broker_tds) / 100)
                        gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)

                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {

                        gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                            (((value.broker_data.broker_rate * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                value.pod_data.pod_broker_data.taxes * 1 ) *
                                value.pod_data.pod_broker_data.tds) / 100 )
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                        gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.unloading_charge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1) *
                                value.pod_data.pod_broker_data.tds) / 100 )
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                        gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.loading_charge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1) *
                                value.pod_data.pod_broker_data.tds) / 100)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {

                        gross_cost = (value.broker_data.balance * 1) +
                            (((value.broker_data.broker_rate * 1 +
                                value.pod_data.pod_broker_data.unloading_charge * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.loading_charge * 1 +
                                value.pod_data.pod_broker_data.detention * 1 +
                                value.pod_data.pod_broker_data.taxes * 1 ) *
                                value.pod_data.pod_broker_data.tds) / 100)
                    }

                    if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {
                        //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod
                        //gross_cost = (value.broker_data.balance * 1) + ((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1) / 100)

                        gross_cost = (value.broker_data.balance * 1)
                    }


                    if (value.is_market == true && value.trip_status.status_name != "Completed") {

                        gross_cost = (value.broker_data.broker_rate * 1)


                        //gross_cost = (value.broker_data.broker_rate * 1) +
                        //    (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100)
                    }

                    if (value.is_market == false) {
                        gross_cost = (value.actual_data.total_all * 1)
                    }


                    //calculation of tds
                    var tds = 'N.A.';
                    if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == true) {
                        tds = ((((value.broker_data.broker_rate * 1) +
                            (value.pod_data.pod_broker_data.detention * 1) +
                            (b_surcharge * 1) +
                            (value.pod_data.pod_broker_data.taxes * 1 )) *
                            (value.pod_data.pod_broker_data.tds)) / 100 + ((
                            (value.pod_data.pod_broker_data.unloading_charge * 1) +
                            (value.pod_data.pod_broker_data.loading_charge * 1)) / 100))
                    }

                    if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == false) {

                        tds = ((((value.broker_data.broker_rate * 1) +
                            (value.pod_data.pod_broker_data.detention * 1) +
                            (b_surcharge * 1) +
                            (value.pod_data.pod_broker_data.loading_charge * 1) +
                            (value.pod_data.pod_broker_data.taxes * 1 )) *
                            (value.pod_data.pod_broker_data.tds)) / 100 + (
                            (value.pod_data.pod_broker_data.unloading_charge * 1) / 100))
                    }

                    if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == true) {

                        tds = ((((value.broker_data.broker_rate * 1) +
                            (value.pod_data.pod_broker_data.detention * 1) +
                            (b_surcharge * 1) +
                            (value.pod_data.pod_broker_data.unloading_charge * 1) +
                            (value.pod_data.pod_broker_data.taxes * 1 )) *
                            (value.pod_data.pod_broker_data.tds)) / 100 + (
                            (value.pod_data.pod_broker_data.loading_charge * 1) / 100))
                    }

                    if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == false) {

                        tds = ((((value.broker_data.broker_rate * 1) +
                            (value.pod_data.pod_broker_data.detention * 1) +
                            (b_surcharge * 1) +
                            (value.pod_data.pod_broker_data.loading_charge * 1) +
                            (value.pod_data.pod_broker_data.unloading_charge * 1) +
                            (value.pod_data.pod_broker_data.taxes * 1 )) *
                            (value.pod_data.pod_broker_data.tds)) / 100)
                    }

                    if (value.pod_status == 'Pending' && value.is_market != false) {
                        tds = (( value.broker_data.broker_rate * 1) * value.broker_data.broker_tds) / 100
                    }

                    if (value.trip_status.status_name == "Completed") {

                        console.log("Total rev ==> ", value.customer_pay);
                        total_rev = value.customer_pay;

                    }
                    if (value.trip_status.status_name != "Completed") {
                        console.log("Total rev ==> ", value.contract);
                        total_rev = value.contract;
                    }

                    var advance_deductions = 0;
                    if (value.advance_deductions == null) {
                        advance_deductions = 0
                    }
                    else {
                        advance_deductions = value.advance_deductions
                    }

                    if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                        p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1)
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                        p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                        p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                        p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                    }


                    if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                        p = (value.customer_pay * 1) - (value.broker_data.balance * 1 )
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                        p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                        p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                    }
                    if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                        p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                    }


                    if (value.is_market == true && value.trip_status.status_name != "Completed") {
                        p = (value.contract * 1) - ((value.broker_data.broker_rate * 1) - (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100) - advance_deductions)
                    }
                    if (value.is_market == false && value.trip_status.status_name == "Completed") {
                        p = (value.customer_pay * 1) - (value.actual_data.total_all * 1)
                    }
                    if (value.is_market == false && value.trip_status.status_name != "Completed") {
                        p = (value.contract * 1) - (value.actual_data.total_all * 1)
                    }
                    if (value.trip_end_date === null) {
                        $scope.trip_end_date = ''
                    } else {
                        $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                    }

                    if (value.is_market === true) {
                        $scope.market = 'yes';
                        console.log("==>", parseFloat(gross_cost * 1) - parseFloat(tds * 1) - advance_deductions)
                        total_cost = parseFloat(gross_cost * 1) - parseFloat(tds * 1);
                        $scope.total_adv = value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1;
                        console.log("total cost", total_cost)
                    } else {
                        $scope.market = 'no';
                        total_cost = gross_cost;
                        try {
                            $scope.total_adv = value.adv_data.total_adv;
                        }
                        catch (err) {
                            $scope.total_adv = 'N.A.'
                        }
                        if (value.own_unpaid_adv_cost != null) {
                            $scope.unpaid_advance = value.own_unpaid_adv_cost
                        }
                        else{
                            $scope.unpaid_advance = '0'
                        }
                    }
                    if (value.lane.lane_code === undefined) {
                        $scope.route = ''
                    } else {
                        $scope.route = value.lane.lane_code
                    }
                    var s_date;
                    var s_time;
                    var t_code;
                    try {
                        s_date = value.trip_start_date.split('T')[0]
                    }
                    catch (err) {
                        s_date = ''
                    }
                    try {
                        s_time = value.trip_start_time.split('T')[1].match(/.{5}/g)[0]
                    }
                    catch (err) {
                        s_time = ''
                    }
                    if (value.trip_code == null) {
                        t_code = 'N/A'
                    }
                    else {
                        t_code = value.trip_code
                    }


                    if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                        origin_c = 'Flipkart Luhari'
                    }
                    else {
                        origin_c = (value.order_data.origin.location_name).replace('<', ' ').replace('>', '')
                    }
                    if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                        des_c = 'Flipkart Ludhiana'
                    }
                    else {
                        des_c = value.order_data.destination.location_name.replace('<', ' ').replace('>', '')
                    }

                    if (value.broker_bal_paid_date != null) {
                        var broker_bal_paid_date = value.broker_bal_paid_date.split('T')[0];
                    }
                    else {
                        broker_bal_paid_date = '';
                    }

                    // Calculate time taken in between operational events

                    try {
                        if (value.indent_datetime != null && value.vehicle_placement_datetime != null) {
                            var diff = (new Date(value.vehicle_placement_datetime).getTime() - new Date(value.indent_datetime).getTime()) / 1000;
                            console.log('value.vehicle_req_datetime', value.vehicle_req_datetime);
                            console.log('value.vehicle_placement_datetime', value.vehicle_placement_datetime);
                            diff /= 60;
                            var total_min = Math.round(diff);
                            var minutes = total_min % 60;
                            var hours = (total_min - minutes) / 60;
                            var placement_indent = hours + ":" + Math.abs(minutes)
                        }
                        else {
                            placement_indent = value.trip_status.status_name;
                        }

                    }
                    catch (err) {
                        placement_indent = 'Unable to calculate'
                    }

                    try {
                        if (value.trip_reported_datetime != null && value.trip_start_datetime != null) {
                            var diff_intransit = (new Date(value.trip_reported_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                            console.log('value.trip_reported_datetime', value.trip_reported_datetime)
                            console.log('value.trip_start_datetime', value.trip_start_datetime)
                            diff_intransit /= 60;
                            var total_min_intransit = Math.round(diff_intransit);
                            var minutes_intransit = total_min_intransit % 60;
                            var hours_intransit = (total_min_intransit - minutes_intransit) / 60;
                            var intransit_time = hours_intransit + ":" + Math.abs(minutes_intransit);
                        }
                        else {
                            intransit_time = value.trip_status.status_name;
                        }

                    }
                    catch (err) {
                        intransit_time = 'Unable to calculate'
                    }

                    try {
                        if (value.trip_start_datetime != null && value.vehicle_placement_datetime != null) {
                            var diff_loading = (new Date(value.trip_start_datetime).getTime() - new Date(value.vehicle_placement_datetime).getTime()) / 1000;
                            diff_loading /= 60;
                            var total_min_loading = Math.round(diff_loading);
                            var minutes_loading = total_min_loading % 60;
                            var hours_loading = (total_min_loading - minutes_loading) / 60;
                            var loading_time = hours_loading + ":" + Math.abs(minutes_loading);
                        }
                        else {
                            loading_time = value.trip_status.status_name;
                        }

                    }
                    catch (err) {
                        loading_time = 'Unable to calculate'
                    }

                    try {
                        if (value.trip_reported_datetime != null && value.trip_unload_datetime != null) {
                            var diff_unloading = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_reported_datetime).getTime()) / 1000;
                            console.log('diff_unloading', diff_unloading)
                            diff_unloading /= 60;
                            var total_min_unloading = Math.round(diff_unloading);
                            var minutes_unloading = total_min_unloading % 60;
                            var hours_unloading = (total_min_unloading - minutes_unloading) / 60;
                            var unloading_time = hours_unloading + ":" + Math.abs(minutes_unloading);
                        }
                        else {
                            unloading_time = value.trip_status.status_name;
                        }

                    }
                    catch (err) {
                        loading_time = 'Unable to calculate'
                    }


                    try {
                        if (value.trip_unload_datetime!= null && value.trip_start_datetime != null) {
                            var time_taken = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                            time_taken /= 60;
                            var total_min_taken = Math.round(time_taken);
                            var minutes_taken = total_min_taken % 60;
                            var hours_taken = (total_min_taken - minutes_taken) / 60;
                            var total_time_taken = hours_taken + ":" + Math.abs(minutes_taken);
                        }
                        else {
                            total_time_taken = value.trip_status.status_name;
                        }

                    }
                    catch (err) {
                        total_time_taken = 'Unable to calculate'
                    }

                    if (value.trip_unload_datetime != null) {
                        var vehicle_unload_date = value.trip_unload_datetime.split('T')[0];
                        var vehicle_unload_time = value.trip_unload_datetime.split('T')[1];
                    }
                    else {
                        vehicle_unload_date = '';
                        vehicle_unload_time = '';
                    }

                    if (value.is_market == true && value.trip_status.status_name != "Completed") {
                        var gross_cost_updated = gross_cost
                    }
                    else {
                        gross_cost_updated = parseFloat(gross_cost + advance_deductions).toFixed(2)
                    }
                    var demand_reference_id = '';
                    if (value.order_data.demand_reference_id){
                        demand_reference_id = value.order_data.demand_reference_id;
                    }
                    $scope.completedExportValue = {
                        "Trip Source": value.source_name,
                        "Trip Recovery ID": value.trip_recovery_data.recovery_ids,
                        "Recovery Amount": value.trip_recovery_data.amount,
                        "Trip Code": value.trip_code,
                        "Vehicle Ref ID": demand_reference_id,
                        "Order Code": value.order_code,
                        "Placement Type": value.placement_type,
                        "Market": $scope.market,
                        "Lr No": $scope.lr_no,
                        "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                        "Origin": origin_c,
                        "Waypoints": waypoints,
                        "Destination": des_c,
                        "Driver Name": value.order_data.driver_name + ' / ' + value.order_data.driver_no,
                        "Second Driver": value.order_data.co_driver_name + ' / ' + value.order_data.co_driver_no,
                        "Vehicle No": value.order_data.vehicle_no,
                        "Route": $scope.route,
                        "Trip Status": value.trip_status.status_name,
                        "Indent Date": value.indent_datetime.split('T')[0],
                        "Indent Time": (value.indent_datetime.split('T')[1]).split('.')[0],
                        "Placement Date": value.vehicle_placement_date,
                        "Placement Time": value.vehicle_placement_time,
                        "Start_Date": s_date,
                        "Start Time": s_time,
                        "Reporting Date": $scope.trip_end_date,
                        "Broker_Code" : value.broker_data.broker_code,
                        "Reporting Time": $scope.trip_end_date,
                        "Vehicle Unloading Date": vehicle_unload_date,
                        "Vehicle Unload Time": vehicle_unload_time,
                        "TAT": value.tat,
                        "Total time consumed": total_time_taken,
                        "Placement Lead Time": placement_indent,
                        "Intransit Time": intransit_time,
                        "Loading Time": loading_time,
                        "Unloading Time": unloading_time,
                        "Buyer/hm": value.buyer_data.name + ' / ' + value.buyer_data.code,
                        "Vehicle Type": value.vehicle_type,
                        "Broker Name": value.broker_data.company,
                        "Broker Phone": value.broker_data.mobile_no,
                        "Broker Pan": value.broker_data.broker_bank.pan_no,
                        "Broker Rate": value.broker_data.broker_rate * 1,
                        "Other Advance": value.broker_data.fuel_advance,
                        "Total Paid Advance": $scope.total_adv,
                        "Total Unpaid Advance": $scope.unpaid_advance,
                        "Broker Advance Date": value.broker_adv_timestamp.split(' ')[0],
                        "Cash Advance Date": value.driver_adv_timestamp.split(' ')[0],
                        "Broker Balance": $scope.getBrokerBalance(value),
                        "Broker Balance Date": broker_bal_paid_date,
                        "Contract Rate": value.contract * 1,
                        "Contract ID": value.contract_id,
                        "Payment Status": value.payment_status.payment_status_name,
                        "Advance Pay RefNo": value.broker_adv_ref,
                        "Balance Pay RefNo": value.broker_bal_ref,
                        "Broker Taxes": value.pod_data.pod_broker_data.taxes * 1,
                        "Broker TDS": value.pod_data.pod_broker_data.tds * 1,
                        "Broker Loading Charge": value.pod_data.pod_broker_data.loading_charge * 1,
                        "Broker Unloading Charge": value.pod_data.pod_broker_data.unloading_charge * 1,
                        "Broker Detention": value.pod_data.pod_broker_data.detention * 1,
                        "Broker Deduction_value": value.pod_data.pod_broker_data.deduction_value * 1,
                        "Broker Deduction Comment": value.pod_data.pod_broker_data.deduction_comment,
                        "Broker Surcharge": b_surcharge,
                        "Customer Taxes": value.pod_data.pod_customer_data.taxes * 1,
                        "Customer TDS": value.pod_data.pod_customer_data.tds * 1,
                        "Customer Loading Charge": value.pod_data.pod_customer_data.loading_charges * 1,
                        "Customer Unloading Charge": value.pod_data.pod_customer_data.unloading_charges * 1,
                        "Customer Detention": value.pod_data.pod_customer_data.detentions * 1,
                        "Customer Deduction Value": value.pod_data.pod_customer_data.deduction_value,
                        "Customer Deduction Comment": value.pod_data.pod_customer_data.deduction_comment,
                        "Customer Surcharge": value.pod_data.pod_customer_data.surcharge,
                        'Gross Cost': gross_cost_updated * 1,
                        "TDS": tds * 1,
                        'Net Cost': total_cost * 1,
                        'Total Revenue': total_rev * 1,
                        'Profit/Loss': p * 1,
                        "POD": value.pod_status,
                        "POD Uploaded On": value.pod_uploaed_on,
                        "POD Uploaded BY": value.pod_uploaded_by,
                        "POD Adjustments": value.pod_data.pod_broker_data.detention * 1 +
                        value.pod_data.pod_broker_data.unloading_charge * 1 +
                        b_surcharge * 1 +
                        value.pod_data.pod_broker_data.taxes * 1 +
                        value.pod_data.pod_broker_data.loading_charge * 1 -
                        value.pod_data.pod_broker_data.deduction_value * 1,
                        "Deductions": advance_deductions,
                        "Trip Type": trip_t
                    };
                    $scope.downloadCompletedCsv.push($scope.completedExportValue)
                });
                var mystyle = {
                    headers: true,
                    column: {style: {Font: {Bold: "1"}}}
                };
                alasql('SELECT * INTO XLSX ("TripReconciliations.xlsx",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'Start_Date', reverse = true)]);

            };

            var recur_trip_menu = function(){
                AdminServices.TripFilterService(num_of_pages, $scope.filterDataObject).then(function (response) {
                    if (response.data.results.code.status == 200){
                        var count = response.data.count;
                        var total_pages = Math.ceil(count/response.data.pagesize);
                        num_of_pages = num_of_pages+1;
                        if(num_of_pages <= total_pages){
                            completedExport = completedExport.concat(response.data.results.trip_data);
                            recur_trip_menu()
                        }
                        else{
                            completedExport = completedExport.concat(response.data.results.trip_data);
                            generate_trip_csv(completedExport)
                        }
                        console.log('count', count);

                        // if (next != null){
                        //     completedExport = completedExport.concat(response.data.results.trip_data);
                        //     recur_trip_menu()
                        // }
                        // else{
                        //     completedExport = completedExport.concat(response.data.results.trip_data);
                        //     generate_trip_csv(completedExport)
                        // }
                        $scope.progress_bar_per = ((completedExport.length/count)*100).toFixed(2);
                    }
                    else{
                        swal("Cancelled", response.data.results.code.message, "error")
                        //alert(response.data.results.code.message)
                    }

                });

            };
            recur_trip_menu();
        };



        $scope.podData = function (trip, tr) {
            $scope.totalCostRevenue = tr;
            console.log('$scope.totalCostRevenue', $scope.totalCostRevenue);
            $scope.pod_data = trip;
            console.log('$scope.pod_data', $scope.pod_data)
        };

        $scope.actualPodData = function (trip) {
            $scope.actual_pod_data = trip;
            console.log('$scope.actual_pod_data', $scope.actual_pod_data)
        };

        /**
         * init call funcation
         */
        $scope.asyncRequestForFilter();

        $scope.months = ['3', '6', '12', 'current_month'];
        $scope.month = {};
        $scope.trip_t = ['Valid', 'Invalid'];
        $scope.downloadTripMonthlyCSV = function (month) {
            $scope.csvDwnLoading = true;
            console.log('month', month);
            if (month.item == undefined) {
                swal("Oops!", "Select month", "error")
            }
            else {
                AdminServices.downloadTripCSVmonthly(month).then(function (response) {
                    console.log('RESPONSE', response)
                    if (response.data.code.status === 200) {
                        $scope.completedExport = response.data.trip_data;
                        $scope.downloadCompletedCsv = [];
                        var total_cost = '';
                        var total_rev = '';
                        var p = '';

                        angular.forEach($scope.completedExport, function (value, key) {
                            $scope.lr_no = "";
                            try {
                                angular.forEach(value.lr.lr_data, function (value, key) {
                                    $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                                });
                                $scope.lr_no = $scope.lr_no.slice(0, -1);
                            }
                            catch (err) {
                                $scope.lr_no = "";
                            }

                            console.log('VALUE', value)
                            if (value.pod_data.pod_broker_data.b_surcharge) {
                                b_surcharge = value.pod_data.pod_broker_data.b_surcharge;
                            }
                            else {
                                b_surcharge = 0.0
                            }
                            // Gross Cost calculation
                            var gross_cost = '';
                            if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                                gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                                gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        value.pod_data.pod_broker_data.unloading_charge * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1 +
                                        value.pod_data.pod_broker_data.loading_charge * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                                gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.loading_charge * 1)) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.unloading_charge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                                gross_cost = ((value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1)) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.loading_charge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {


                                //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod

                                //gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1) +
                                //    ((value.broker_data.broker_rate * 1 * value.broker_data.broker_tds) / 100)

                                gross_cost = (value.broker_data.broker_advance * 1) + (value.broker_data.balance * 1) + (value.broker_data.broker_advance_cash * 1)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == true) {

                                gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                                    (((value.broker_data.broker_rate * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1 ) *
                                        value.pod_data.pod_broker_data.tds) / 100 )
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                                gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.loading_charge * 1) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.unloading_charge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100 )
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == true && value.loading_ent == false) {

                                gross_cost = (value.broker_data.balance * 1) + (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.loading_charge * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Completed" && value.unloading_ent == false && value.loading_ent == false) {

                                gross_cost = (value.broker_data.balance * 1) +
                                    (((value.broker_data.broker_rate * 1 +
                                        value.pod_data.pod_broker_data.unloading_charge * 1 +
                                        b_surcharge * 1 +
                                        value.pod_data.pod_broker_data.loading_charge * 1 +
                                        value.pod_data.pod_broker_data.detention * 1 +
                                        value.pod_data.pod_broker_data.taxes * 1 ) *
                                        value.pod_data.pod_broker_data.tds) / 100)
                            }

                            if ((value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed") && value.pod_status == "Pending") {

                                //As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod

                                //gross_cost = (value.broker_data.balance * 1) + ((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1) / 100)

                                gross_cost = (value.broker_data.balance * 1)
                            }


                            if (value.is_market == true && value.trip_status.status_name != "Completed") {

                                gross_cost = (value.broker_data.broker_rate * 1)

                                //gross_cost = (value.broker_data.broker_rate * 1) +
                                //    (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100)
                            }

                            if (value.is_market == false) {
                                gross_cost = (value.actual_data.total_all * 1)
                            }


                            //calculation of tds
                            var tds = 'N.A.';
                            if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == true) {
                                tds = ((((value.broker_data.broker_rate * 1) +
                                    (value.pod_data.pod_broker_data.detention * 1) +
                                    (b_surcharge * 1) +
                                    (value.pod_data.pod_broker_data.taxes * 1 )) *
                                    (value.pod_data.pod_broker_data.tds)) / 100 + ((
                                    (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                    (value.pod_data.pod_broker_data.loading_charge * 1)) / 100))
                            }

                            if (value.pod_status == 'Completed' && value.unloading_ent == true && value.loading_ent == false) {

                                tds = ((((value.broker_data.broker_rate * 1) +
                                    (value.pod_data.pod_broker_data.detention * 1) +
                                    (b_surcharge * 1) +
                                    (value.pod_data.pod_broker_data.loading_charge * 1) +
                                    (value.pod_data.pod_broker_data.taxes * 1 )) *
                                    (value.pod_data.pod_broker_data.tds)) / 100 + (
                                    (value.pod_data.pod_broker_data.unloading_charge * 1) / 100))
                            }

                            if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == true) {

                                tds = ((((value.broker_data.broker_rate * 1) +
                                    (value.pod_data.pod_broker_data.detention * 1) +
                                    (b_surcharge * 1) +
                                    (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                    (value.pod_data.pod_broker_data.taxes * 1 )) *
                                    (value.pod_data.pod_broker_data.tds)) / 100 + (
                                    (value.pod_data.pod_broker_data.loading_charge * 1) / 100))
                            }

                            if (value.pod_status == 'Completed' && value.unloading_ent == false && value.loading_ent == false) {

                                tds = ((((value.broker_data.broker_rate * 1) +
                                    (value.pod_data.pod_broker_data.detention * 1) +
                                    (b_surcharge * 1) +
                                    (value.pod_data.pod_broker_data.loading_charge * 1) +
                                    (value.pod_data.pod_broker_data.unloading_charge * 1) +
                                    (value.pod_data.pod_broker_data.taxes * 1 )) *
                                    (value.pod_data.pod_broker_data.tds)) / 100)
                            }

                            if (value.pod_status == 'Pending' && value.is_market != false) {
                                tds = (( value.broker_data.broker_rate * 1) * value.broker_data.broker_tds) / 100
                            }

                            if (value.trip_status.status_name == "Completed") {
                                total_rev = value.customer_pay

                            }
                            if (value.trip_status.status_name != "Completed") {
                                total_rev = value.contract
                            }

                            var advance_deductions = 0;
                            if (value.advance_deductions == null) {
                                advance_deductions = 0
                            }
                            else {
                                advance_deductions = value.advance_deductions
                            }


                            if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                                p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1)
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                                p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                                p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name == "Advance Paid" || value.payment_status.payment_status_name == "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                                p = (value.customer_pay * 1) - (value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1 ) - (value.broker_data.balance * 1) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                            }


                            if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == false) {
                                p = (value.customer_pay * 1) - (value.broker_data.balance * 1 )
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == true) {
                                p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == true && value.loading_ent == false) {
                                p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.unloading_charge * 1) - (value.pod_data.pod_broker_data.unloading_charge * 1) / 100)
                            }
                            if (value.is_market == true && (value.payment_status.payment_status_name != "Advance Paid" && value.payment_status.payment_status_name != "Balance Paid") && value.trip_status.status_name == "Completed" && value.unloading_ent == false && value.loading_ent == true) {
                                p = (value.customer_pay * 1) - (value.broker_data.balance * 1 ) - ((value.pod_data.pod_broker_data.loading_charge * 1) - (value.pod_data.pod_broker_data.loading_charge * 1) / 100)
                            }


                            if (value.is_market == true && value.trip_status.status_name != "Completed") {
                                p = (value.contract * 1) - ((value.broker_data.broker_rate * 1) - (((value.broker_data.broker_rate * 1) * (value.broker_data.broker_tds * 1)) / 100) - advance_deductions)
                            }
                            if (value.is_market == false && value.trip_status.status_name == "Completed") {
                                p = (value.customer_pay * 1) - (value.actual_data.total_all * 1)
                            }
                            if (value.is_market == false && value.trip_status.status_name != "Completed") {
                                p = (value.contract * 1) - (value.actual_data.total_all * 1)
                            }
                            if (value.trip_end_date === null) {
                                $scope.trip_end_date = ''
                            } else {
                                $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                            }

                            if (value.is_market === true) {
                                $scope.market = 'yes';
                                total_cost = parseFloat(gross_cost * 1) - parseFloat(tds * 1) - advance_deductions;
                                $scope.total_adv = value.broker_data.broker_advance * 1 + value.broker_data.broker_advance_cash * 1;
                            } else {
                                $scope.market = 'no';
                                total_cost = gross_cost;
                                try {
                                    $scope.total_adv = value.adv_data.total_adv;
                                }
                                catch (err) {
                                    $scope.total_adv = 'N.A.'
                                }
                            }
                            if (value.lane.lane_code === undefined) {
                                $scope.route = ''
                            } else {
                                $scope.route = value.lane.lane_code
                            }
                            var s_date;
                            var s_time;
                            var t_code;
                            try {
                                s_date = value.trip_start_date.split('T')[0]
                            }
                            catch (err) {
                                s_date = ''
                            }
                            try {
                                s_time = value.trip_start_time.split('T')[1].match(/.{5}/g)[0]
                            }
                            catch (err) {
                                s_time = ''
                            }
                            if (value.trip_code == null) {
                                t_code = 'N/A'
                            }
                            else {
                                t_code = value.trip_code
                            }

                            console.log('tds', tds);
                            console.log('total_cost', total_cost);
                            console.log('gross_cost', gross_cost);
                            if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                                origin_c = 'Flipkart Luhari'
                            }
                            else {
                                origin_c = value.order_data.origin.location_name
                            }
                            if (value.trip_code === '20180121|M|FKRT||007129' || value.trip_code === '20180123|M|FKRT||007130' || value.trip_code === '20180127|M|FKRT||007131' || value.trip_code === '20180129|M|FKRT||007132') {
                                des_c = 'Flipkart Ludhiana'
                            }
                            else {
                                des_c = value.order_data.destination.location_name
                            }

                            if (value.broker_bal_paid_date != null) {
                                var broker_bal_paid_date = value.broker_bal_paid_date.split('T')[0];
                            }
                            else {
                                broker_bal_paid_date = '';
                            }

                            // Calculate time taken in between operational events

                            try {
                                if (value.indent_datetime != null && value.vehicle_placement_datetime != null) {
                                    var diff = (new Date(value.vehicle_placement_datetime).getTime() - new Date(value.indent_datetime).getTime()) / 1000;
                                    console.log('value.vehicle_req_datetime', value.vehicle_req_datetime);
                                    console.log('value.vehicle_placement_datetime', value.vehicle_placement_datetime);
                                    diff /= 60;
                                    var total_min = Math.round(diff);
                                    var minutes = total_min % 60;
                                    var hours = (total_min - minutes) / 60;
                                    var placement_indent = hours + ":" + Math.abs(minutes)
                                }
                                else {
                                    placement_indent = value.trip_status.status_name;
                                }

                            }
                            catch (err) {
                                placement_indent = 'Unable to calculate'
                            }

                            try {
                                if (value.trip_reported_datetime != null && value.trip_start_datetime != null) {
                                    var diff_intransit = (new Date(value.trip_reported_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                                    console.log('value.trip_reported_datetime', value.trip_reported_datetime)
                                    console.log('value.trip_start_datetime', value.trip_start_datetime)
                                    diff_intransit /= 60;
                                    var total_min_intransit = Math.round(diff_intransit);
                                    var minutes_intransit = total_min_intransit % 60;
                                    var hours_intransit = (total_min_intransit - minutes_intransit) / 60;
                                    var intransit_time = hours_intransit + ":" + Math.abs(minutes_intransit);
                                }
                                else {
                                    intransit_time = value.trip_status.status_name;
                                }

                            }
                            catch (err) {
                                intransit_time = 'Unable to calculate'
                            }

                            try {
                                if (value.trip_start_datetime != null && value.vehicle_placement_datetime != null) {
                                    var diff_loading = (new Date(value.trip_start_datetime).getTime() - new Date(value.vehicle_placement_datetime).getTime()) / 1000;
                                    diff_loading /= 60;
                                    var total_min_loading = Math.round(diff_loading);
                                    var minutes_loading = total_min_loading % 60;
                                    var hours_loading = (total_min_loading - minutes_loading) / 60;
                                    var loading_time = hours_loading + ":" + Math.abs(minutes_loading);
                                }
                                else {
                                    loading_time = value.trip_status.status_name;
                                }

                            }
                            catch (err) {
                                loading_time = 'Unable to calculate'
                            }

                            try {
                                if (value.trip_reported_datetime != null && value.trip_unload_datetime != null) {
                                    var diff_unloading = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_reported_datetime).getTime()) / 1000;
                                    console.log('diff_unloading', diff_unloading)
                                    diff_unloading /= 60;
                                    var total_min_unloading = Math.round(diff_unloading);
                                    var minutes_unloading = total_min_unloading % 60;
                                    var hours_unloading = (total_min_unloading - minutes_unloading) / 60;
                                    var unloading_time = hours_unloading + ":" + Math.abs(minutes_unloading);
                                }
                                else {
                                    unloading_time = value.trip_status.status_name;
                                }

                            }
                            catch (err) {
                                loading_time = 'Unable to calculate'
                            }

                            if (value.trip_unload_datetime != null) {
                                var vehicle_unload_date = value.trip_unload_datetime.split('T')[0];
                                var vehicle_unload_time = value.trip_unload_datetime.split('T')[1];
                            }
                            else {
                                vehicle_unload_date = '';
                                vehicle_unload_time = '';
                            }

                            if (value.is_market == true && value.trip_status.status_name != "Completed") {
                                var gross_cost_updated = gross_cost
                            }
                            else {
                                gross_cost_updated = parseFloat(gross_cost + advance_deductions).toFixed(2)
                            }


                            try {
                                if (value.trip_unload_datetime != null && value.trip_start_datetime != null) {
                                    var time_taken = (new Date(value.trip_unload_datetime).getTime() - new Date(value.trip_start_datetime).getTime()) / 1000;
                                    time_taken /= 60;
                                    var total_min_taken = Math.round(time_taken);
                                    var minutes_taken = total_min_taken % 60;
                                    var hours_taken = (total_min_taken - minutes_taken) / 60;
                                    var total_time_taken = hours_taken + ":" + Math.abs(minutes_taken);
                                }
                                else {
                                    total_time_taken = value.trip_status.status_name;
                                }

                            }
                            catch (err) {
                                total_time_taken = 'Unable to calculate'
                            }
                            //to check trip is valid/invalid
                            var tripValidity = 'valid'
                            if (value.invalid){
                                tripValidity = "invalid"
                            }
                            $scope.completedExportValue = {
                                "Trip Code": value.trip_code,
                                "Order Code": value.order_code,
                                "Placement Type": value.placement_type,
                                "Market": $scope.market,
                                "Lr No": $scope.lr_no,
                                "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                                "Origin": origin_c,
                                "Destination": des_c,
                                "Driver Name": value.order_data.driver_name + ' / ' + value.order_data.driver_no,
                                "Second Driver": value.order_data.co_driver_name + ' / ' + value.order_data.co_driver_no,
                                "Vehicle No": value.order_data.vehicle_no,
                                "Route": $scope.route,
                                "Trip Status": value.trip_status.status_name,
                                "Indent Date": value.indent_datetime.split('T')[0],
                                "Indent Time": (value.indent_datetime.split('T')[1]).split('.')[0],
                                "Placement Date": value.vehicle_placement_date,
                                "Placement Time": value.vehicle_placement_time,
                                "Start_Date": s_date,
                                "Start Time": s_time,
                                "Reporting Date": $scope.trip_end_date,
                                "Reporting Time": $scope.trip_end_date,
                                "Vehicle Unloading Date": vehicle_unload_date,
                                "Vehicle Unload Time": vehicle_unload_time,
                                "TAT": value.tat,
                                "Total Time Consumed": total_time_taken,
                                "Placement Lead Time": placement_indent,
                                "Intransit Time": intransit_time,
                                "Loading Time": loading_time,
                                "Unloading Time": unloading_time,
                                "Buyer/hm": value.buyer_data.name + ' / ' + value.buyer_data.code,
                                "Vehicle Type": value.vehicle_type,
                                "Broker Name": value.broker_data.company,
                                "Broker Phone": value.broker_data.mobile_no,
                                "Broker Pan": value.broker_data.broker_bank.pan_no,
                                "Broker Rate": value.broker_data.broker_rate,
                                "Total Advance": $scope.total_adv,
                                "Broker Advance Date": value.broker_adv_timestamp.split(' ')[0],
                                "Cash Advance Date": value.driver_adv_timestamp.split(' ')[0],
                                "Broker Balance": value.broker_data.balance,
                                "Broker Balance Date": broker_bal_paid_date,
                                "Contract Rate": value.contract,
                                "Payment Status": value.payment_status.payment_status_name,
                                "Broker Taxes": value.pod_data.pod_broker_data.taxes,
                                "Broker TDS": value.pod_data.pod_broker_data.tds,
                                "Broker Loading Charge": value.pod_data.pod_broker_data.loading_charge,
                                "Broker Unloading Charge": value.pod_data.pod_broker_data.unloading_charge,
                                "Broker Detention": value.pod_data.pod_broker_data.detention,
                                "Broker Deduction_value": value.pod_data.pod_broker_data.deduction_value,
                                "Broker Deduction Comment": value.pod_data.pod_broker_data.deduction_comment,
                                "Broker Surcharge": b_surcharge,
                                "Customer Taxes": value.pod_data.pod_customer_data.taxes,
                                "Customer TDS": value.pod_data.pod_customer_data.tds,
                                "Customer Loading Charge": value.pod_data.pod_customer_data.loading_charges,
                                "Customer Unloading Charge": value.pod_data.pod_customer_data.unloading_charges,
                                "Customer Detention": value.pod_data.pod_customer_data.detentions,
                                "Customer Deduction Value": value.pod_data.pod_customer_data.deduction_value,
                                "Customer Deduction Comment": value.pod_data.pod_customer_data.deduction_comment,
                                "Customer Surcharge": value.pod_data.pod_customer_data.surcharge,
                                'Gross Cost': gross_cost_updated,
                                "TDS": tds,
                                'Net Cost': total_cost,
                                'Total Revenue': total_rev,
                                'Profit/Loss': p,
                                "POD": value.pod_status,
                                "POD Uploaded On": value.pod_uploaed_on,
                                "POD Uploaded BY": value.pod_uploaded_by,
                                "POD Adjustments": value.pod_data.pod_broker_data.detention * 1 +
                                value.pod_data.pod_broker_data.unloading_charge * 1 +
                                b_surcharge * 1 +
                                value.pod_data.pod_broker_data.taxes * 1 +
                                value.pod_data.pod_broker_data.loading_charge * 1 -
                                value.pod_data.pod_broker_data.deduction_value * 1,
                                "Deductions": advance_deductions,
                                "Trip Type": tripValidity

                            };
                            $scope.downloadCompletedCsv.push($scope.completedExportValue)
                        });
                        var mystyle = {
                            headers: true,
                            column: {style: {Font: {Bold: "1"}}}
                        };
                        alasql('SELECT * INTO XLS("TripReconciliations.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'Start_Date', reverse = true)]);
                        $scope.csvDwnLoading = false;
                    }
                    else {
                        $scope.csvDwnLoading = false;
                        swal("Oops!", response.data.code.message, "error")
                    }

                })
            }
        };


        $scope.finGet = function (data) {
          $scope.financeAttribute = {
              'trip_code': data.trip_code,
              'advance': data.finance_attr.advance,
              'advance_remark': data.finance_attr.advance_remark,
              'balance': data.finance_attr.balance,
              'balance_remark': data.finance_attr.balance_remark,
              'deduction': data.finance_attr.deduction,
              'deduction_remark': data.finance_attr.deduction_remark
          }
        };

        $scope.entityPaymentStatus = function(entity_data, own_adv_cost){
            $scope.own_entity_payment_data = []
            if (entity_data){
                $scope.own_entity_payment_data = entity_data
            }
            angular.element('#own_entity_payment').modal('show')
        };

        $scope.finUpdate = function (data, form) {
            $scope.finForm = form;
            console.log('data', data)
            if (!$scope.finForm.$invalid) {
                AdminServices.finUpdateSer(data).then(function (response) {
                    if (response.data.code.status === 200) {
                        swal("Good job!", response.data.code.message, "success")
                        angular.element('#fin_atr').modal('hide');
                    }
                    else {
                        swal("Cancelled", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            }
            else{
                swal("Please fill all required fields.", "", "error");
                $scope.submitted = true
            }
        };

        $scope.isNullValue = function(value) {
            return value === null || value === undefined
                || (((typeof value) === "string") ? value.trim() : value) === "";
        }

        $scope.getBrokerBalance = function(trip) {
            if($scope.isNullValue(trip.broker_data.balance)){
                if(trip.is_market){
                    const tds = ((trip.broker_data.broker_rate * 1)  * (trip.broker_data.broker_tds * 1)) / 100;
                    let broker_balance = (trip.broker_data.broker_rate * 1) - ((trip.broker_data.broker_advance * 1) + (trip.broker_data.fuel_advance * 1 ) + tds + (trip.advance_deductions * 1));
                    return broker_balance;
                }else{
                    return 'N.A'
                }
            }else{
                return trip.broker_data.balance;
            }
        }

        $scope.getPaymentStausList = function () {
            $scope.loading = true;
            AdminServices.getPaymentStatus().then(function (response) {
                $scope.loading = false;
                if (response.data.code.status === 200) {
                    $scope.payment_status_list = response.data.result.payment_status_list;
                } else {
                    swal('oops!', response.data.code.message)
                }
            })
        };

    }]);
