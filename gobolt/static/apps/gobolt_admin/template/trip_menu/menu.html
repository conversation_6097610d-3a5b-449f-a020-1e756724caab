<section class="">
    <loading></loading>
    <div class="row" ng-init="adminTripMenu()">
        <div class="col-xs-12">
            <div class="box">
                <div class="box-header">
                    <h3 class="box-title">Trip Reconciliations</h3>
                    &nbsp; &nbsp; &nbsp;
                    <button type="button" class="btn btn-info pointer clear-button " ng-disabled="csvUpdateLoading==true"
                            ng-click="updateTripCsvCurrent(filterData)">
                            <span ng-show="csvUpdateLoading === true"><i
                                    class="glyphicon glyphicon-refresh spinning"></i></span>
                        Update trip csv data
                    </button>


                    <div class="box-tools">
                        <div class="input-group input-group-sm " style="width: 165px">
                            <div class="input-group-btn">
                                <button type="submit" ng-click="clearFilter()" class="btn btn-default"><i
                                        class="fa fa-refresh"></i></button>
                            </div>
                            <input type="text" name="table_search" class="form-control pull-right" placeholder="Search"
                                ng-model="searchText" ng-change="setCountedValue()">

                            <div class="input-group-btn">
                                <button type="submit" class="btn btn-default"><i class="fa fa-search"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <br />

                <div class="row">

                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.customer" ng-init="getCustomerListNewRecon()"
                            ng-change="getLocationNewRecon(filterData.customer);getBrokerNewRecon(filterData.customer);getVehNoNewRecon(filterData.customer)"
                            theme="bootstrap" ng-required="true" multiple>
                            <ui-select-match allow-clear="true" placeholder="Select Customer" style="max-height: 100px; overflow-y: auto;">
                                {{ $item.company_name }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in customerName | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.company_name }}<{{ item.customer_code }}> "></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3">
                        <div class="typahead_container">
                            <input type="text" ng-model="filterData.origin" placeholder="Type to search Origin"
                                uib-typeahead="item as item for item in fetchOriginLocation($viewValue)" typeahead-editable="false"
                                typeahead-loading="originLoading" class="form-control typeaheadInput" ng-disabled="filterData.customer.length>1"/>
                    
                            <!-- Loading spinner -->
                            <span ng-show="originLoading" class="spinner-icon">
                                <i class="fa fa-spinner fa-spin"></i>
                            </span>
                            <!-- Clear button -->
                            <span ng-show="filterData.origin && !originLoading" class="clear_icon" ng-click="filterData.origin = ''">
                                <i class="glyphicon glyphicon-remove"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="typahead_container">
                            <input type="text" ng-model="filterData.destination" placeholder="Type to search Destination"
                                uib-typeahead="item as item for item in fetchDestinationLocation($viewValue)" typeahead-editable="false"
                                typeahead-loading="destinationLoading" class="form-control typeaheadInput" ng-disabled="filterData.customer.length>1"/>
                    
                            <!-- Loading spinner -->
                            <span ng-show="destinationLoading" class="spinner-icon">
                                <i class="fa fa-spinner fa-spin"></i>
                            </span>
                            <!-- Clear button -->
                            <span ng-show="filterData.destination && !destinationLoading" class="clear_icon"
                                ng-click="filterData.destination = ''">
                                <i class="glyphicon glyphicon-remove"></i>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.origin" theme="bootstrap"
                            ng-change="destinationBasedOriginRecon(filterData.origin);oDBasedBroker(filterData.customer);oDBasedVehNo(filterData.customer)"
                            ng-click="checkIfCustRecon(filterData.customer)" ng-required="true" ng-disabled="filterData.customer.length>1">
                            <!--ng-disabled="disable_uiselect">-->
                            <!-- <ui-select-match allow-clear="true" placeholder="Select Origin">
                                {{ $select.selected.location_name }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in origin | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.location_name }}"></div>
                            </ui-select-choices> -->
                            <!--<ui-select-no-choice>-->
                            <!--<b>Show All.</b>-->
                            <!--<span class="pointer"-->
                            <!--ng-click="showAllLocationCustomer(filterData.customer)">-->
                            <!--<img src="../static/apps/common/web-font/checklist.png"/>-->
                            <!--</span>-->
                            <!--</ui-select-no-choice>-->
                        <!-- </ui-select> -->
                    <!-- </div> -->
                    <!-- <div class="col-md-3"> -->
                        <!--<i class="fa fa-spinner fa-pulse fa-3x fa-fw dropdown-loader"-->
                        <!--ng-if="disable_uiselect"></i>-->
                        <!-- <ui-select name="customer" ng-model="filterData.destination" theme="bootstrap" -->
                            <!-- ng-click="checkIfCustRecon(filterData.customer)"
                            ng-change="destinationLocationRecon(filterData.origin, filterData.destination);oDBasedBroker(filterData.customer);oDBasedVehNo(filterData.customer)"
                            ng-required="true" ng-disabled="filterData.customer.length>1"> -->
                            <!--ng-disabled="disable_uiselect">-->
                            <!-- <ui-select-match allow-clear="true" placeholder="Select Destination">
                                {{ $select.selected.location_name }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in destination | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.location_name }}"></div> -->
                            <!-- </ui-select-choices> -->
                        <!-- </ui-select> -->
                    <!-- </div> -->
                    <div class="col-md-3">
                        <!--<i class="fa fa-spinner fa-pulse fa-3x fa-fw dropdown-loader"-->
                        <!--ng-if="disable_uiselect"></i>-->
                        <ui-select name="customer" ng-model="filterData.vehicle_number"
                            ng-init="getVehNoNewRecon(undefined)" theme="bootstrap" ng-required="true">
                            <!--ng-disabled="disable_uiselect">-->
                            <ui-select-match allow-clear="true" placeholder="Select Vehicle No">
                                {{ $select.selected._source.vehicle_data.vehicle_registration_number }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in vehicleNo | filter: $select.search | limitTo: 20 track by $index">
                                <div ng-bind-template="{{ item._source.vehicle_data.vehicle_registration_number }}">
                                </div>
                            </ui-select-choices>
                            <!--<ui-select-no-choice>-->
                            <!--<b>Show All Vehicles..</b>-->
                            <!--<span class="pointer" ng-click="showAllVehicles()">-->
                            <!--<img src="../static/apps/common/web-font/checklist.png"/>-->
                            <!--</span>-->
                            <!--</ui-select-no-choice>-->
                        </ui-select>
                    </div>
                </div>
                <br />

                <div class="row">
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.placement_date"
                            placeholder="From 'Placement  Date'" jqdatepicker="" required autocomplete="off" />
                    </div>
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.to_placement_date"
                            placeholder="To 'Placement  Date'" jqdatepicker="" required autocomplete="off" />
                    </div>

                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.vr_from_date"
                            placeholder="From  Vehicle Required Date" jqdatepicker="" required autocomplete="off" />
                    </div>
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.vr_to_date"
                            placeholder="To  Vehicle Required Date" jqdatepicker="" required autocomplete="off" />
                    </div>
                </div>
                <br />

                <div class="row">
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.s_from_date"
                            placeholder="From  Start Date" jqdatepicker="" required autocomplete="off" />
                    </div>
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date" ng-model="filterData.s_to_date"
                            placeholder="To  Start Date" jqdatepicker="" required autocomplete="off" />
                    </div>
                    <div class="col-md-3">
                        <!--<i class="fa fa-spinner fa-pulse fa-3x fa-fw dropdown-loader"-->
                        <!--ng-if="disable_uiselect"></i>-->
                        <ui-select name="customer" ng-model="filterData.broker" theme="bootstrap"
                            ng-init="getBrokerNewRecon(undefined)" ng-required="true" multiple>
                            <!--ng-disabled="disable_uiselect">-->
                            <ui-select-match allow-clear="true" placeholder="Select Broker" style="max-height: 100px; overflow-y: auto;">
                                {{ $item._source.broker_data.company }}
                                <{{ $item._source.broker_data.mobile_number }}>
                            </ui-select-match>
                            <ui-select-choices repeat="item in brokerName | filter: $select.search track by $index">
                                <div
                                    ng-bind-template="{{ item._source.broker_data.company }}<{{ item._source.broker_data.mobile_number }}>">
                                </div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.trip_type" theme="bootstrap" ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Trip Validity">
                                {{ $select.selected }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in trip_t | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>

                <br />

                <div class="row">
                    <div class="col-md-3">
                        <ui-select name="trip_type" ng-model="filterData.is_market" theme="bootstrap"
                            ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Trip Type">
                                {{ $select.selected }}
                            </ui-select-match>
                            <ui-select-choices
                                repeat="item in ['Own Trip', 'Market Trip'] | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3 ">
                        <ui-select name="customer" ng-model="filterData.vehicle_type" theme="bootstrap"
                            ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Vehicle Type">
                                {{ $select.selected.name }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in vehicleType | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3">
                        <input type="text" class="form-control" name="order_trip_code"
                            ng-model="filterData.order_trip_code" placeholder="OrderCode/ TripCode" />
                    </div>

                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.source_name"
                                   theme="bootstrap"
                                   ng-required="true" ng-init="getSourceList()">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Trip Source">
                                {{ $select.selected.name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in trip_source_type| filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-md-3 ">
                        <ui-select name="customer" ng-model="filterData.placement_type" theme="bootstrap"
                            ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Placement Type">
                                {{ $select.selected }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in placementType | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3 ">
                        <ui-select name="primary_driver" ng-model="filterData.primary_driver" theme="bootstrap" ng-init="getAllDriversList()"
                            ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Primary Driver">
                                {{ $select.selected.driver_name }}({{ $select.selected.driver_code }})
                            </ui-select-match>
                            <ui-select-choices repeat="item in trip_reconciliations_driver_filter_list | filter: $select.search | limitTo: 20 track by $index">
                                <div ng-bind-template="{{ item.driver_name }} ({{ item.driver_code }})"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3 ">
                        <ui-select name="secondary_driver" ng-model="filterData.secondary_driver" theme="bootstrap"
                            ng-required="true">
                            <ui-select-match allow-clear="true" placeholder="Select Secondary Driver">
                                {{ $select.selected.driver_name }}({{ $select.selected.driver_code }})
                            </ui-select-match>
                            <ui-select-choices repeat="item in trip_reconciliations_driver_filter_list | filter: $select.search | limitTo: 20 track by $index">
                                <div ng-bind-template="{{ item.driver_name }} ({{ item.driver_code }})"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3 ">
                        <ui-select name="payment_status" ng-model="filterData.payment_status" theme="bootstrap"
                            ng-required="true" ng-init="getPaymentStausList()">
                            <ui-select-match allow-clear="true" placeholder="Select Payment Status">
                                {{ $select.selected.payment_status_name }}
                            </ui-select-match>
                            <ui-select-choices repeat="item in payment_status_list | filter: $select.search | limitTo: 20 track by $index">
                                <div ng-bind-template="{{ item.payment_status_name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <br />
                    <div class="col-md-4 pull-right text-right" style="margin-top: 10px">
                        <span type="button" class="btn btn-info pointer apply-button"
                              ng-click="TripFilter(count, filterData)">Apply Filter</span>&nbsp;&nbsp;&nbsp;
                        <span type="button" class="btn btn-info clear-button pointer"
                              ng-click="clearFilter()">Clear</span>&nbsp;&nbsp;&nbsp;
                        <button type="button" class="btn btn-info pointer clear-button" ng-disabled="csvloading==true"
                                ng-click="completedExportDataUpdated(filterData)">
                            <span ng-show="csvloading === true"><i
                                    class="glyphicon glyphicon-refresh spinning"></i></span>
                            Export
                            CSV
                        </button>
                        &nbsp;&nbsp;&nbsp;<br><br>
                        <div ng-show="csvloading === true" class="progress progress-xs"
                            style="height: 25px; background: lightgrey">
                            <div class="progress-bar progress-bar-green" style="width: {{ progress_bar_per }}%">
                                <b>{{ progress_bar_per }}%</b>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- /.box-header -->
                <div ng-if="trip_list.length && counted !=0">
                    <div class="box-body">
                        <scrollable-table>
                            <table class="table table-striped table-bordered ">
                                <thead>
                                    <tr>
                                        <th>Sr. No</th>
                                        <th ng-click="sortData('contract_id')">
                                            <div ng-class="getSortClass('contract_id')"></div>
                                            Contract ID
                                        </th>
                                        <th ng-click="sortData('trip_code')">
                                            <div ng-class="getSortClass('trip_code')"></div>
                                            Trip Code
                                        </th>
                                        <th ng-click="sortData('order_code')">
                                            <div ng-class="getSortClass('order_code')"></div>
                                            Order Code
                                        </th>
                                        <th ng-click="sortData('order_code')">
                                            <div ng-class="getSortClass('order_code')"></div>
                                            Broker Code
                                        </th>
                                        <th ng-click="sortData('demand_reference_id')">
                                            <div ng-class="getSortClass('demand_reference_id')"></div>
                                            Vehicle Ref <br />ID
                                        </th>


                                        <th ng-click="sortData('source_name')">
                                            <div ng-class="getSortClass('source_name')"></div>
                                            Trip <br/>Source
                                        </th>
                                        <th>
                                            Trip <br/>Recovery ID
                                        </th>
                                        <th>
                                            Recovery <br/>Amount
                                        </th>
                                        <th ng-click="sortData('placement_type')">
                                            <div ng-class="getSortClass('placement_type')"></div>
                                            Placement<br />Type
                                        </th>

                                        <th ng-click="sortData('lr.lr_data')">
                                            <div ng-class="getSortClass('lr.lr_data')"></div>
                                            LR No
                                        </th>
                                        <th ng-click="sortData('company_name')">
                                            <div ng-class="getSortClass('company_name')"></div>
                                            Customer
                                        </th>
                                        <th ng-click="sortData('order_data.origin.location_name')">
                                            <div ng-class="getSortClass('order_data.origin.location_name')"></div>
                                            Origin
                                        </th>
                                        <th ng-click="sortData('order_data.destination.location_name')">
                                            <div ng-class="getSortClass('order_data.destination.location_name')"></div>
                                            Destination
                                        </th>
                                        <th ng-click="sortData('order_data.destination.location_name')">
                                            <div ng-class="getSortClass('order_data.destination.location_name')"></div>
                                            Start-End <br /> Point
                                        </th>
                                        <th ng-click="sortData('order_data.driver_name')">
                                            <div ng-class="getSortClass('order_data.driver_name')"></div>
                                            Driver Name/No
                                        </th>
                                        <th ng-click="sortData('order_data.driver_name')">
                                            <div ng-class="getSortClass('order_data.driver_name')"></div>
                                            Second Driver <br /> Name/No
                                        </th>
                                        <th ng-click="sortData('order_data.vehicle_no')">
                                            <div ng-class="getSortClass('order_data.vehicle_no')"></div>
                                            Vehicle No.
                                        </th>
                                        <th ng-click="sortData('lane.lane_code')">
                                            <div ng-class="getSortClass('lane.lane_code')"></div>
                                            Route
                                        </th>
                                        <th ng-click="sortData('trip_status.status_name')">
                                            <div ng-class="getSortClass('trip_status.status_name')"></div>
                                            Trip Status
                                        </th>
                                        <th ng-class="abc" ng-click="sortData('vehicle_placement_date')">
                                            <div ng-class="getSortClass('vehicle_placement_date')"></div>
                                            Placement <br /> Date&Time
                                        </th>
                                        <th ng-click="sortData('trip_start_date')">
                                            <div ng-class="getSortClass('trip_start_date')"></div>
                                            Start <br /> Date&Time
                                        </th>
                                        <th ng-click="sortData('trip_end_date')">
                                            <div ng-class="getSortClass('trip_end_date')"></div>
                                            Reporting <br /> Date&Time
                                        </th>
                                        <th ng-click="sortData('unloading_date')">
                                            <div ng-class="getSortClass('unloading_date')"></div>
                                            Unloading <br /> Date&Time
                                        </th>
                                        <th ng-click="sortData('tat')">
                                            <div ng-class="getSortClass('tat')"></div>
                                            TAT (In Hours) <br />
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Buyer/HM
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Vehicle Type
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Broker Name
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Broker <br />Phone no
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Broker PAN no
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Contract <br /> Rate
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Broker <br /> Rate
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            POD <br>Adjustments
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Total Paid<br /> Advance
                                        </th>
                                        <th>
                                            Other<br /> Advance
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Total Unpaid <br /> Advance
                                        </th>

                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Deductions
                                        </th>
                                        <th ng-click="sortData('buyer_data.name')">
                                            <div ng-class="getSortClass('buyer_data.name')"></div>
                                            Broker <br /> Balance
                                        </th>
                                        <th>Gross Cost</th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            TDS
                                        </th>
                                        <th>Net Cost</th>
                                        <th ng-click="sortData('customer_pay')">
                                            <div ng-class="getSortClass('customer_pay')"></div>
                                            Total <br /> Revenue
                                        </th>
                                        <th ng-click="sortData('customer_pay')">
                                            <div ng-class="getSortClass('customer_pay')"></div>
                                            Profit/Loss
                                        </th>

                                        <th ng-click="sortData('payment_status.payment_status_name')">
                                            <div ng-class="getSortClass('payment_status.payment_status_name')"></div>
                                            Payment <br /> Status
                                        </th>
                                        <th>POD</th>
                                        <th>Finance <br /> Attrib</th>
                                        <th>Own Advance<br />
                                            Payment Status</th>
                                        <th>
                                            Trip Validity
                                        </th>
                                    </tr>
                                </thead>
                                <br />
                                <tbody>
                                    <tr dir-paginate="trip in trip_list   | itemsPerPage: 100 | orderBy: ['vehicle_placement_date', 'vehicle_placement_time']:true | filter : searchText | orderBy:sortColumn:reverseSort track by $index"
                                        total-items="TripDataList.count" current-page="pagination.current">

                                        <td>
                                            {{ ($index + 1) + ((pagination.current ? pagination.current : 1) - 1) * 100 }}
                                        </td>
                                        <td ng-bind="trip.contract_id"></td>
                                        <td ng-bind="trip.trip_code"></td>
                                        <td ng-bind="trip.order_code"></td>
                                        <td ng-bind="trip.broker_data.broker_code || 'NA'"></td>
                                        <td ng-bind="trip.order_data.demand_reference_id"></td>
                                        <td ng-bind="trip.source_name"></td>
                                        <td ng-bind="trip.trip_recovery_data.recovery_ids"></td>
                                        <td ng-bind="trip.trip_recovery_data.amount"></td>
                                        <td ng-bind="trip.placement_type"></td>
                                        <td>
                                            <span href="#" ng-repeat="lr_data in trip.lr.lr_data "
                                                ng-click="showLrNumberPicture(lr_data.pic_url)">{{ lr_data.lr_reciept }}
                                                <br /></span>
                                        </td>
                                        <td
                                            ng-bind-template="{{ trip.company_name }}<{{ trip.order_data.customer_code }}>">
                                        </td>

                                        <td ng-bind="trip.order_data.origin.location_name"></td>
                                        <td ng-bind="trip.order_data.destination.location_name"></td>
                                        <td><span>{{dataBindD(trip.order_data.origin.location_name, trip.order_data.multiple_stopage, trip.order_data.destination.location_name)}}
                                            </span>
                                        <td>
                                            <p ng-class="{'red': trip.is_market === true, 'green':trip.is_market === false }"
                                                data-toggle="modal" data-target="#biltyDriverDetails" class="pointer"
                                                ng-click="driverDetailsAccount(completedTrip)">
                                                <span ng-bind="trip.order_data.driver_name"></span>(<span
                                                    ng-bind="trip.order_data.driver_no"></span>)
                                            </p>
                                        </td>
                                        <td ng-if="trip.order_data.co_driver_name">
                                            <p ng-class="{'red': trip.is_market === true, 'green':trip.is_market === false }"
                                                data-toggle="modal" data-target="#biltyDriverDetails" class="pointer"
                                                ng-click="driverDetailsAccount(completedTrip)">
                                                <span ng-bind="trip.order_data.co_driver_name"></span>(<span
                                                    ng-bind="trip.order_data.co_driver_no"></span>)
                                            </p>
                                        </td>
                                        <td ng-if="trip.order_data.co_driver_name==''"></td>
                                        <td ng-class="{'red': trip.is_market === true, 'green':trip.is_market === false }"
                                            data-toggle="modal" data-target="#biltyVehicleDetails"
                                            ng-bind="trip.order_data.vehicle_no" class="pointer"
                                            ng-click="vehicleDetailAccount(completedTrip)"></td>
                                        <td ng-bind="trip.lane.lane_code"></td>
                                        <td ng-bind="trip.trip_status.status_name"></td>

                                        <td
                                            ng-bind-template="{{ trip.vehicle_placement_date }}  {{ trip.vehicle_placement_time | formatTime }}">
                                        </td>
                                        <td ng-bind-template="{{ trip.trip_start_date | date:'yyyy-MM-dd ' }}
                                        {{ trip.trip_start_time | date:'HH:mm' }}"></td>
                                        <td ng-bind-template="{{ trip.trip_end_date | date:'yyyy-MM-dd ' }}
                                        {{ trip.trip_end_time | date:'HH:mm' }}"></td>
                                        <td ng-bind-template="{{ trip.unloading_date | date:'yyyy-MM-dd ' }}
                                        {{ trip.unloading_time | date:'HH:mm' }}"></td>
                                        <td ng-bind="trip.tat"></td>
                                        <td ng-show="trip.is_market === true">
                                            <span ng-bind-template="{{ trip.buyer_data.name }}"></span><br>
                                            <span ng-bind-template="<{{ trip.buyer_data.code }}>"></span></td>
                                        <td ng-show="trip.is_market === false">
                                            <span ng-bind-template="{{ trip.buyer_data.name }}"></span><br>
                                            <span ng-bind-template="<{{ trip.buyer_data.code }}>"></span></td>
                                        <td ng-bind="trip.vehicle_type"></td>
                                        <td ng-bind="trip.broker_data.company || 'N.A'"></td>
                                        <td ng-bind="trip.broker_data.mobile_no || 'N.A'"></td>
                                        <td ng-bind="trip.broker_data.broker_bank.pan_no || 'N.A'"></td>
                                        <td ng-bind="trip.contract || 'N.A'"></td>
                                        <td ng-bind="trip.broker_data.broker_rate || 'N.A'"></td>
                                        <td ng-if="trip.is_market == true" ng-bind="podCalculation(trip.pod_data)"></td>
                                        <td ng-if="trip.is_market == false">0</td>

                                        <td ng-if='trip.is_market == true'>
                                            <span>{{ (trip.broker_data.broker_advance * 1) +
                                            (trip.broker_data.broker_advance_cash * 1) }}</span>
                                        </td>
                                        <td ng-if='trip.is_market == false' ng-bind="trip.adv_data.total_adv || 'N.A.'">
                                        </td>
                                        <td ng-bind="trip.broker_data.fuel_advance">
                                        </td>
                                        <td ng-if='trip.is_market == false' ng-bind="trip.own_unpaid_adv_cost">
                                        </td>
                                        <td ng-if="trip.is_market == true">0</td>

                                        <td ng-bind="trip.advance_deductions || '0'"></td>
                                        <td>{{ getBrokerBalance(trip) }}</td>
                                        <td>

                                            <!------ 1st condition and its 4 sub conditions for completed pod status --------->
                                            <!-- When loading and unloading amount both paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == true && trip.loading_ent == true'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ ((trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + (trip.pod_data.pod_broker_data.unloading_charge  * 1) + (trip.pod_data.pod_broker_data.loading_charge  * 1)) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100)  + (trip.advance_deductions * 1)}}"></span>
                                                </a>
                                            </span>
                                            <!-- When loading and unloading amount both not paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == false && trip.loading_ent == false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ ((trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1)) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.unloading_charge  * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1 +
                                                    trip.pod_data.pod_broker_data.loading_charge * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100) + (trip.advance_deductions * 1)}}"></span>
                                                </a></span>
                                            <!-- When only loading amount paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == false && trip.loading_ent == true'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ ((trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1)  + (trip.pod_data.pod_broker_data.loading_charge  * 1)) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge  * 1 +
                                                    trip.pod_data.pod_broker_data.unloading_charge  * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100) + (trip.advance_deductions * 1)}}"></span>
                                                </a>
                                            </span>
                                            <!-- When only unloading amount paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == true && trip.loading_ent == false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ ((trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1)  + (trip.pod_data.pod_broker_data.unloading_charge  * 1)) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge  * 1 +
                                                    trip.pod_data.pod_broker_data.loading_charge  * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100)+ (trip.advance_deductions * 1)}}"></span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) +
                                                    (trip.advance_deductions * 1)}}</span>
                                                </a>
                                            </span>


                                            <!------ 2nd condition and its 4 sub conditions for completed pod status   --------->
                                            <!-- When loading and unloading amount both paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == true && trip.loading_ent == true'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ (trip.broker_data.balance * 1) + (trip.pod_data.pod_broker_data.unloading_charge * 1) + (trip.pod_data.pod_broker_data.loading_charge * 1) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1 ) *
                                                    trip.pod_data.pod_broker_data.tds) / 100 )+ (trip.advance_deductions * 1)}} "></span>
                                                </a>
                                            </span>

                                            <!-- When only loading paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == false && trip.loading_ent == true'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ (trip.broker_data.balance * 1) + (trip.pod_data.pod_broker_data.loading_charge * 1) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    trip.pod_data.pod_broker_data.unloading_charge  * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100 )+ (trip.advance_deductions * 1)}} "></span>
                                                </a>
                                            </span>

                                            <!-- When only unloading paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == true && trip.loading_ent == false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="
                                                    {{ (trip.broker_data.balance * 1) + (trip.pod_data.pod_broker_data.unloading_charge * 1) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    +
                                                    trip.pod_data.pod_broker_data.taxes * 1) *
                                                    trip.pod_data.pod_broker_data.tds) / 100)+ (trip.advance_deductions * 1)}} "></span>
                                                </a>
                                            </span>

                                            <!-- When loading and unloading amount both not paid via entity payment -->
                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Completed" && trip.unloading_ent == false && trip.loading_ent == false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template="{{ (trip.broker_data.balance * 1) +
                                                    (((trip.broker_data.broker_rate * 1 +
                                                    trip.pod_data.pod_broker_data.unloading_charge * 1 +
                                                    trip.pod_data.pod_broker_data.b_surcharge * 1 +
                                                    trip.pod_data.pod_broker_data.loading_charge * 1 +
                                                    trip.pod_data.pod_broker_data.detention * 1 +
                                                    trip.pod_data.pod_broker_data.taxes * 1 ) *
                                                    trip.pod_data.pod_broker_data.tds) / 100)+ (trip.advance_deductions * 1)}} "></span>
                                                </a>
                                            </span>


                                            <!-- As discussed TDS excluded on 11 dec 2018 from gross cost from pending pod -->

                                            <!--  <span ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Pending"'>
                                        <a href="" class="pointer tooltips" data-toggle="modal" data-target="#podData"
                                           ng-click="podData(trip, 'total-cost')">
                                            <span>{{ trip.broker_data.balance * 1 +
                                                    ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) + (trip.advance_deductions * 1)}}</span>
                                        </a>
                                        </span>-->

                                            <span
                                                ng-if='(trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed") && trip.pod_status=="Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ trip.broker_data.balance * 1  + (trip.advance_deductions * 1)}}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && trip.trip_status.status_name !="Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ trip.broker_data.broker_rate * 1 }}</span>
                                                </a>
                                            </span>

                                            <span ng-if='trip.is_market==false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#actualPodData" ng-click="actualPodData(trip)">
                                                    <span>{{ trip.actual_data.total_all * 1 + (trip.advance_deductions * 1)}}</span>
                                                </a>
                                            </span>
                                        </td>
                                        <td>
                                            <!-- This span is used for calculate tds when both load and unload amounts paid by enity payment -->
                                            <span
                                                ng-if="trip.pod_status == 'Completed' && trip.unloading_ent == true && trip.loading_ent == true"
                                                ng-bind="((({{ trip.broker_data.broker_rate * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.detention * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.b_surcharge * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.taxes * 1 }}) *
                                            {{ trip.pod_data.pod_broker_data.tds }}) / 100  + ((
                                                      {{ trip.pod_data.pod_broker_data.unloading_charge  * 1 }}  +
                                                      {{ trip.pod_data.pod_broker_data.loading_charge * 1 }}) / 100))">
                                            </span>


                                            <!-- This span is used for calculate tds when only unload amount paid by enity payment -->

                                            <span
                                                ng-if="trip.pod_status == 'Completed' && trip.unloading_ent == true && trip.loading_ent == false"
                                                ng-bind="((({{ trip.broker_data.broker_rate * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.detention * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.b_surcharge * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.taxes * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.loading_charge * 1 }}) *
                                            {{ trip.pod_data.pod_broker_data.tds }}) / 100  + (



                                                      {{ trip.pod_data.pod_broker_data.unloading_charge  * 1 }} / 100))">
                                            </span>

                                            <!-- This span is used for calculate tds when only load amount paid by enity payment -->

                                            <span
                                                ng-if="trip.pod_status == 'Completed' && trip.unloading_ent == false && trip.loading_ent == true"
                                                ng-bind="((({{ trip.broker_data.broker_rate * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.detention * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.b_surcharge * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.taxes * 1 }} +
                                            {{ trip.pod_data.pod_broker_data.unloading_charge * 1 }}) *
                                            {{ trip.pod_data.pod_broker_data.tds }}) / 100  + (
                                                      {{ trip.pod_data.pod_broker_data.loading_charge  * 1 }} / 100))">
                                            </span>

                                            <!-- This span is used for calculate tds when only both load and unload amount not paid by enity payment -->
                                            <span
                                                ng-if="trip.pod_status == 'Completed' && trip.unloading_ent == false && trip.loading_ent == false">

                                                {{ (((  trip.broker_data.broker_rate * 1  +
                                                    trip.pod_data.pod_broker_data.detention * 1  +
                                                    trip.pod_data.pod_broker_data.b_surcharge  * 1  +
                                                    trip.pod_data.pod_broker_data.unloading_charge  * 1  +
                                                    trip.pod_data.pod_broker_data.taxes * 1  +
                                                    trip.pod_data.pod_broker_data.loading_charge * 1 ) *
                                                    trip.pod_data.pod_broker_data.tds ) / 100) }}

                                            </span>
                                            <span ng-if="trip.pod_status == 'Pending' && trip.is_market != false"
                                                ng-bind-template="
                                                      {{ ((trip.broker_data.broker_rate * 1)  * (trip.broker_data.broker_tds * 1)) / 100}} ">
                                            </span>
                                            <span ng-if="trip.is_market == false" ng-bind="'N.A.'"></span>
                                        </td>
                                        <td>
                                            <!-- 1st Condition -->
                                            <!-- When unloading and loading both not paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) }}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}</span>
                                                </a>
                                            </span>

                                            <!-- When unloading and loading both paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100 )  + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100)}}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100 )  + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}</span>
                                                </a>
                                            </span>

                                            <!-- When only unloading paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100)}}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100)  - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}</span>
                                                </a>
                                            </span>

                                            <!-- When only loading paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100)}}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.broker_advance * 1) + (trip.broker_data.balance * 1) + (trip.broker_data.broker_advance_cash * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}</span>
                                                </a>
                                            </span>
                                            <!-- 2nd Condition -->
                                            <!-- When unloading and loading not both paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ trip.broker_data.balance * 1 }}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span
                                                        ng-bind-template=" {{ (trip.broker_data.balance * 1) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}"></span>
                                                </a>
                                            </span>

                                            <!-- When unloading and loading both paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) }}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}</span>
                                                </a>
                                            </span>

                                            <!-- When only unloading paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) }}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}</span>
                                                </a>
                                            </span>

                                            <!-- When only loading paid by entity-->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) }}</span>
                                                </a>
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Pending"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ (trip.broker_data.balance * 1) + ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100)  - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}</span>
                                                </a>
                                            </span>


                                            <span
                                                ng-if='trip.is_market == true && trip.trip_status.status_name !="Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-cost')">
                                                    <span>{{ trip.broker_data.broker_rate * 1  - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) - (trip.advance_deductions * 1)}}</span>
                                                </a>
                                            </span>

                                            <span ng-if='trip.is_market==false'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#actualPodData" ng-click="actualPodData(trip)">
                                                    <span>{{ trip.actual_data.total_all * 1 }}</span>
                                                </a>
                                            </span>
                                        </td>
                                        <td>
                                            <span ng-if='trip.trip_status.status_name =="Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-revenue')">
                                                    <span>{{ trip.customer_pay }}</span>
                                                </a>
                                            </span>

                                            <span ng-if='trip.trip_status.status_name !="Completed"'>
                                                <a href="" class="pointer tooltips" data-toggle="modal"
                                                    data-target="#podData" ng-click="podData(trip, 'total-revenue')">
                                                    <span>{{ trip.contract }}</span>
                                                </a>
                                            </span>
                                        </td>
                                        <td>
                                            <!-- 1st condition  -->
                                            <!-- When load and unload both amount not paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Completed"'>
                                                {{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                trip.broker_data.broker_advance_cash * 1) -
                                                (trip.broker_data.balance * 1) }}
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Pending"'>
                                                {{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                trip.broker_data.broker_advance_cash * 1) -
                                                (trip.broker_data.balance * 1) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}
                                            </span>

                                            <!-- When both load and unload amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Completed"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                  trip.broker_data.broker_advance_cash * 1) -
                                                  (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) }}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Pending"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                      trip.broker_data.broker_advance_cash * 1) -
                                                      (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) }}">
                                            </span>

                                            <!-- When only load  amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Completed"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                  trip.broker_data.broker_advance_cash * 1) -
                                                  (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) }}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == false && trip.loading_ent == true && trip.pod_status == "Pending"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                      trip.broker_data.broker_advance_cash * 1) -
                                                      (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>
                                            <!-- When only unload  amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Completed"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                  trip.broker_data.broker_advance_cash * 1) -
                                                  (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) }}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name=="Advance Paid" || trip.payment_status.payment_status_name=="Balance Paid") && trip.trip_status.status_name =="Completed"  && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Pending"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.broker_advance * 1 +
                                                      trip.broker_data.broker_advance_cash * 1) -
                                                      (trip.broker_data.balance * 1) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>
                                            <!-- 2nd condition -->
                                            <!-- When load and unload both amount not paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Completed"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) }}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == false && trip.pod_status == "Pending"'
                                                ng-bind-template="{{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>

                                            <!-- When load and unload both amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Completed"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100)}}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == true && trip.pod_status == "Pending"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>


                                            <!-- When only load amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true  && trip.pod_status == "Completed"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100)}}">
                                            </span>

                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == false && trip.loading_ent == true  && trip.pod_status == "Pending"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.loading_charge * 1) - (trip.pod_data.pod_broker_data.loading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>
                                            <!-- When only unload amount paid by entity -->
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Completed"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100)}}">
                                            </span>
                                            <span
                                                ng-if='trip.is_market == true && (trip.payment_status.payment_status_name!="Advance Paid" && trip.payment_status.payment_status_name!="Balance Paid") && trip.trip_status.status_name =="Completed" && trip.unloading_ent == true && trip.loading_ent == false && trip.pod_status == "Pending"'
                                                ng-bind-template="
                                                      {{ (trip.customer_pay * 1) - (trip.broker_data.balance * 1 ) - ((trip.pod_data.pod_broker_data.unloading_charge * 1) - (trip.pod_data.pod_broker_data.unloading_charge * 1)/100) +  ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100)}}">
                                            </span>


                                            <span
                                                ng-if='trip.is_market == true && trip.trip_status.status_name !="Completed"'>
                                                {{ (trip.contract * 1) - (trip.broker_data.broker_rate * 1  - ((trip.broker_data.broker_rate * 1 * trip.broker_data.broker_tds) / 100) - (trip.advance_deductions * 1)) }}

                                                <span
                                                    ng-if='trip.is_market==false && trip.trip_status.status_name =="Completed"'>
                                                    {{ (trip.customer_pay * 1) - (trip.actual_data.total_all * 1) }}
                                                </span>

                                                <span
                                                    ng-if='trip.is_market==false && trip.trip_status.status_name !="Completed"'>
                                                    {{ (trip.contract * 1) - (trip.actual_data.total_all * 1) }}
                                                </span>
                                        </td>
                                        <td ng-if="trip.own_advance_data.length">N.A</td>
                                        <td ng-if="!trip.own_advance_data.length">
                                            {{ trip.payment_status.payment_status_name }}</td>
                                        <td><span ng-bind="trip.pod_status"></span>
                                            <br>
                                            <span ng-bind="trip.pod_uploaded_by"></span>
                                            <br>
                                            <span ng-bind="trip.pod_uploaed_on"></span>
                                        </td>
                                        <td>
                                            <a ng-if="trip.trip_status.status_name == 'In Transit'" data-toggle="modal"
                                                data-target="#fin_atr" href="javascript:void(0)" class="pointer "
                                                ng-click="finGet(trip)">
                                                <span data-toggle="tooltip" data-placement="top"
                                                    title="Finance Attributes">
                                                    <img src="../static/apps/common/web-font/edit.png" />
                                                </span>
                                            </a>
                                        </td>
                                        <td ng-if="trip.own_advance_data.length">
                                            <a ng-click="entityPaymentStatus(trip.own_advance_data, own_adv_cost)">
                                                <button type="button" class="btn btn-info">Check Status</button></a>
                                        </td>
                                        <td ng-if="!trip.own_advance_data.length">
                                        </td>
                                        <td ng-bind="trip.trip_validity"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </scrollable-table>
                    </div>
                    <div ng-if='wf ==0'>
                        <dir-pagination-controls max-size="8" direction-links="true" boundary-links="true"
                            on-page-change="adminTripMenu(newPageNumber)" class="ng-isolate-scope">
                        </dir-pagination-controls>
                    </div>
                    <div ng-if='wf==1'>
                        <dir-pagination-controls max-size="8" direction-links="true" boundary-links="true"
                            on-page-change="TripFilter(newPageNumber,filterData)" class="ng-isolate-scope">
                        </dir-pagination-controls>
                    </div>
                    <!-- /.box-body -->
                </div>
                <div class="container" style="height: 600px;" ng-if="(!trip_list.length || counted ==0) && !loading">
                    <div class="row">
                        <div class="col-md-4"></div>
                        <div class="col-md-4">
                            <img style="margin-top: 50px" src="../static/apps/common/images/noData.png">
                        </div>
                        <div class="col-md-4"></div>
                    </div>
                </div>
            </div>
            <!-- /.box -->
        </div>
    </div>
</section>

<div class="modal fade" id="lr_number_pic" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <header class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close Modal</span></button>
                <h4 class="modal-title">LR</h4>
            </header>
            <!-- form start -->
            <form class="form-horizontal" novalidate="">
                <div align="center">
                    <img ng-src="{{ showLrNumPic }}" height=auto width=100% />
                </div>
            </form>
        </div>
    </div>
</div>



<div class="modal fade" id="own_entity_payment" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <header class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close Modal</span></button>
                <h4 class="modal-title">Own Entity Advance Payment</h4>
            </header>
            <!-- form start -->
            <div class="modal-body">
            <table class="table">
                <thead>
                  <tr>
                    <th>Entity Name</th>
                    <th>Entity Type</th>
                    <th>Amount</th>
                    <th>Created At</th>
                    <th>Paid At</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="entity_data in own_entity_payment_data">
                    <td>{{ entity_data.entity__entity_name }}</td>
                    <td>{{ entity_data.entity_payment_type__entity_pay_type_name }}</td>
                    <td>{{ entity_data.amount }}</td>
                    <td>{{ entity_data.created_at | date:'dd-MM-yyyy hh:mm a' }}</td>
                    <td>{{ entity_data.pay_date_time | date:'dd-MM-yyyy hh:mm a' }}</td>
                      <td ng-if="entity_data.is_paid">Paid</td>
                      <td ng-if="!entity_data.is_paid">UnPaid</td>

                  </tr>

                </tbody>
            </table>
                </div>
             <div class="modal-footer">
                <button type="button" class="clear-button btn btn-default" data-dismiss="modal">Close</button>
             </div>
        </div>
    </div>
</div>

<div ng-include="'../static/apps/gobolt_admin/template/trip_menu/pod_data.html'"></div>
<div ng-include="'../static/apps/gobolt_admin/template/trip_menu/finance_attrib.html'"></div>
<div ng-include="'../static/apps/gobolt_admin/template/trip_menu/actual_pod_data.html'"></div>