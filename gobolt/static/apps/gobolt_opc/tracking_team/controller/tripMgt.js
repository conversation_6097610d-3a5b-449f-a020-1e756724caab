/**
 * Created by som on 3/9/16.
 */

angular.module('myApp')
    .controller('trip.listing', ['$scope', '$state', '$rootScope', 'trackingServices', function ($scope, $state, $rootScope, trackingServices) {

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Data Connection Lost")
            }

        });

        $scope.loading = true;
        $scope.trackList = function () {
            trackingServices.tracking_trip_listing().then(function (data) {
                console.log('tracking list', data);
                if (data.code.status === 200) {
                    $scope.TrackingTripData = data.trip_data;
                    $scope.loading = false;
                }
                else {
                    alert(data.code.message)
                }
            })
        };

        $scope.trackList();
    }])

    .controller('vehicle.tracking', ['$scope', '$location', '$state', 'trackingServices', '$filter', '$rootScope', 'NgMap', '$timeout', 'MasterService', '$sce',
        function ($scope, $location, $state, trackingServices, $filter, $rootScope, NgMap, $timeout, MasterService, $sce) {


        $scope.getSourceList = function () {
            trackingServices.getSourceList().then(function (response) {
            console.log("Source response ", response)
            $scope.trip_source_type = response.data

            },
            function (err) {
                        swal(err, "Error", "error");
                    })

            }

            $scope.trustSrc = function (src) {
                return $sce.trustAsResourceUrl(src);
            };

            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            $scope.hour = MasterService.hour;

            $scope.sortColumn = '';
            $scope.reverseSort = false;
            $scope.sortDataTracking = function (column) {
                $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
                $scope.sortColumn = column
            };

            $scope.getSortClassTracking = function (column) {
                if ($scope.sortColumn == column) {
                    //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                    return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
                } else {
                    return 'fa fa-sort'
                }
                //return '';
            };
            $scope.locTrackingMap = {};
            NgMap.getMap({ id: 'locTrackingMap' }).then(function (map) {
                $scope.locTrackingMap = map
            });

            NgMap.getMap({ id: 'trackingMap' }).then(function (map) {
                $scope.trackingMap = map
            });

            NgMap.getMap({ id: 'trackingMapNew' }).then(function (map) {
                $scope.trackingMapNew = map
            });

            $('#addLocation').on('show.bs.modal', function (e) {
                $timeout(function () {
                    google.maps.event.trigger($scope.locTrackingMap, 'resize');
                    $scope.locTrackingMap.setCenter({ lat: 20.5937, lng: 78.9629 });
                    $scope.locTrackingMap.setZoom(4);
                });
            });


            $('#myModalTrack').on('show.bs.modal', function (e) {
                $timeout(function () {
                    google.maps.event.trigger($scope.trackingMap, 'resize');
                    $scope.trackingMap.setCenter({ lat: $scope.center_lat, lng: $scope.center_long });
                    //$scope.trackingMap.setZoom = function (number) {
                    //    zoom: 10
                    //};
                    //$scope.trackingMap.setZoom(4);
                });
            });

            //function initMap() {
            //
            //    var map = new google.maps.Map(document.getElementById('trackingMap'), {
            //        center: {lat: -33.8688, lng: 151.2195},
            //        zoom: 13
            //    });
            //    google.maps.event.trigger(map, 'resize');
            //}
            //initMap()
            //function init() {
            //
            //    var map = new google.maps.Map(document.getElementById('trackingMap'), {
            //        zoom: 16
            //    });
            //    google.maps.event.trigger($scope.trackingMap, 'resize');
            //    $scope.trackingMap.setCenter({lat: 20.5937, lng: 78.9629});
            //
            //}
            //
            //$('#myModalTrack').on('shown.bs.modal', function () {
            //     $timeout(function () {
            //    init();
            //    });
            //});

            //$("#myModalTrack").on("shown.bs.modal", function () {
            //    google.maps.event.trigger(map, "resize");
            //    map.setCenter({lat: 20.5937, lng: 78.9629});
            //
            //});

            //Ashish
            // Construct a bdccGeo from its latitude and longitude in degrees
            function bdccGeo(lat, lon) {
                var theta = (lon * Math.PI / 180.0);
                var rlat = bdccGeoGeocentricLatitude(lat * Math.PI / 180.0);
                var c = Math.cos(rlat);
                this.x = c * Math.cos(theta);
                this.y = c * Math.sin(theta);
                this.z = Math.sin(rlat);
            }

            bdccGeo.prototype = new bdccGeo();

            // internal helper functions =========================================

            // Convert from geographic to geocentric latitude (radians).
            function bdccGeoGeocentricLatitude(geographicLatitude) {
                var flattening = 1.0 / 298.257223563;//WGS84
                var f = (1.0 - flattening) * (1.0 - flattening);
                return Math.atan((Math.tan(geographicLatitude) * f));
            }

            // Convert from geocentric to geographic latitude (radians)
            function bdccGeoGeographicLatitude(geocentricLatitude) {
                var flattening = 1.0 / 298.257223563;//WGS84
                var f = (1.0 - flattening) * (1.0 - flattening);
                return Math.atan(Math.tan(geocentricLatitude) / f);
            }

            // Returns the two antipodal points of intersection of two great
            // circles defined by the arcs geo1 to geo2 and
            // geo3 to geo4. Returns a point as a Geo, use .antipode to get the other point
            function bdccGeoGetIntersection(geo1, geo2, geo3, geo4) {
                var geoCross1 = geo1.crossNormalize(geo2);
                var geoCross2 = geo3.crossNormalize(geo4);
                return geoCross1.crossNormalize(geoCross2);
            }

            //from Radians to Meters
            function bdccGeoRadiansToMeters(rad) {
                return rad * 6378137.0; // WGS84 Equatorial Radius in Meters
            }

            // properties =================================================


            bdccGeo.prototype.getLatitudeRadians = function () {
                return (bdccGeoGeographicLatitude(Math.atan2(this.z,
                    Math.sqrt((this.x * this.x) + (this.y * this.y)))));
            };

            bdccGeo.prototype.getLongitudeRadians = function () {
                return (Math.atan2(this.y, this.x));
            };

            // Methods =================================================

            //Maths
            bdccGeo.prototype.dot = function (b) {
                return ((this.x * b.x) + (this.y * b.y) + (this.z * b.z));
            };

            //More Maths
            bdccGeo.prototype.crossLength = function (b) {
                var x = (this.y * b.z) - (this.z * b.y);
                var y = (this.z * b.x) - (this.x * b.z);
                var z = (this.x * b.y) - (this.y * b.x);
                return Math.sqrt((x * x) + (y * y) + (z * z));
            };

            //More Maths
            bdccGeo.prototype.scale = function (s) {
                var r = new bdccGeo(0, 0);
                r.x = this.x * s;
                r.y = this.y * s;
                r.z = this.z * s;
                return r;
            };

            // More Maths
            bdccGeo.prototype.crossNormalize = function (b) {
                var x = (this.y * b.z) - (this.z * b.y);
                var y = (this.z * b.x) - (this.x * b.z);
                var z = (this.x * b.y) - (this.y * b.x);
                var L = Math.sqrt((x * x) + (y * y) + (z * z));
                var r = new bdccGeo(0, 0);
                r.x = x / L;
                r.y = y / L;
                r.z = z / L;
                return r;
            };

            // point on opposite side of the world to this point
            bdccGeo.prototype.antipode = function () {
                return this.scale(-1.0);
            };


            //distance in radians from this point to point v2
            bdccGeo.prototype.distance = function (v2) {
                return Math.atan2(v2.crossLength(this), v2.dot(this));
            };

            //returns in meters the minimum of the perpendicular distance of this point from the line segment geo1-geo2
            //and the distance from this point to the line segment ends in geo1 and geo2
            bdccGeo.prototype.distanceToLineSegMtrs = function (geo1, geo2) {

                //point on unit sphere above origin and normal to plane of geo1,geo2
                //could be either side of the plane
                var p2 = geo1.crossNormalize(geo2);

                // intersection of GC normal to geo1/geo2 passing through p with GC geo1/geo2
                var ip = bdccGeoGetIntersection(geo1, geo2, this, p2);

                //need to check that ip or its antipode is between p1 and p2
                var d = geo1.distance(geo2);
                var d1p = geo1.distance(ip);
                var d2p = geo2.distance(ip);
                //window.status = d + ", " + d1p + ", " + d2p;
                if ((d >= d1p) && (d >= d2p))
                    return bdccGeoRadiansToMeters(this.distance(ip));
                else {
                    ip = ip.antipode();
                    d1p = geo1.distance(ip);
                    d2p = geo2.distance(ip);
                }
                if ((d >= d1p) && (d >= d2p))
                    return bdccGeoRadiansToMeters(this.distance(ip));
                else
                    return bdccGeoRadiansToMeters(Math.min(geo1.distance(this), geo2.distance(this)));
            };

            // distance in meters from GLatLng point to GPolyline or GPolygon poly
            function bdccGeoDistanceToPolyMtrs(poly, point) {
                var d = 999999999;
                var i;
                var p = new bdccGeo(point.lat(), point.lng());
                for (i = 0; i < (poly.getPath().getLength() - 1); i++) {
                    var p1 = poly.getPath().getAt(i);
                    var l1 = new bdccGeo(p1.lat(), p1.lng());
                    var p2 = poly.getPath().getAt(i + 1);
                    var l2 = new bdccGeo(p2.lat(), p2.lng());
                    var dp = p.distanceToLineSegMtrs(l1, l2);
                    if (dp < d)
                        d = dp;
                }
                return d;
            }

            $scope.showMarker = function (location) {
                $timeout(function () {
                    if (location.lat !== '' && location.long !== '') {
                        var rtPoints = [];
                        angular.forEach($scope.points, function (value) {
                            rtPoints.push({ 'lat': value[0], 'lng': value[1] })
                        });
                        var e = new google.maps.LatLng(parseFloat(location['lat']), parseFloat(location['long']));
                        var rtPoly = new google.maps.Polyline({
                            path: rtPoints,
                            strokeColor: "#FFFFFF",
                            strokeWeight: 0,
                            map: $scope.locTrackingMap
                        });
                        var poly_distance = Math.round(bdccGeoDistanceToPolyMtrs(rtPoly, e));
                        console.log('poly_distance', poly_distance);
                        if (poly_distance > 2000) {
                            $scope.location = { 'lat': '', 'lng': '', 'location_address': '' };
                            swal("Off-route", "", "error");
                            return 0;
                        }
                        else {
                            $scope.markerLatLong = location.latLong;
                            $scope.customIcon = {
                                "scaledSize": [30, 30],
                                "url": "../static/apps/common/images/vehicle.png"
                            };
                        }
                    }
                    console.log('$scope.markerLatLong', location)
                }, 500)
            };

            $scope.location = {};
            $scope.locRefresh = true;
            $scope.addLoc = function (trip) {
                console.log('trip_details', trip);
                $scope.locRefresh = false;
                $scope.markerLatLong = '0,0';
                $scope.location = {
                    location_address: '',
                    lat: '',
                    long: ''
                };

                $scope.index = $scope.vehicleTrackingReceived.indexOf(trip);
                $scope.vehicle_no = trip.vehicle_no;
                $scope.trip_id = trip.trip_status.id;

                $scope.loc_wayPoints = [];
                $scope.track_lane = trip.lane;
                $scope.loc_origin = '';
                $scope.loc_destination = '';
                $scope.track_route = trip.route;
                $scope.trip_id = trip.id;
                $scope.newlat = '';
                $scope.newlong = '';
                try {
                    $scope.loc_origin = trip.start_point.location_point.coordinates.coordinates[1] + ',' + trip.start_point.location_point.coordinates.coordinates[0];
                    $scope.loc_destination = trip.destination_point.location_point.coordinates.coordinates[1] + ',' + trip.destination_point.location_point.coordinates.coordinates[0];
                }
                catch (err) {
                    $scope.loc_origin = trip.start_point.location_point.coordinates[1] + ',' + trip.start_point.location_point.coordinates[0];
                    $scope.loc_destination = trip.destination_point.location_point.coordinates[1] + ',' + trip.destination_point.location_point.coordinates[0]
                }
                if ($scope.track_route.origin_start && $scope.track_lane.lane_name) {
                    $scope.track_lane.location.data.forEach(function (item) {
                        $scope.loc_wayPoints.push({
                            location: {
                                lat: item.location_point.coordinates.coordinates[1],
                                lng: item.location_point.coordinates.coordinates[0]
                            }, stopover: true
                        })
                    });
                }
                var ordinates = {
                    'origin': $scope.loc_origin, 'destination': $scope.loc_destination,
                    'waypoints': $scope.loc_wayPoints
                };
                trackingServices.latLngInRoute(ordinates).then(function (response) {
                    console.log('response_ordinates', response);
                    if (response.code.status === 200) {
                        $scope.points = response.decoded_latlng;
                    }
                    else {
                        swal("Oops!", 'No Route', "warning");
                        return 0;
                    }
                });

                window.setTimeout(function () {
                    $scope.$apply();
                }, 100);

                $timeout(function () {
                    $scope.locRefresh = true;
                }, 1);


                google.maps.event.addListener($scope.locTrackingMap, "click", function (e) {
                    $scope.$apply(function () {
                        var rtPoints = [];
                        angular.forEach($scope.points, function (value) {
                            rtPoints.push({ 'lat': value[0], 'lng': value[1] })
                        });
                        var rtPoly = new google.maps.Polyline({
                            path: rtPoints,
                            strokeColor: "#FFFFFF",
                            strokeWeight: 0,
                            map: $scope.locTrackingMap
                        });
                        var poly_distance = Math.round(bdccGeoDistanceToPolyMtrs(rtPoly, e.latLng));
                        console.log('poly_distance', poly_distance);
                        if (poly_distance > 2000) {
                            swal("Off-route", "", "error");
                            return 0;
                        }
                        var latLng = e.latLng;
                        $scope.location = {
                            lat: latLng.lat(),
                            long: latLng.lng(),
                            location_address: ''
                        };
                        $scope.markerLatLong = latLng.lat() + ", " + latLng.lng();
                        $scope.customIcon = {
                            "scaledSize": [30, 30],
                            "url": "../static/apps/common/images/vehicle.png"
                        };
                    });
                });
            };
            $scope.congestion = {};
            $scope.trackRefresh = true;
            $scope.trackTrip = function (trip) {
//            $scope.trip_x_hrs = {};
//            $scope.location = '';
//            $scope.congestion = {};
            $scope.final_eta = '';
            $scope.tat = '';
            $scope.delay = '';
            try{
                console.log("track trip", trip);
                $scope.selectedTrip = trip;
                $scope.tripDataForGPS = trip;
                $scope.driver_no = trip.order_data.driver_no;
                $scope.veh_no = trip.vehicle_no;
                $scope.delay = trip?.delay_status ? trip?.delay_status : 'N/A';
                $scope.trip_x_hrs = {
                    trip_code: trip.trip_code,
                    cust_id: trip.order_data.customer_trip_data.id,
                    origin: trip.start_point.location_name,
                    destination: trip.destination_point.location_name
                };
                $scope.congestion = {
                    route_name: trip.route.route_name,
                    lane_name: trip.lane.lane_name,
                    congested_route: '',
                    alternate_route: '',
                    trip_start_date_time: trip?.trip_start_date_time ? trip?.trip_start_date_time : 'N/A',
                    tat: trip?.tat ? trip?.tat : 'N/A',
                    time: trip?.current_date_stamp ? trip.current_date_stamp.replace(' ', 'T') : ''
                };
                $scope.show_curr_loc = true;
                $scope.trackRefresh = false;
                $scope.track_wayPoints = [];
                $scope.track_origin = '';
                $scope.track_destination = '';
                $scope.center_lat = trip?.current_location?.lat ? parseFloat(trip.current_location.lat ) : '';
                $scope.center_long = trip?.current_location?.long ? parseFloat(trip.current_location.long) : '';
                $scope.addLatLong = $scope.center_lat + ',' + $scope.center_long;
                $scope.current_location = trip?.current_location?.location ? trip?.current_location?.location : 'N/A';
                var d = new Date();
                var d_tat = new Date(trip.trip_start_date_time);
                var eta_minutes = (parseInt(trip.final_eta.split('hr')) * 60) + parseInt(trip.final_eta.split('hr')[1].split('min')[0].trim());
                d.setMinutes(d.getMinutes() + eta_minutes);
                var date = d.getDate();
                var month = d.getMonth();
                var year = d.getFullYear();
                var timestamp = d.toLocaleTimeString();
                var dateString = String(date + "-" + (month + 1) + "-" + year + ' ' + timestamp);
                $scope.final_eta = dateString;
                d_tat.setMinutes(d_tat.getMinutes() + parseInt(trip.tat) * 60);
                date = d_tat.getDate();
                month = d_tat.getMonth();
                year = d_tat.getFullYear();
                timestamp = d_tat.toLocaleTimeString();
                dateString = String(date + "-" + (month + 1) + "-" + year + ' ' + timestamp);
                $scope.tat = dateString;

                try {
                    $scope.track_origin = trip.start_point.location_point.coordinates.coordinates[1] + ',' + trip.start_point.location_point.coordinates.coordinates[0];
                    $scope.track_destination = trip.destination_point.location_point.coordinates.coordinates[1] + ',' + trip.destination_point.location_point.coordinates.coordinates[0];

                }
                catch (err) {
                    $scope.track_origin = trip.start_point.location_point.coordinates[1] + ',' + trip.start_point.location_point.coordinates[0];
                    $scope.track_destination = trip.destination_point.location_point.coordinates[1] + ',' + trip.destination_point.location_point.coordinates[0];

                }
                $scope.customIcon = {
                    "scaledSize": [30, 30],
                    "url": "../static/apps/common/images/vehicle.png"
                };

                $timeout(function () {
                    $scope.trackRefresh = true;
                }, 1);
                trackingServices.last_x_dist($scope.trip_x_hrs).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.distance_last_x_hrs = response.data.distance_last_x_hrs;
                        for (var i = 0; i < response.data.lat_lng.length; i++) {
                            $scope.track_wayPoints.push({
                                location: {
                                    lat: parseFloat(response.data.lat_lng[i].split(',')[0]),
                                    lng: parseFloat(response.data.lat_lng[i].split(',')[1])
                                }, stopover: true
                            })
                        }
                    }
                    else {
                        swal("Oops!", 'No Route', "warning");
                        return 0;
                    }
                });
                }
                catch(err){
                console.log("error", err)

                }
            };

            $scope.trackingApi  = null;
            $scope.consentStatus = null;

            $scope.getTrackingDetails = function(trip) {
                const trackingDataParams = {
                    trip_id: trip.id,
                    vehicle_no: trip.vehicle_no
                };
                $scope.trackingApi  = null;
                $scope.consentStatus = null;
                trackingServices.getVehicleTrackingDetails(trackingDataParams).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.trackingApi = response.data.results.tracking_info.tracking_api;
                        $scope.consentStatus = response.data.results.tracking_info.consent_status;
                    } else {
                        swal("Oops !", "Error Fetching Tracking Info");
                    }
                }, function (error) {
                    swal('Error Occurred');
                })
            }

            $scope.getSimTrackingStatus = function(){
                return (!$scope.trackingApi && $scope.consentStatus === 'Consented') ? 'Enabled' : 'Not Enabled';
            }

            $scope.getDriverConsent = function(trip){
                const consentParams = {
                    trip_id: trip.id,
                    driver_no: trip.order_data.driver_no
                };
                trackingServices.getDriverConsentForSimTracking(consentParams).then(function (response) {
                    if (response.data.code.status === 200) {
                        swal('Message sent to driver for consent');
                    } else {
                        swal("Oops !", response.data.code.message);
                    }
                }, function (error) {
                    swal('Error Occurred');
                })
            }

            $scope.vehicleHistoryPings = [];
            $scope.getHistoryPingViews = function(vehicle_no){
                $scope.vehicleHistoryPings = [];
                let historyPingParams = {
                    vehicle_number: vehicle_no,
                    trip_code: $scope.trip_x_hrs.trip_code
                }
                trackingServices.getHistoryPings(historyPingParams).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.vehicleHistoryPings = response.data.data?.history ? response.data.data.history : [];
                    } else {
                        swal("Oops !", "Error Fetching Pings Info", response.data.code.message);
                    }
                }, function (error) {
                    console.log('Error in history pings', error)
                    swal('Error Occurred');
                })
            }

            // get Route List Based On Lane
            $scope.getRouteDetails = function (routeCode) {
                console.log("called", routeCode);
                $scope.origin = '';
                $scope.destination = '';
                $scope.mapRoutePoints = [];
                $scope.petrolPumpList = [];
                $scope.routePlacesList = [];
                routeCode && trackingServices.getRouteDetails(routeCode).then(function (response) {
                    if (response.data.details.polyline && response.data.details.polyline.length) {
                        let polylineData = response.data.details.polyline;
                        let lastItem = response.data.details.polyline.length - 1;
                        $scope.origin = polylineData[0][0] + "," + polylineData[0][1];
                        $scope.destination = polylineData[lastItem][0] + "," + polylineData[lastItem][1];
                        $scope.mapRoutePoints = polylineData;
                    }
                }, function (err) {
                    console.log(err);
                    alert('Error Occurred')
                })
            }

            // get Route List Based On Lane
            // 'ROUTE-1597-415089-163'
            $scope.getRoutePlacesDetails = function (routeCode) {
                console.log("called", routeCode);
                $scope.petrolPumpList = [];
                $scope.routePlacesList = [];
                routeCode && trackingServices.getRoutePlacesDetails(routeCode).then(function (response) {
                    if (response.data.details.places && response.data.details.places.length) {
                        $scope.routePlacesList = response.data.details.places;
                    }
                }, function (err) {
                    console.log(err);
                    alert('Error Occurred')
                })
            }

            $scope.setPetrolPumpList = function () {
                $scope.petrolPumpList = $scope.routePlacesList && $scope.routePlacesList.length && $scope.routePlacesList
                    .filter((element) => element.placeType === 'gas_station').map((element) => (
                        {
                            location: {
                                lat: element.latLng.split('|')[0],
                                lng: element.latLng.split('|')[1]
                            }, stopover: true,
                            icon: '../static/apps/common/images/fuel-icon2.png',
                        }));
            }

            $scope.setTollPlazaList = function () {
                $scope.petrolPumpList = $scope.routePlacesList && $scope.routePlacesList.length && $scope.routePlacesList
                    .filter((element) => element.placeType === 'toll').map((element) => ({
                        location: {
                            lat: element.latLng.split('|')[0],
                            lng: element.latLng.split('|')[1]
                        }, stopover: true,
                        icon: '../static/apps/common/images/toll-icon2.png',
                    }));
            }

            $scope.clearMarker = function () {
                $scope.petrolPumpList = [];
            }

            $scope.show_curr_loc = true;
            $scope.show_current_point = function (show) {
                if (show === 'show') {
                    $scope.show_curr_loc = false;
                } else {
                    $scope.show_curr_loc = true;
                }

            };


            $scope.showDriverGpsLocation = function (trip) {
                $scope.driver_consent = { 'consent_status': trip.consent_status }
                $scope.gps_data = {};
                try {
                    console.log("trip---->", trip);
                    $scope.show_curr_loc = true;
                    $scope.trackRefresh = false;
                    $scope.track_wayPoints = [];
                    $scope.track_origin = '';
                    $scope.track_destination = '';
                    $scope.center_lat = parseFloat(trip.current_location.lat);
                    $scope.center_long = parseFloat(trip.current_location.long);
                    $scope.location = trip.current_location.location;
                    var d = new Date();
                    var d_tat = new Date(trip.trip_start_date_time);
                    var eta_minutes = (parseInt(trip.final_eta.split('hr')) * 60) + parseInt(trip.final_eta.split('hr')[1].split('min')[0].trim());
                    d.setMinutes(d.getMinutes() + eta_minutes);
                    var date = d.getDate();
                    var month = d.getMonth();
                    var year = d.getFullYear();
                    var timestamp = d.toLocaleTimeString();
                    var dateString = String(date + "-" + (month + 1) + "-" + year + ' ' + timestamp);
                    $scope.final_eta = dateString;
                    d_tat.setMinutes(d_tat.getMinutes() + parseInt(trip.tat) * 60);
                    date = d_tat.getDate();
                    month = d_tat.getMonth();
                    year = d_tat.getFullYear();
                    timestamp = d_tat.toLocaleTimeString();
                    dateString = String(date + "-" + (month + 1) + "-" + year + ' ' + timestamp);
                    $scope.tat = dateString;
                    $scope.veh_no = trip.vehicle_no;
                    $scope.delay = trip.delay_status;
                    $scope.congestion = {
                        route_name: trip.route.route_name,
                        lane_name: trip.lane.lane_name,
                        congested_route: '',
                        alternate_route: '',
                        time: trip.current_date_stamp.replace(' ', 'T'),
                        trip_start_date_time: trip.trip_start_date_time,
                        tat: trip.tat
                    };

                    try {
                        $scope.track_origin = trip.start_point.location_point.coordinates.coordinates[1] + ',' + trip.start_point.location_point.coordinates.coordinates[0];
                        $scope.track_destination = trip.destination_point.location_point.coordinates.coordinates[1] + ',' + trip.destination_point.location_point.coordinates.coordinates[0];

                    }
                    catch (err) {
                        $scope.track_origin = trip.start_point.location_point.coordinates[1] + ',' + trip.start_point.location_point.coordinates[0];
                        $scope.track_destination = trip.destination_point.location_point.coordinates[1] + ',' + trip.destination_point.location_point.coordinates[0];

                    }
                    $scope.customIcon = {
                        "scaledSize": [30, 30],
                        "url": "../static/apps/common/images/vehicle.png"
                    };

                    $timeout(function () {
                        $scope.trackRefresh = true;
                    }, 1);
                }
                catch (err) {
                    console.log(err)
                }

                trackingServices.getSimBasedTrackingData(trip.id).then(function (response) {
                    console.log('getSimBasedTrackingData', response);
                    if (response.code.status === 200) {
                        $('#tracking_driver_sim_location').modal('show');
                        // $('#gpsData').modal('show');
                        if (response.data) {
                            $scope.gps_data = response.data;
                            $scope.gps_lat = response.data.current_location.lat;
                            $scope.gps_long = response.data.current_location.long;
                        }
                    }
                    else {
                        swal("Oops!", response.code.message, "warning");
                        return 0;
                    }
                });
            };

            $scope.send_w_m = function (m_no) {
                var data = {};
                data.veh_no = $scope.veh_no;
                data.trip_x_hrs = $scope.trip_x_hrs;
                data.congestion = $scope.congestion;
                data.tat = $scope.tat;
                data.location = $scope.location;
                data.delay = $scope.delay;
                data.final_eta = $scope.final_eta;
                data.m_no = m_no;
                console.log('THE_DATA', data);
                trackingServices.send_w_message(data).then(function (response) {
                    if (response.data.code.status === 200) {
                        swal("Message sent", '', "success")
                    } else if (response.data.code.status === 300) {
                        swal("Cancelled", 'Enter Valid number!', "error")
                    } else if (response.data.code.status === 400) {
                        swal("Cancelled", response.data.code.message, "error")
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            };

            $scope.current_lat_lng = function (lat, lng) {
                console.log('latlng', lat, lng);
                $scope.lat = lat;
                $scope.lng = lng;
                $scope.customIcon = {
                    "scaledSize": [40, 40],
                    "url": "../static/apps/common/images/vehicle.png"
                };
            };

            $scope.add_loc = function (location) {
                console.log('location', location);
                trackingServices.addLocation(location.lat, location.long, $scope.trip_id, $scope.vehicle_no).then(function (response) {
                    if (response.data.code.status === 200) {
                        console.log('response', response);
                        $scope.locationAddress = response.data.location;
                        $scope.vehicleTrackingReceived[$scope.index].current_location = response.data.location;
                        $scope.vehicleTrackingReceived[$scope.index].current_date_stamp = response.data.date.split('.')[0];
                        $scope.current_location_lat = +($scope.vehicleTrackingReceived[$scope.index].current_location.lat);
                        $scope.current_location_long = +($scope.vehicleTrackingReceived[$scope.index].current_location.long);
                        $scope.origin = { lat: $scope.current_location_lat, lng: $scope.current_location_long };
                        $scope.current_eta_lat = $scope.vehicleTrackingReceived[$scope.index].current_eta_destination[1];
                        $scope.current_eta_long = $scope.vehicleTrackingReceived[$scope.index].current_eta_destination[0];
                        $scope.destination = { lat: $scope.current_eta_lat, lng: $scope.current_eta_long };
                        var service = new google.maps.DistanceMatrixService;
                        service.getDistanceMatrix({
                            origins: [$scope.origin],
                            destinations: [$scope.destination],
                            travelMode: 'DRIVING',
                            unitSystem: google.maps.UnitSystem.METRIC,
                            avoidHighways: false,
                            avoidTolls: false
                        }, buildResponseFor($scope.index));
                    } else if (response.data.code.status === 202) {
                        swal("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            /**
             * updateLocationByVehicleReporting
             * Prafull
             */

            $scope.updateLocationByVehicleReporting = function (location) {
                console.log('location', location);
                trackingServices.addLocation(location.lat, location.long, $scope.trip_id, $scope.vehicle_no).then(function (response) {
                    if (response.data.code.status === 200) {
                        console.log('response', response)
                        $scope.locationAddress = response.data.location;
                        $scope.vehicleTrackingReceived[$scope.index].current_location = response.data.location;
                        $scope.vehicleTrackingReceived[$scope.index].current_date_stamp = response.data.date.split('.')[0];

                        console.log('$scope.vehicleTracking.current_location.location', $scope.vehicleTrackingReceived[$scope.index].current_location.location);

                        $scope.current_location_lat = +($scope.vehicleTrackingReceived[$scope.index].current_location.lat);
                        $scope.current_location_long = +($scope.vehicleTrackingReceived[$scope.index].current_location.long);
                        $scope.origin = { lat: $scope.current_location_lat, lng: $scope.current_location_long };
                        console.log('$scope.vehicleTracking', $scope.vehicleTracking);
                        console.log('$scope.origin', $scope.origin);
                        $scope.current_eta_lat = $scope.vehicleTrackingReceived[$scope.index].current_eta_destination[1];
                        $scope.current_eta_long = $scope.vehicleTrackingReceived[$scope.index].current_eta_destination[0];
                        $scope.destination = { lat: $scope.current_eta_lat, lng: $scope.current_eta_long };
                        console.log('$scope.destination', $scope.destination);
                        var service = new google.maps.DistanceMatrixService;
                        service.getDistanceMatrix({

                            origins: [$scope.origin],
                            destinations: [$scope.destination],
                            travelMode: 'DRIVING',
                            unitSystem: google.maps.UnitSystem.METRIC,
                            avoidHighways: false,
                            avoidTolls: false
                        }, buildResponseFor($scope.index));
                        //$scope.vehicleTrackingData();
                    } else if (response.data.code.status === 202) {
                        swal("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            $scope.showLrNumberPicture = function (lr_num_pic) {
                $scope.showLrNumPic = null;
                if (!lr_num_pic) {
                    setTimeout(function () {
                        $('#lr_num_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(lr_num_pic)) {
                    $scope.showLrNumPic = lr_num_pic;
                    return;
                }

                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        if (response.data.code.status === 200) {
                            $scope.showLrNumPic = response.data.url
                        } else {
                            $('#lr_num_pic').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                }
                else {
                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }

            };

            $scope.isFileFromGoogleStorage = function (file) {
                return file.includes('https://storage.googleapis.com');
            };
        
            $scope.isPdf = function (url) {
                return url && url.toLowerCase().includes(".pdf");
            };

            /**
             * functionName:vehicleTrackingData
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 21/07/2017
             * testerName:
             * testDate:
             */

            $scope.getCustomerListNewTrack = function () {
                $scope.customerName = [];
                trackingServices.getAllCustomerListsNew().then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.customerName = response.data.customer;
                    } else {
                        if (response.data.code.status === 100) {
                            angular.forEach(response.data.customer, function (value, key) {
                                $scope.customerName.push(value._source)
                            });
                        }
                        else {
                            swal("Oops", response.data.code.message, "error");
                        }
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', "error")
                })
            };

            $scope.oDBasedVehNo = function (customer) {
                //console.log('filterdata', $scope.filterData);
                var newArr = [];
                if ($scope.filterData.origin && $scope.filterData.destination) {
                    angular.forEach($scope.vehnox, function (value, key) {
                        if ((angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) &&
                            (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name))) {
                            newArr.push(value);
                        }
                    });
                }
                else if ($scope.filterData.origin) {
                    angular.forEach($scope.vehnox, function (value, key) {
                        if (angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) {
                            newArr.push(value);
                        }
                    });

                }
                else if ($scope.filterData.destination) {
                    angular.forEach($scope.vehnox, function (value, key) {
                        if (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name)) {
                            newArr.push(value);
                        }
                    });
                }
                else if (!$scope.filterData.origin && !$scope.filterData.destination) {
                    $scope.filterData = {
                        vehicle_type: '',
                        customer: customer,
                        origin: '',
                        destination: '',
                        broker: '',
                        placement_date: '',
                        to_placement_date: '',
                        s_from_date: '',
                        s_to_date: ''

                    };
                    $scope.vehicleNo = [];
                    angular.forEach($scope.vehnox, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                    exists = true
                                }
                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        }
                        else {
                            newArr.push(value);
                        }
                    });
                }
                $scope.vehicleNo = newArr;
                //console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
            };

            $scope.checkIfCustTrack = function (data) {
                if (data) {
                }
                else {
                    swal("Oops", 'Please select customer First.', "warning")
                }
            };

            $scope.destinationBasedOriginTrack = function (data) {
                $scope.destination = [];
                if (data === undefined || data === null) {
                    $scope.filterData.destination = null;
                }
                if (data !== undefined && data !== null) {
                    angular.forEach($scope.y, function (value, key) {
                        if (data.location_name === value.origin_data.location_name) {
                            $scope.destination.push({ 'location_name': value.destination_data.location_name })
                        }
                    });
                }
                else {
                    angular.forEach($scope.y, function (value, key) {
                        $scope.destination.push({ 'location_name': value.destination_data.location_name });
                    });
                }
                var newArr = [];
                angular.forEach($scope.destination, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value.location_name, val2.location_name)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    }
                    else {
                        newArr.push(value);
                    }

                });
                $scope.destination = newArr;
            };

            $scope.destinationLocationTrack = function (data, data_1) {

                if (data === undefined || data === null) {
                    $scope.filterData.origin = null;
                    $scope.origin = [];
                    if (data_1 !== undefined && data_1 !== null) {
                        angular.forEach($scope.y, function (value, key) {
                            if (data_1.location_name === value.destination_data.location_name) {
                                $scope.origin.push({ 'location_name': value.origin_data.location_name })
                            }
                        });
                    }

                    if (data_1 === undefined || data_1 == null) {
                        $scope.filterData.origin = null;
                        angular.forEach($scope.y, function (value, key) {
                            $scope.origin.push({ 'location_name': value.origin_data.location_name });
                        });
                    }
                    var newArr = [];
                    angular.forEach($scope.origin, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value.location_name, val2.location_name)) {
                                    exists = true
                                }
                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        }
                        else {
                            newArr.push(value);
                        }
                    });

                    $scope.origin = newArr;
                }
                else {
                    if (data_1 === undefined || data_1 == null) {
                        $scope.origin = [];
                        $scope.filterData.origin = null;
                        angular.forEach($scope.y, function (value, key) {
                            $scope.origin.push({ 'location_name': value.origin_data.location_name });
                        });
                        var newArr = [];
                        angular.forEach($scope.origin, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value.location_name, val2.location_name)) {
                                        exists = true
                                    }

                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            }
                            else {
                                newArr.push(value);
                            }

                        });
                        $scope.origin = newArr;
                        $scope.destination = [];
                        $scope.filterData.destination = null;
                        angular.forEach($scope.y, function (value, key) {
                            $scope.destination.push({ 'location_name': value.destination_data.location_name });
                        });
                        var newArr = [];
                        angular.forEach($scope.destination, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value.location_name, val2.location_name)) {
                                        exists = true
                                    }

                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            }
                            else {
                                newArr.push(value);
                            }
                        });
                        $scope.destination = newArr;
                    }
                }
            };


            $scope.getVehNoNewTrack = function (customer) {
                console.log('cust', customer);
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    placement_date: '',
                    to_placement_date: '',
                    s_from_date: '',
                    s_to_date: '',
                    vehicle_no: ''
                };
                $scope.vehicleNo = [];
                var id = null;
                if (customer) {
                    id = customer.id;
                }
                trackingServices.getAllVehNoListNew(id).then(function (response) {
                    console.log('response_vehno', response);
                    if (response.data.code.status === 100) {
                        $scope.vehicleNo = response.data.data_list;
                        if (customer) {
                            var newArr = [];
                            $scope.vehnox = response.data.data_list;
                            angular.forEach($scope.vehicleNo, function (value, key) {
                                var exists = false;
                                if (newArr.length) {
                                    angular.forEach(newArr, function (val2, key) {
                                        if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                            exists = true
                                        }
                                    });
                                    if (exists === false) {
                                        newArr.push(value);
                                    }
                                }
                                else {
                                    newArr.push(value);
                                }
                            });
                            $scope.vehicleNo = newArr;
                        }
                        console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });
            };

            $scope.getLocationNewTrack = function (customerName) {
                $scope.filterData.origin = null;
                $scope.filterData.destination = null;
                $scope.origin = [];
                $scope.destination = [];

                trackingServices.getLocationListsNew(customerName).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.locationNmae = response.data.location;
                    } else {
                        if (response.data.code.status === 100) {
                            $scope.y = response.data.location;
                            angular.forEach(response.data.com.origin_data, function (value, key) {
                                $scope.origin.push({ 'location_name': value });
                            });
                            angular.forEach(response.data.com.destination_data, function (value, key) {
                                $scope.destination.push({ 'location_name': value });
                            });
                        }
                        else {
                            swal("Oops", response.data.code.message, "error");
                        }
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            };

            $scope.tracking_detail = function (vehicleTracking) {
                $rootScope.vehicleTracking = vehicleTracking
            };


            $scope.vehicleTrackingData = function (count) {
                $scope.asyncRequestForFilter();
                $scope.completedTripPage = {};
                $scope.loading = true;
                $scope.lat = 22;
                $scope.lng = 87;
                trackingServices.vehicle_tracking(count).then(function (response) {
                    $scope.loading = false;
                    console.log('tracking_data_response', response);
                    if (response.results.code.status === 200) {
                        $scope.vehicleTrackingReceived = response.results.track_data;
                        // console.log("vehicleTrackingReceived", $scope.vehicleTrackingReceived);
                        $scope.wf = response.results.wf;
                        $scope.completedTripPage.count = response.count;
                        // alert("Hi I am here nhi hu bhai");

                        // console.log('hey do operation here', $scope.vehicleTrackingReceived);
                        for (var i = 0; i < $scope.vehicleTrackingReceived.length; i++) {
                            $scope.vehicleTracking = $scope.vehicleTrackingReceived[i];
                            if ($scope.vehicleTracking.current_date_stamp != null) {
                                $scope.vehicleTrackingReceived[i].current_date_stamp = $scope.vehicleTracking.current_date_stamp.split('.')[0];
                            }


                            // Now get google route
                            // $scope.vehicleTracking.google_route.route;
                            // console.log($scope.vehicleTracking.new_o_data.new_o_route);
                            if ($scope.vehicleTracking.new_o_data.new_o_route.length) {
                                // if ($scope.vehicleTracking.vehicle_no === "NL01AD1836"){
                                //     alert($scope.vehicleTracking.vehicle_no)
                                // }
                                // alert("fsdfsdf")
                                // console.log( $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0],  $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1])
                                var current_to_destination_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].distance.value;

                                $scope.vehicleTracking.eta_data.current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].duration.value;
                                // $scope.vehicleTracking.eta_data.current_eta = current_to_destination_eta

                                //    Now origin to current location
                                try {
                                    var origin_to_current_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].distance.value;
                                }
                                catch (err) {
                                    origin_to_current_distnace = 0
                                }
                                try {

                                    var origin_to_current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].duration.value;
                                }
                                catch (err) {
                                    origin_to_current_eta = 0
                                }

                                $scope.vehicleTracking.eta_data.distance_total = origin_to_current_distnace + current_to_destination_distnace;
                                $scope.vehicleTracking.eta_data.current_distance = current_to_destination_distnace;

                                // $scope.vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);
                                $scope.vehicleTracking.trip_complete = (($scope.vehicleTracking.eta_data.distance_total - $scope.vehicleTracking.eta_data.current_distance) / ($scope.vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                                //var time_now = new Date();
                                //var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                                //var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;
                                var trip_duration = $scope.vehicleTracking.trip_duration;
                                $scope.vehicleTrackingReceived[i].trip_complete = $scope.vehicleTracking.trip_complete;
                                //Ashish
                                var time_complete_percentage = (trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);

                                if (time_complete_percentage > 100) {
                                    time_complete_percentage = 100;
                                }


                                if ($scope.vehicleTrackingReceived[i].trip_complete < 0) {
                                    $scope.vehicleTrackingReceived[i].trip_complete = 0;
                                }


                                // $scope.$apply(function () {
                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage;
                                // });

                                if ($scope.vehicleTracking.ideal_time !== '') {
                                    $scope.vehicleTracking.final_delay = trip_duration + $scope.vehicleTracking.ideal_eta - $scope.vehicleTracking.ideal_tat;
                                    $scope.vehicleTracking.final_eta = $scope.vehicleTracking.ideal_eta
                                }
                                else {
                                    var etaRemaining = ((100 - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;

                                    if (etaRemaining > $scope.vehicleTracking.eta_data.current_eta) {
                                        $scope.vehicleTracking.final_eta = etaRemaining;
                                    }

                                    else {
                                        $scope.vehicleTracking.final_eta = $scope.vehicleTracking.eta_data.current_eta;
                                    }


                                    $scope.vehicleTracking.final_delay = (
                                        (time_complete_percentage - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;
                                }

                                if (time_complete_percentage === 100 && $scope.vehicleTracking.trip_complete !== 100) {
                                    var x = trip_duration - parseFloat($scope.vehicleTracking.tat) * 3600;
                                    if (x > 0) {
                                        $scope.vehicleTracking.final_delay = x + $scope.vehicleTracking.final_eta;
                                    }
                                    else {
                                        $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_eta;
                                    }
                                }

                                if ($scope.vehicleTracking.final_eta > 0 && isFinite($scope.vehicleTracking.final_eta)) {
                                    var final_eta_hour = Math.floor($scope.vehicleTracking.final_eta / 3600);
                                    var final_eta_minute = Math.floor(($scope.vehicleTracking.final_eta % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].final_eta = final_eta_hour + "hr "
                                        + final_eta_minute + "min"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].final_eta = 0
                                }

                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null)
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;

                                var total_stoppage = 0;
                                var total_stoppage_previous = 0;
                                angular.forEach($scope.vehicleTracking.order_data.multiple_stopage, function (value) {
                                    total_stoppage_previous = total_stoppage;
                                    try {
                                        if (value.destination_point.stop_time !== undefined) {
                                            console.log("stop_time: ", $scope.vehicleTracking.trip_code, " ", value.destination_point.stop_time)
                                            total_stoppage += value.destination_point.stop_time;
                                        }
                                    }
                                    catch (err) {
                                        total_stoppage = total_stoppage_previous;
                                    }
                                });

                                if (isFinite(total_stoppage)) {
                                    $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_delay - (total_stoppage * 60);
                                }

                                if ($scope.vehicleTracking.final_delay > 300 && isFinite($scope.vehicleTracking.final_delay)) {
                                    var final_delay_hour = Math.floor($scope.vehicleTracking.final_delay / 3600);
                                    var final_delay_minute = Math.floor(($scope.vehicleTracking.final_delay % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].delay_status = final_delay_hour + "hr "
                                        + final_delay_minute + "min"
                                }
                                else if ($scope.vehicleTracking.trip_complete === 100) {
                                    $scope.vehicleTrackingReceived[i].delay_status = "Trip Completed"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].delay_status = "No Delay"
                                }


                            }




                            // if ($scope.vehicleTracking.current_location) {
                            //     $scope.current_location_long = +($scope.vehicleTracking.current_location.long);
                            //     $scope.current_location_lat = +($scope.vehicleTracking.current_location.lat);
                            //     $scope.current_eta_lat = $scope.vehicleTracking.current_eta_destination[1];
                            //     $scope.current_eta_long = $scope.vehicleTracking.current_eta_destination[0];
                            //     try {
                            //         $scope.origin = [{
                            //             lat: $scope.current_location_lat,
                            //             lng: $scope.current_location_long
                            //         }];
                            //         $scope.destination = [{lat: $scope.current_eta_lat, lng: $scope.current_eta_long}];
                            //     }
                            //     catch (err) {
                            //         console.log(err)
                            //     }
                            //
                            //
                            //     var service = new google.maps.DistanceMatrixService;
                            //     service.getDistanceMatrix({
                            //
                            //         origins: $scope.origin,
                            //         destinations: $scope.destination,
                            //         travelMode: 'DRIVING',
                            //         unitSystem: google.maps.UnitSystem.METRIC,
                            //         avoidHighways: false,
                            //         avoidTolls: false
                            //     }, buildResponseFor(i, $scope.vehicleTracking.trip_duration))
                            // }
                            else {
                                $scope.vehicleTrackingReceived[i].delay_status = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].trip_complete = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].time_complete = "Location Not Available";
                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null)
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;
                                else
                                    $scope.vehicleTrackingReceived[i].final_eta = "Location Not Available";
                                // var trip_duration = $scope.vehicleTracking.trip_duration;
                                // $scope.vehicleTrackingReceived[i].trip_complete = $scope.vehicleTracking.trip_complete;
                                //Ashish
                                var time_complete_percentage1 = ($scope.vehicleTracking.trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);
                                // var time_now1 = new Date();
                                // var trip_start_date_time1 = new Date($scope.vehicleTrackingReceived[i].trip_start_date_time + '+05:30');
                                // var trip_duration1 = ((+time_now1) - (+trip_start_date_time1)) / 1000;
                                // var time_complete_percentage1 = (trip_duration1 / ($scope.vehicleTrackingReceived[i].tat * 36)).toFixed(0);
                                if (time_complete_percentage1 > 100) {
                                    time_complete_percentage1 = 100;
                                }

                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage1;
                            }
                        }
                    }
                    else if (response.results.code.status === 500) {
                        $scope.loading = false;

                    }

                })

            };

            $scope.predicate = function (vehicleTracking) {

                if (vehicleTracking.delay_status === "Location Not Available") {
                    return parseFloat("3")
                }
                if (vehicleTracking.delay_status === "No Delay") {
                    return parseFloat("2")
                }
                if (vehicleTracking.delay_status === "Trip Completed") {
                    return parseFloat("1")
                }
                try {
                    var data_ar = vehicleTracking.delay_status.split("hr");
                    var hr_seconds = parseFloat(data_ar[0]);
                    var min_sec = parseFloat(data_ar[1].split('min')[0]);
                    return hr_seconds * 60 * 60 + min_sec * 60;
                    // console.log(min_sec)
                }
                catch (error) {
                    return parseFloat("0")
                }


            };

            $scope.dataBindD = function (data_0, data_1, data_2, data_3, data_4) {
                var add = ' ---> ';

                angular.forEach(data_2, function (value, key) {
                    // console.log()
                    add += value.destination_point.location_name + ' ( ' + value.destination_point.location_code + ' ) ' + ' ---> '

                });
                return data_0 + ' ( ' + data_1 + ' ) ' + add + data_3 + ' ( ' + data_4 + ' ) '

            };$scope.dataBindDB = function (data_0, data_1) {

                return data_0 + ' ( ' + data_1 + ' )'

            };


            function secondsToString(seconds) {

                if (seconds != null) {
                    var numdays = Math.floor(seconds / 86400);
                    var numhours = Math.floor((seconds % 86400) / 3600);
                    var numminutes = Math.floor(((seconds % 86400) % 3600) / 60);
                    //var numseconds = ((seconds % 86400) % 3600) % 60;
                    if (numhours == 0) {
                        return numminutes + " minutes ";
                    }
                    if (numdays == 0) {
                        return numhours + " hours " + numminutes + " minutes ";
                    }
                    if (numminutes == 0) {
                        return numdays + " days " + numhours + " hours ";
                    }
                    //if(numseconds == 0){
                    //     return numminutes + " minutes " + numseconds + " seconds ";
                    //}

                    return numdays + " days " + numhours + " hours " + numminutes + " minutes ";
                }
            }

            $scope.getVehNoNewAcc = function (customer) {
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    placement_date: '',
                    to_placement_date: '',
                    s_from_date: '',
                    s_to_date: '',
                    vehicle_no: ''
                };
                $scope.vehicleNo = [];
                var id = null;
                if (customer) {
                    id = customer.id;
                }
                trackingServices.getAllVehNoListNew(id).then(function (response) {
                    if (response.data.code.status === 100) {
                        $scope.vehicleNo = response.data.data_list;
                        if (customer) {
                            var newArr = [];
                            $scope.vehnox = response.data.data_list;
                            angular.forEach($scope.vehicleNo, function (value, key) {
                                var exists = false;
                                if (newArr.length) {
                                    angular.forEach(newArr, function (val2, key) {
                                        if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                            exists = true
                                        }
                                    });
                                    if (exists === false) {
                                        newArr.push(value);
                                    }
                                }
                                else {
                                    newArr.push(value);
                                }
                            });
                            $scope.vehicleNo = newArr;
                        }
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });
            };

            $scope.originLoading = false;
            $scope.destinationLoading = false;
            $scope.originDebounceTimer = null;
            $scope.destinationDebounceTimer = null;

            $scope.fetchOriginLocation = function (originStr) {
                debugger;
                if (!$scope.filterData.customer?.id) {
                    swal("Oops", 'Please select customer First.', "warning")
                    return Promise.resolve([]);
                }

                if (!originStr || originStr.length < 2) {
                    return Promise.resolve([]);
                }

                if ($scope.originDebounceTimer) {
                    $timeout.cancel($scope.originDebounceTimer);
                }

                // Return a promise that resolves with the data
                return new Promise(function (resolve) {
                    $scope.originDebounceTimer = $timeout(function () {
                        $scope.originLoading = true;
                        trackingServices.fetchCustomerLocation($scope.filterData.customer?.id, originStr, '')
                            .then(function (response) {
                                $scope.originLoading = false;
                                if (response.data.code.status === 200) {
                                    resolve(response.data?.com?.origin_data || []);
                                } else {
                                    resolve([]);
                                }
                            })
                            .catch(function (error) {
                                $scope.originLoading = false;
                                console.error('Origin API error:', error);
                                resolve([]);
                            });
                    }, 500);
                });
            };

            $scope.fetchDestinationLocation = function (destStr) {
                if (!$scope.filterData.customer?.id) {
                    swal("Oops", 'Please select customer First.', "warning")
                    return Promise.resolve([]);
                }

                if (!destStr || destStr.length < 2) {
                    return Promise.resolve([]);

                }

                if ($scope.destinationDebounceTimer) {
                    $timeout.cancel($scope.destinationDebounceTimer);
                }

                return new Promise(function (resolve) {
                    $scope.destinationDebounceTimer = $timeout(function () {
                        $scope.destinationLoading = true;
                        trackingServices.fetchCustomerLocation($scope.filterData.customer?.id, '', destStr)
                            .then(function (response) {
                                $scope.destinationLoading = false;
                                if (response.data.code.status === 200) {
                                    resolve(response.data?.com?.destination_data || []);
                                } else {
                                    resolve([]);
                                }
                            })
                            .catch(function (error) {
                                $scope.destinationLoading = false;
                                console.error('Destination API error:', error);
                                resolve([]);
                            });
                    }, 500);
                });
            };

            //Ashish
            $scope.exportTrackingData = function (filterData) {
                $scope.csvloading = true;
                $scope.vehicle_no = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';
                $scope.lr_no = '';
                $scope.broker_id = '';
                $scope.customer_code = '';
                $scope.lane_type = '';
                $scope.delay = '';
                $scope.source_name = '';

                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;

                if (filterData.source_name)
                    $scope.source_name = filterData.source_name.name;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id;

                if (filterData.lane_type)
                    $scope.lane_type = filterData.lane_type;

                if (filterData.delay)
                    $scope.delay = filterData.delay;

                $scope.filterDataObject = {
                    customer: $scope.customer_code,
                    vehicle_no: $scope.vehicle_no,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required,
                    lr: $scope.lr_no,
                    lane_type: $scope.lane_type,
                    broker: $scope.broker_id,
                    delay: $scope.delay,
                    source_name: $scope.source_name
                };

                const generateVehicleTrackingCsv = function (trackingData) {
                    $scope.csvloading = false;
                    $scope.vehicleTrackingExportRecieved = trackingData;
                    $scope.downloadPaymentCsv = [];

                    for (var i = 0; i < $scope.vehicleTrackingExportRecieved.length; i++) {
                        $scope.vehicleTracking = $scope.vehicleTrackingExportRecieved[i];
                        if ($scope.vehicleTracking.current_date_stamp != null) {
                            $scope.vehicleTrackingExportRecieved[i].current_date_stamp = $scope.vehicleTracking.current_date_stamp.split('.')[0];
                        }

                        if ($scope.vehicleTracking.new_o_data.new_o_route.length) {
                            // alert("fsdfsdf")
                            var current_to_destination_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].distance.value;

                            $scope.vehicleTracking.eta_data.current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].duration.value;
                            // $scope.vehicleTracking.eta_data.current_eta = current_to_destination_eta

                            //    Now origin to current location
                            var origin_to_current_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].distance.value;

                            var origin_to_current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].duration.value;

                            $scope.vehicleTracking.eta_data.distance_total = origin_to_current_distnace + current_to_destination_distnace;
                            $scope.vehicleTracking.eta_data.current_distance = current_to_destination_distnace;

                            // $scope.vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);
                            $scope.vehicleTracking.trip_complete = (($scope.vehicleTracking.eta_data.distance_total - $scope.vehicleTracking.eta_data.current_distance) / ($scope.vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                            //var time_now = new Date();
                            //var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                            //var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;
                            var trip_duration = $scope.vehicleTracking.trip_duration;
                            $scope.vehicleTrackingExportRecieved[i].trip_complete = $scope.vehicleTracking.trip_complete;
                            //Ashish
                            var time_complete_percentage = (trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);

                            if (time_complete_percentage > 100) {
                                time_complete_percentage = 100;
                            }


                            if ($scope.vehicleTrackingExportRecieved[i].trip_complete < 0) {
                                $scope.vehicleTrackingExportRecieved[i].trip_complete = 0;
                            }


                            // $scope.$apply(function () {
                            $scope.vehicleTrackingExportRecieved[i].time_complete = time_complete_percentage;
                            // });

                            if ($scope.vehicleTracking.ideal_time !== '') {
                                $scope.vehicleTracking.final_delay = trip_duration + $scope.vehicleTracking.ideal_eta - $scope.vehicleTracking.ideal_tat;
                                $scope.vehicleTracking.final_eta = $scope.vehicleTracking.ideal_eta
                            }
                            else {
                                var etaRemaining = ((100 - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;

                                if (etaRemaining > $scope.vehicleTracking.eta_data.current_eta) {
                                    $scope.vehicleTracking.final_eta = etaRemaining;
                                }

                                else {
                                    $scope.vehicleTracking.final_eta = $scope.vehicleTracking.eta_data.current_eta;
                                }


                                $scope.vehicleTracking.final_delay = (
                                    (time_complete_percentage - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;
                            }

                            if (time_complete_percentage === 100 && $scope.vehicleTracking.trip_complete !== 100) {
                                var x = trip_duration - parseFloat($scope.vehicleTracking.tat) * 3600;
                                if (x > 0) {
                                    $scope.vehicleTracking.final_delay = x + $scope.vehicleTracking.final_eta;
                                }
                                else {
                                    $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_eta;
                                }
                            }

                            if ($scope.vehicleTracking.final_eta > 0 && isFinite($scope.vehicleTracking.final_eta)) {
                                var final_eta_hour = Math.floor($scope.vehicleTracking.final_eta / 3600);
                                var final_eta_minute = Math.floor(($scope.vehicleTracking.final_eta % 3600) / 60);
                                $scope.vehicleTrackingExportRecieved[i].final_eta = final_eta_hour + "hr "
                                    + final_eta_minute + "min"
                            }
                            else {
                                $scope.vehicleTrackingExportRecieved[i].final_eta = 0
                            }

                            if ($scope.vehicleTrackingExportRecieved[i].trip_end_date != null)
                                $scope.vehicleTrackingExportRecieved[i].final_eta = 0;

                            var total_stoppage = 0;
                            var total_stoppage_previous = 0;
                            angular.forEach($scope.vehicleTracking.order_data.multiple_stopage, function (value) {
                                total_stoppage_previous = total_stoppage;
                                try {
                                    if (value.destination_point.stop_time !== undefined) {
                                        console.log("stop_time: ", $scope.vehicleTracking.trip_code, " ", value.destination_point.stop_time)
                                        total_stoppage += value.destination_point.stop_time;
                                    }
                                }
                                catch (err) {
                                    total_stoppage = total_stoppage_previous;
                                }
                            });

                            if (isFinite(total_stoppage)) {
                                $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_delay - (total_stoppage * 60);
                            }

                            if ($scope.vehicleTracking.final_delay > 300 && isFinite($scope.vehicleTracking.final_delay)) {
                                var final_delay_hour = Math.floor($scope.vehicleTracking.final_delay / 3600);
                                var final_delay_minute = Math.floor(($scope.vehicleTracking.final_delay % 3600) / 60);
                                $scope.vehicleTrackingExportRecieved[i].delay_status = final_delay_hour + "hr "
                                    + final_delay_minute + "min"
                            }
                            else if ($scope.vehicleTracking.trip_complete === 100) {
                                $scope.vehicleTrackingExportRecieved[i].delay_status = "Trip Completed"
                            }
                            else {
                                $scope.vehicleTrackingExportRecieved[i].delay_status = "No Delay"
                            }


                        }
                        else {
                            $scope.vehicleTrackingExportRecieved[i].delay_status = "Location Not Available";
                            $scope.vehicleTrackingExportRecieved[i].trip_complete = "Location Not Available";
                            $scope.vehicleTrackingExportRecieved[i].time_complete = "Location Not Available";
                            if ($scope.vehicleTrackingExportRecieved[i].trip_end_date != null) {
                                $scope.vehicleTrackingExportRecieved[i].final_eta = 0;
                            }

                            else {
                                $scope.vehicleTrackingExportRecieved[i].final_eta = "Location Not Available";
                            }


                            var time_complete_percentage1 = ($scope.vehicleTracking.trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);
                            // var time_now1 = new Date();
                            // var trip_start_date_time1 = new Date($scope.vehicleTrackingReceived[i].trip_start_date_time + '+05:30');
                            // var trip_duration1 = ((+time_now1) - (+trip_start_date_time1)) / 1000;
                            // var time_complete_percentage1 = (trip_duration1 / ($scope.vehicleTrackingReceived[i].tat * 36)).toFixed(0);
                            if (time_complete_percentage1 > 100) {
                                time_complete_percentage1 = 100;
                            }

                            $scope.vehicleTrackingExportRecieved[i].time_complete = time_complete_percentage1;
                        }

                        const value = $scope.vehicleTrackingExportRecieved[i];

                        $scope.lr_no = "";
                        angular.forEach(value.lr, function (value, key) {
                            $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                        });
                        $scope.lr_no = $scope.lr_no.slice(0, -1);
                        $scope.duration = secondsToString(value.duration);
                        $scope.duration1 = value.duration;

                        var distance_in_last_2_hrs = value.distance_last_x_hrs + 'Km';
                        if (!value.distance_last_x_hrs) {
                            distance_in_last_2_hrs = 'NA'
                        }
                        var current_location = 'NA';
                        try {
                            var loc = (value.current_location.location).replace(/,/g, '');
                            current_location = loc + ' @' + value.current_date_stamp;
                        }
                        catch (err) {
                        }

                        $scope.trackingExportData = {
                            "Trip Source": value.source_name,
                            "Trip Code": value.trip_code,
                            "Order Code": value.order_code,
                            "Lr No": $scope.lr_no ? $scope.lr_no : '',
                            "Vehicle No": value.vehicle_no,
                            "Driver Name": value.order_data.driver_name + ' ' + value.order_data.driver_no,
                            "Customer": (value.company + ' <' + value.customer_code + '>').replace(/,/g, ''),
                            "Start Point": (value.start_point.location_name).replace(/,/g, '').replace('<', '').replace('>', ''),
                            "Destination Point": (value.destination_point.location_name).replace(/,/g, '').replace('<', '').replace('>', ''),
                            "Route": value.lane.lane_code,
                            "Start Date": value.trip_start_date_time.split('T')[0],
                            "Start Time": value.trip_start_date_time.split('T')[1],
                            "Current Location": current_location,
                            "Stopped For": $scope.duration1 >= 900 ? $scope.duration : 'NA',
                            "Stopped Since": $scope.duration1 >= 900 ? $filter('date')(value.time_s, 'yyyy-MM-dd h:mm:ss') : 'NA',
                            "Tat": value.tat,
                            "ETA": value.final_eta,
                            "Trip Complete": value.trip_complete + '%',
                            "Time Complete": value.time_complete + '%',
                            "Avg Speed": value.avg_speed ? value.avg_speed + 'Km/hr' : 'NA',
                            "Distance Travelled": value.distance_travelled ? value.distance_travelled + ' Km' : 'NA',
                            "Distance in last 2 hrs": distance_in_last_2_hrs
                        };
                        $scope.downloadPaymentCsv.push($scope.trackingExportData);
                    }

                    var mystyle = {
                        headers: true,
                        column: { style: { Font: { Bold: "1" } } }
                    };
                    alasql('SELECT * INTO XLS("Tracking_Data.xls",?) FROM ?', [mystyle, $scope.downloadPaymentCsv = $filter('orderBy')($scope.downloadPaymentCsv, 'order_code')]);
                }

                let currPage = 1;
                let trackingExportData = [];

                const recurTracking = function () {
                    trackingServices.vehicleTrackingFilterService(currPage, $scope.filterDataObject).then(function (response) {
                        if (response.data.results.code.status === 200) {
                            const count = response.data.count;
                            const totaPages = Math.ceil(count / 100);
                            currPage += 1;

                            if (currPage <= totaPages) {
                                trackingExportData = trackingExportData.concat(response.data.results.trip_data);
                                recurTracking();
                            } else {
                                trackingExportData = trackingExportData.concat(response.data.results.trip_data);
                                generateVehicleTrackingCsv(trackingExportData);
                            }
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    });
                };

                recurTracking();
            }


            /**
             * functionName:getLocation
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 07/02/2018
             * testerName:
             * testDate:
             */
            $scope.getLocation = function (customerName) {
                //$scope.billModel.origin_location = '';
                //$scope.billModel.destination_location = '';
                trackingServices.getLocationLists(customerName).then(function (response) {
                    if (response.data.code.status === 200) {
                        //$scope.locations = response.data.location;
                        $scope.locationNmae = response.data.location;
                        //angular.forEach($scope.locationNmae, function (value, key) {
                        //    $scope.locationNmae[key].location_id = value.id;
                        //});
                        //console.log('new',$scope.locationNmae)
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            };


            //$scope.distLocation = function (location) {
            //    angular.forEach($scope.locationNmae, function (value, key) {
            //        if (value.location_name == location.location_name)
            //            $scope.locationNmae.splice(key, 1);
            //    });
            //};

            //$scope.getLocation();

            $scope.asyncRequestForFilter = function () {
                //Write here function for async request
                async.parallel([
                    // function (callback) {
                    //     // request no 1
                    //     trackingServices.getAllLrOngoingLists().then(function (response) {
                    //
                    //         if (response.data.results.code.status === 200) {
                    //             $scope.lrName = response.data.results.data_list;
                    //             callback();
                    //         } else {
                    //             alert(response.data.results.code.message)
                    //         }
                    //     }, function (err) {
                    //         swal("Oops", 'No internet connection.', "error")
                    //     });
                    // },
                    //
                    // function (callback) {
                    //
                    //    //request no 2
                    //    trackingServices.getAllCustomerLists().then(function (response) {
                    //        if (response.data.code.status === 200) {
                    //            $scope.customerName = response.data.customer;
                    //            callback();
                    //        } else {
                    //            alert(response.data.code.message)
                    //        }
                    //    }, function (err) {
                    //        swal("Oops", 'No internet connection.', "error")
                    //    });
                    // },
                    //
                    // function (callback) {
                    //    //request no 3
                    //    trackingServices.getAllLocationLists().then(function (response) {
                    //        if (response.data.code.status === 200) {
                    //            $scope.locationNmae = response.data.data_list;
                    //            //console.log('old',$scope.locationNmae)
                    //            callback();
                    //        } else {
                    //            alert(response.data.code.message)
                    //        }
                    //    }, function (err) {
                    //        swal("Oops", 'No internet connection.', "error")
                    //    });
                    // },
                    //
                    // function (callback) {
                    //    //request no 4
                    //    trackingServices.getAllBrokerList().then(function (response) {
                    //        if (response.data.code.status === 200) {
                    //            $scope.brokerName = response.data.data_list;
                    //            callback();
                    //        } else {
                    //            alert(response.data.code.message)
                    //        }
                    //    }, function (err) {
                    //        swal("Oops", 'No internet connection.', "error")
                    //    });
                    // },
                    //
                    // function (callback) {
                    //    //request no 5
                    //    trackingServices.getAllVehicleLists().then(function (response) {
                    //        if (response.data.code.status === 200) {
                    //            $scope.vehicleNo = response.data.data_list;
                    //            callback();
                    //        } else {
                    //            alert(response.data.code.message)
                    //        }
                    //    }, function (err) {
                    //        swal("Oops", 'No internet connection.', "error")
                    //    });
                    // }

                ])
            };

            $scope.getLrNumbers = function (data) {
                if (data.length > 3) {
                    var lr = {
                        'lr_num': data
                    };
                    trackingServices.getLrNumSerTrc(lr).then(function (response) {
                        console.log('response', response)
                        if (response.data.code.status === 200) {
                            $scope.lrName = response.data.lr;
                        }
                        else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                }
            };

            $scope.pagination = {};
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                lane_type: '',
                vehicle_no: ''
            };

            $scope.vehicleTrackingFilter = function (count, filterData) {

            console.log("===>", filterData)
                $scope.loading = true;
                $scope.vehicle_no = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';
                $scope.lr_no = '';
                $scope.customer_id = '';
                $scope.lane_type = '';
                $scope.delay = '';
                $scope.source_name = '';

                if (filterData.source_name){

                $scope.source_name = filterData.source_name.name;

                 }


                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;

                if (filterData.lane_type)
                    $scope.lane_type = filterData.lane_type;

                if (filterData.delay)
                    $scope.delay = filterData.delay;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required,
                    lane_type: $scope.lane_type,
                    lr: $scope.lr_no,
                    delay: $scope.delay,
                    source_name: $scope.source_name
                };
                console.log('$scope.filterDataObject', $scope.filterDataObject)
                $scope.vehicleTrackingFilterPage = { 'next': '', 'previous': '', 'count': '' };
                $scope.completedTrips = '';
                $scope.searchText = '';
                trackingServices.vehicleTrackingFilterService(count, $scope.filterDataObject).then(function (response) {
                    $scope.loading = false;
                    console.log('vehicleTrackingFilter', response);
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    if (response.data.results.code.status === 200) {
                        $scope.vehicleTrackingReceived = response.data.results.trip_data;
                        $scope.wf = response.data.results.wf;
                        $scope.completedTripPage.count = response.data.count;
                        for (var i = 0; i < $scope.vehicleTrackingReceived.length; i++) {
                            $scope.vehicleTracking = $scope.vehicleTrackingReceived[i];
                            if ($scope.vehicleTracking.current_date_stamp != null) {
                                $scope.vehicleTrackingReceived[i].current_date_stamp = $scope.vehicleTracking.current_date_stamp.split('.')[0];
                            }

                            if ($scope.vehicleTracking.new_o_data.new_o_route.length) {
                                // alert("fsdfsdf")
                                var current_to_destination_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].distance.value;

                                $scope.vehicleTracking.eta_data.current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].duration.value;
                                // $scope.vehicleTracking.eta_data.current_eta = current_to_destination_eta

                                //    Now origin to current location
                                try {
                                    var origin_to_current_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].distance.value;
                                }
                                catch (err) {
                                    origin_to_current_distnace = 0
                                }
                                try {
                                    var origin_to_current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].duration.value;
                                }
                                catch (err) {
                                    origin_to_current_eta = 0
                                }
                                $scope.vehicleTracking.eta_data.distance_total = origin_to_current_distnace + current_to_destination_distnace;
                                $scope.vehicleTracking.eta_data.current_distance = current_to_destination_distnace;

                                // $scope.vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);
                                $scope.vehicleTracking.trip_complete = (($scope.vehicleTracking.eta_data.distance_total - $scope.vehicleTracking.eta_data.current_distance) / ($scope.vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                                //var time_now = new Date();
                                //var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                                //var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;
                                var trip_duration = $scope.vehicleTracking.trip_duration;
                                $scope.vehicleTrackingReceived[i].trip_complete = $scope.vehicleTracking.trip_complete;
                                //Ashish
                                var time_complete_percentage = (trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);

                                if (time_complete_percentage > 100) {
                                    time_complete_percentage = 100;
                                }


                                if ($scope.vehicleTrackingReceived[i].trip_complete < 0) {
                                    $scope.vehicleTrackingReceived[i].trip_complete = 0;
                                }


                                // $scope.$apply(function () {
                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage;
                                // });

                                if ($scope.vehicleTracking.ideal_time !== '') {
                                    $scope.vehicleTracking.final_delay = trip_duration + $scope.vehicleTracking.ideal_eta - $scope.vehicleTracking.ideal_tat;
                                    $scope.vehicleTracking.final_eta = $scope.vehicleTracking.ideal_eta
                                }
                                else {
                                    var etaRemaining = ((100 - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;

                                    if (etaRemaining > $scope.vehicleTracking.eta_data.current_eta) {
                                        $scope.vehicleTracking.final_eta = etaRemaining;
                                    }

                                    else {
                                        $scope.vehicleTracking.final_eta = $scope.vehicleTracking.eta_data.current_eta;
                                    }


                                    $scope.vehicleTracking.final_delay = (
                                        (time_complete_percentage - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;
                                }

                                if (time_complete_percentage === 100 && $scope.vehicleTracking.trip_complete !== 100) {
                                    var x = trip_duration - parseFloat($scope.vehicleTracking.tat) * 3600;
                                    if (x > 0) {
                                        $scope.vehicleTracking.final_delay = x + $scope.vehicleTracking.final_eta;
                                    }
                                    else {
                                        $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_eta;
                                    }
                                }

                                if ($scope.vehicleTracking.final_eta > 0 && isFinite($scope.vehicleTracking.final_eta)) {
                                    var final_eta_hour = Math.floor($scope.vehicleTracking.final_eta / 3600);
                                    var final_eta_minute = Math.floor(($scope.vehicleTracking.final_eta % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].final_eta = final_eta_hour + "hr "
                                        + final_eta_minute + "min"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].final_eta = 0
                                }

                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null)
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;

                                var total_stoppage = 0;
                                var total_stoppage_previous = 0;
                                angular.forEach($scope.vehicleTracking.order_data.multiple_stopage, function (value) {
                                    total_stoppage_previous = total_stoppage;
                                    try {
                                        if (value.destination_point.stop_time !== undefined) {
                                            console.log("stop_time: ", $scope.vehicleTracking.trip_code, " ", value.destination_point.stop_time);
                                            total_stoppage += value.destination_point.stop_time;
                                        }
                                    }
                                    catch (err) {
                                        total_stoppage = total_stoppage_previous;
                                    }
                                });

                                if (isFinite(total_stoppage)) {
                                    $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_delay - (total_stoppage * 60);
                                }

                                if ($scope.vehicleTracking.final_delay > 300 && isFinite($scope.vehicleTracking.final_delay)) {
                                    var final_delay_hour = Math.floor($scope.vehicleTracking.final_delay / 3600);
                                    var final_delay_minute = Math.floor(($scope.vehicleTracking.final_delay % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].delay_status = final_delay_hour + "hr "
                                        + final_delay_minute + "min"
                                }
                                else if ($scope.vehicleTracking.trip_complete === 100) {
                                    $scope.vehicleTrackingReceived[i].delay_status = "Trip Completed"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].delay_status = "No Delay"
                                }


                            }
                            // if ($scope.vehicleTracking.current_location) {
                            //     $scope.current_location_lat = +($scope.vehicleTracking.current_location.lat);
                            //     $scope.current_location_long = +($scope.vehicleTracking.current_location.long);
                            //     $scope.origin = {lat: $scope.current_location_lat, lng: $scope.current_location_long};
                            //     $scope.current_eta_lat = $scope.vehicleTracking.current_eta_destination[1];
                            //     $scope.current_eta_long = $scope.vehicleTracking.current_eta_destination[0];
                            //     $scope.destination = {lat: $scope.current_eta_lat, lng: $scope.current_eta_long};
                            //     //console.log('$scope.destination', $scope.destination)
                            //     var service = new google.maps.DistanceMatrixService;
                            //     service.getDistanceMatrix({
                            //
                            //         origins: [$scope.origin],
                            //         destinations: [$scope.destination],
                            //         travelMode: 'DRIVING',
                            //         unitSystem: google.maps.UnitSystem.METRIC,
                            //         avoidHighways: false,
                            //         avoidTolls: false
                            //     }, buildResponseFor(i))
                            // }
                            else {
                                $scope.vehicleTrackingReceived[i].delay_status = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].trip_complete = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].time_complete = "Location Not Available";
                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null) {
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;
                                }

                                else {
                                    $scope.vehicleTrackingReceived[i].final_eta = "Location Not Available";
                                }


                                var time_complete_percentage1 = ($scope.vehicleTracking.trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);
                                // var time_now1 = new Date();
                                // var trip_start_date_time1 = new Date($scope.vehicleTrackingReceived[i].trip_start_date_time + '+05:30');
                                // var trip_duration1 = ((+time_now1) - (+trip_start_date_time1)) / 1000;
                                // var time_complete_percentage1 = (trip_duration1 / ($scope.vehicleTrackingReceived[i].tat * 36)).toFixed(0);
                                if (time_complete_percentage1 > 100) {
                                    time_complete_percentage1 = 100;
                                }

                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage1;
                            }
                        }
                    } else {
                        alert(response.data.results.code.message)
                    }
                })

            };

            $scope.clearFilterTracking = function () {
                //Ashish
                $scope.pagination.current = 1;
                $scope.searchText = '';
                $scope.vehicleTrackingData();
                $scope.filterData = {
                    lr_no: '',
                    customer: '',
                    origin: '',
                    destination: '',
                    date_vehicle_required: '',
                    to_date_vehicle_required: '',
                    vehicle_no: ''
                };
            };

            function buildResponseForExport(i) {
                return function (response, status) {
                    if (status !== 'OK') {
                        alert('Error was: ' + status);
                    }
                    else {
                        var vehicleTracking = $scope.vehicleTrackingExport[i];
                        vehicleTracking.eta_data.current_distance = response.rows[0].elements[0].distance.value;
                        vehicleTracking.eta_data.current_eta = response.rows[0].elements[0].duration.value;

                        vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                        var time_now = new Date();
                        var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                        var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;

                        $scope.vehicleTrackingExport[i].trip_complete = vehicleTracking.trip_complete;
                        //Ashish
                        var time_complete_percentage = (trip_duration / (vehicleTracking.tat * 36)).toFixed(0);

                        if (time_complete_percentage > 100)
                            time_complete_percentage = 100;

                        if ($scope.vehicleTrackingExport[i].trip_complete < 0)
                            $scope.vehicleTrackingExport[i].trip_complete = 0;

                        $scope.$apply(function () {
                            $scope.vehicleTrackingExport[i].time_complete = time_complete_percentage;
                        });

                        if (vehicleTracking.ideal_time !== '') {
                            vehicleTracking.final_delay = trip_duration - vehicleTracking.ideal_time;
                            vehicleTracking.final_eta = vehicleTracking.ideal_eta
                        }
                        else {
                            var etaRemaining = ((100 - vehicleTracking.trip_complete) * vehicleTracking.tat) * 36;

                            if (etaRemaining > vehicleTracking.eta_data.current_eta)
                                vehicleTracking.final_eta = etaRemaining;
                            else
                                vehicleTracking.final_eta = vehicleTracking.eta_data.current_eta;

                            vehicleTracking.final_delay = (
                                (time_complete_percentage - vehicleTracking.trip_complete) * vehicleTracking.tat) * 36;
                        }

                        if (time_complete_percentage === 100 && vehicleTracking.trip_complete !== 100) {
                            var x = trip_duration - parseFloat(vehicleTracking.tat) * 3600;
                            if (x > 0) {
                                vehicleTracking.final_delay = x + vehicleTracking.final_eta;
                            }
                            else {
                                vehicleTracking.final_delay = vehicleTracking.final_eta;
                            }
                        }

                        if (vehicleTracking.final_eta > 0 && isFinite(vehicleTracking.final_eta)) {
                            var final_eta_hour = Math.floor(vehicleTracking.final_eta / 3600);
                            var final_eta_minute = Math.floor((vehicleTracking.final_eta % 3600) / 60);
                            $scope.vehicleTrackingExport[i].final_eta = final_eta_hour + "hr "
                                + final_eta_minute + "min"
                        }
                        else {
                            $scope.vehicleTrackingExport[i].final_eta = 0
                        }

                        if ($scope.vehicleTrackingExport[i].trip_end_date != null)
                            $scope.vehicleTrackingExport[i].final_eta = 0;

                        var total_stoppage = 0;
                        var total_stoppage_previous = 0;
                        angular.forEach(vehicleTracking.order_data.multiple_stopage, function (value) {
                            total_stoppage_previous = total_stoppage;
                            try {
                                if (value.destination_point.stop_time !== undefined) {
                                    console.log("stop_time: ", vehicleTracking.trip_code, " ", value.destination_point.stop_time)
                                    total_stoppage += value.destination_point.stop_time;
                                }
                            }
                            catch (err) {
                                total_stoppage = total_stoppage_previous;
                            }
                        });

                        if (isFinite(total_stoppage)) {
                            console.log("total_stoppage: ", vehicleTracking.trip_code, " ", total_stoppage);
                            vehicleTracking.final_delay = vehicleTracking.final_delay - (total_stoppage * 60);
                        }

                        if (vehicleTracking.final_delay > 300 && isFinite(vehicleTracking.final_delay)) {
                            var final_delay_hour = Math.floor(vehicleTracking.final_delay / 3600);
                            var final_delay_minute = Math.floor((vehicleTracking.final_delay % 3600) / 60);
                            $scope.vehicleTrackingExport[i].delay_status = final_delay_hour + "hr "
                                + final_delay_minute + "min"
                        }
                        else {
                            $scope.vehicleTrackingExport[i].delay_status = "No Delay"
                        }
                    }
                }
            }

            function buildResponseFor(i, t_d) {
                return function (response, status) {
                    if (status !== 'OK') {
                        alert('Error was: ' + status);
                    }
                    else {
                        var vehicleTracking = $scope.vehicleTrackingReceived[i];
                        vehicleTracking.eta_data.current_distance = response.rows[0].elements[0].distance.value;
                        vehicleTracking.eta_data.current_eta = response.rows[0].elements[0].duration.value;

                        vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                        //var time_now = new Date();
                        //var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                        //var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;
                        var trip_duration = t_d;
                        $scope.vehicleTrackingReceived[i].trip_complete = vehicleTracking.trip_complete;
                        //Ashish
                        var time_complete_percentage = (trip_duration / (vehicleTracking.tat * 36)).toFixed(0);

                        if (time_complete_percentage > 100)
                            time_complete_percentage = 100;

                        if ($scope.vehicleTrackingReceived[i].trip_complete < 0)
                            $scope.vehicleTrackingReceived[i].trip_complete = 0;

                        $scope.$apply(function () {
                            $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage;
                        });


                        if ($scope.vehicleTracking.ideal_time !== '') {
                            vehicleTracking.final_delay = trip_duration + vehicleTracking.ideal_eta - vehicleTracking.ideal_tat;
                            vehicleTracking.final_eta = vehicleTracking.ideal_eta
                        }
                        else {
                            var etaRemaining = ((100 - vehicleTracking.trip_complete) * vehicleTracking.tat) * 36;

                            if (etaRemaining > vehicleTracking.eta_data.current_eta)
                                vehicleTracking.final_eta = etaRemaining;
                            else
                                vehicleTracking.final_eta = vehicleTracking.eta_data.current_eta;

                            vehicleTracking.final_delay = (
                                (time_complete_percentage - vehicleTracking.trip_complete) * vehicleTracking.tat) * 36;
                        }

                        if (time_complete_percentage === 100 && vehicleTracking.trip_complete !== 100) {
                            var x = trip_duration - parseFloat(vehicleTracking.tat) * 3600;
                            if (x > 0) {
                                vehicleTracking.final_delay = x + vehicleTracking.final_eta;
                            }
                            else {
                                vehicleTracking.final_delay = vehicleTracking.final_eta;
                            }
                        }

                        if (vehicleTracking.final_eta > 0 && isFinite(vehicleTracking.final_eta)) {
                            var final_eta_hour = Math.floor(vehicleTracking.final_eta / 3600);
                            var final_eta_minute = Math.floor((vehicleTracking.final_eta % 3600) / 60);
                            $scope.vehicleTrackingReceived[i].final_eta = final_eta_hour + "hr "
                                + final_eta_minute + "min"
                        }
                        else {
                            $scope.vehicleTrackingReceived[i].final_eta = 0
                        }

                        if ($scope.vehicleTrackingReceived[i].trip_end_date != null)
                            $scope.vehicleTrackingReceived[i].final_eta = 0;

                        var total_stoppage = 0;
                        var total_stoppage_previous = 0;
                        angular.forEach(vehicleTracking.order_data.multiple_stopage, function (value) {
                            total_stoppage_previous = total_stoppage;
                            try {
                                if (value.destination_point.stop_time !== undefined) {
                                    console.log("stop_time: ", vehicleTracking.trip_code, " ", value.destination_point.stop_time)
                                    total_stoppage += value.destination_point.stop_time;
                                }
                            }
                            catch (err) {
                                total_stoppage = total_stoppage_previous;
                            }
                        });

                        if (isFinite(total_stoppage)) {
                            vehicleTracking.final_delay = vehicleTracking.final_delay - (total_stoppage * 60);
                        }

                        if (vehicleTracking.final_delay > 300 && isFinite(vehicleTracking.final_delay)) {
                            var final_delay_hour = Math.floor(vehicleTracking.final_delay / 3600);
                            var final_delay_minute = Math.floor((vehicleTracking.final_delay % 3600) / 60);
                            $scope.vehicleTrackingReceived[i].delay_status = final_delay_hour + "hr "
                                + final_delay_minute + "min"
                        }
                        else if (vehicleTracking.trip_complete === 100) {
                            $scope.vehicleTrackingReceived[i].delay_status = "Trip Completed"
                        }
                        else {
                            $scope.vehicleTrackingReceived[i].delay_status = "No Delay"
                        }

                    }
                }
            }


            $scope.finGetTracking = function (data) {
                $scope.financeAttribute = {
                    'trip_code': data.trip_code,
                    'advance': data.finance_attr.advance,
                    'advance_remark': data.finance_attr.advance_remark,
                    'balance': data.finance_attr.balance,
                    'balance_remark': data.finance_attr.balance_remark,
                    'deduction': data.finance_attr.deduction,
                    'deduction_remark': data.finance_attr.deduction_remark
                }
            };

            $scope.redirectToMaintenance = function (vehicleNumber) {
                let redirectToLink = '';
                const PORT = 3000;
                const hostName = window.location.hostname;
                let firstDotIndex = hostName.indexOf('.');
                const env = (firstDotIndex === -1 || hostName === "127.0.0.1") ? hostName : hostName.substring(firstDotIndex + 1);
                const isLocalOrDev = ["localhost", "127.0.0.1", "gobolt.dev", "gobolt.live"].includes(env);
                

                if (isLocalOrDev) {
                    if (env === "localhost" || env === "127.0.0.1") {
                        redirectToLink = `http://${env}:${PORT}/ops/request/create/${vehicleNumber}`;
                    } else {
                        redirectToLink = `http://maintenance.${env}/ops/request/create/${vehicleNumber}`;
                    }
                } else {
                    redirectToLink = `https://maintenance.gobolt.team/ops/request/create/${vehicleNumber}`;
                }
                window.open(redirectToLink, target='_blank');
            }

            const requestListingAPIStatusObj = {
                OPEN: "OPEN",
                REVIEW: "REVIEW",
                APPROVED: "APPROVED",
                IN_PROGRESS: "IN_PROGRESS",
                AWAITING_APPROVAL: "AWAITING_APPROVAL",
                PAYMENT_RAISED: "PAYMENT_RAISED",
                PAYMENT_PAID: "PAYMENT_PAID",
                PAYMENT_REJECTED: "PAYMENT_REJECTED",
                CANCELLED: "CANCELLED",
                REJECTED: "REJECTED",
            }

            function getMaintenanceStatusColor(status){
                switch (status) {
                    case requestListingAPIStatusObj.PAYMENT_RAISED: 
                    case requestListingAPIStatusObj.PAYMENT_PAID:
                        return 0;
                    case requestListingAPIStatusObj.REJECTED:
                    case requestListingAPIStatusObj.PAYMENT_REJECTED:
                    case requestListingAPIStatusObj.CANCELLED:
                        return 1;
                    default:
                        return 2;
                }
            }

            $scope.breakdownDetailsModal = function(trackingData){
                // to create data to be shown in modal
                const maintenanceCode = trackingData.maintenance_code;
                $scope.breakdownHeader = {
                    vehicle_no: trackingData.vehicle_no,
                    origin_name: trackingData.start_point.location_name,
                    origin_code: trackingData.start_point.location_code,
                    destination_name: trackingData.destination_point.location_name,
                    destination_code: trackingData.destination_point.location_code,
                    maintenance_code: maintenanceCode
                }
                trackingServices.getVehicleMaintenanceData(maintenanceCode).then(function (response){
                    if(response.data && response.data.code && response.data.code.status==200){
                        const data = response.data.data;
                        $scope.breakdownData = {
                            ...data,
                            haltedColour: data.haltedColour ? data.haltedColour : 3,
                            statusColor: getMaintenanceStatusColor(data.status)
                        }
                    }
                })
            }

            $scope.clearBreakdownData = function (){
                $scope.breakdownHeader = {};
                $scope.breakdownData = {};
            }

            $scope.getDhanukaLockInfo = function (trackingData) {
                $scope.dhanukaLock = {};
                const vehicle_number = trackingData.order_data.vehicle_no;
                const order_code = trackingData.order_code;
                trackingServices.getDhanukaLockInfo(vehicle_number, order_code).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.dhanukaLock = response.data.data;
                    }
                })
            };

            $scope.getDhanukaLockColorClass = function (status) {
                if (!status) {
                    return;
                }

                const upperStatus = status.toUpperCase();
                return upperStatus === 'LOCKED' || upperStatus === 'HEALTHLY' ? 'green' : 'red';
            };

            $scope.fetchContractIds = function (trackingData) {
                $scope.data = {};
                $scope.contract_ids = [];
                $scope.selectedOrderCode = trackingData.order_code;

                const orderData = trackingData.order_data;
                const contract_id = orderData.contract_id;
                $scope.currentContractId = contract_id;

                trackingServices.fetchContractIds(contract_id).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.contract_ids = response.data.c_ids;
                        angular.forEach($scope.contract_ids, function (contract) {
                            if (contract.contract_id === trackingData.order_data.contract_id) {
                                $scope.data.contract_id = contract;
                                return;
                            }
                        });
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            }

            $scope.changeContractId = function (selectedContract) {
                if (!selectedContract || !selectedContract.contract_id) {
                    swal("Oops", "Please Select Contract ID", "error")
                    return;
                }
                const payload = {
                    order_code: $scope.selectedOrderCode,
                    contract_id: selectedContract.contract_id,
                }
                trackingServices.changeContractId(payload).then(function (response) {
                    if (response?.data?.code?.status === 200) {
                        angular.element('#change_contract_id_modal').modal('hide');
                        swal("Good !", response.data.code.message, "success");
                        $scope.selectedContract = null;
                        $scope.vehicleTrackingFilterAfterContractChange($scope.pagination.current, $scope.filterDataObject);
                    } else {
                        swal("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            }

            $scope.vehicleTrackingFilterAfterContractChange = function (count, filterData) {
                $scope.loading = true;
                let filterDataObject = filterData;

                if (filterDataObject === undefined || filterDataObject === null) {
                    filterDataObject = {
                        lr_no: '',
                        customer: '',
                        origin: '',
                        destination: '',
                        date_vehicle_required: '',
                        to_date_vehicle_required: '',
                        lane_type: '',
                        vehicle_no: ''
                    };
                }

                console.log('filterDataObject', filterDataObject)
                $scope.vehicleTrackingFilterPage = { 'next': '', 'previous': '', 'count': '' };
                $scope.completedTrips = '';
                $scope.searchText = '';
                trackingServices.vehicleTrackingFilterService(count, filterDataObject).then(function (response) {
                    $scope.loading = false;
                    console.log('vehicleTrackingFilter', response);
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    if (response.data.results.code.status === 200) {
                        $scope.vehicleTrackingReceived = response.data.results.trip_data;
                        $scope.wf = response.data.results.wf;
                        $scope.completedTripPage.count = response.data.count;
                        for (var i = 0; i < $scope.vehicleTrackingReceived.length; i++) {
                            $scope.vehicleTracking = $scope.vehicleTrackingReceived[i];
                            if ($scope.vehicleTracking.current_date_stamp != null) {
                                $scope.vehicleTrackingReceived[i].current_date_stamp = $scope.vehicleTracking.current_date_stamp.split('.')[0];
                            }

                            if ($scope.vehicleTracking.new_o_data.new_o_route.length) {
                                // alert("fsdfsdf")
                                var current_to_destination_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].distance.value;

                                $scope.vehicleTracking.eta_data.current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[0].elements[0].duration.value;
                                // $scope.vehicleTracking.eta_data.current_eta = current_to_destination_eta

                                //    Now origin to current location
                                try {
                                    var origin_to_current_distnace = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].distance.value;
                                }
                                catch (err) {
                                    origin_to_current_distnace = 0
                                }
                                try {
                                    var origin_to_current_eta = $scope.vehicleTracking.new_o_data.new_o_route[0].rows[1].elements[1].duration.value;
                                }
                                catch (err) {
                                    origin_to_current_eta = 0
                                }
                                $scope.vehicleTracking.eta_data.distance_total = origin_to_current_distnace + current_to_destination_distnace;
                                $scope.vehicleTracking.eta_data.current_distance = current_to_destination_distnace;

                                // $scope.vehicleTracking.trip_complete = ((vehicleTracking.eta_data.distance_total - vehicleTracking.eta_data.current_distance) / (vehicleTracking.eta_data.distance_total) * 100).toFixed(0);
                                $scope.vehicleTracking.trip_complete = (($scope.vehicleTracking.eta_data.distance_total - $scope.vehicleTracking.eta_data.current_distance) / ($scope.vehicleTracking.eta_data.distance_total) * 100).toFixed(0);

                                //var time_now = new Date();
                                //var trip_start_date_time = new Date(vehicleTracking.trip_start_date_time + '+05:30');
                                //var trip_duration = ((+time_now) - (+trip_start_date_time)) / 1000;
                                var trip_duration = $scope.vehicleTracking.trip_duration;
                                $scope.vehicleTrackingReceived[i].trip_complete = $scope.vehicleTracking.trip_complete;
                                //Ashish
                                var time_complete_percentage = (trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);

                                if (time_complete_percentage > 100) {
                                    time_complete_percentage = 100;
                                }


                                if ($scope.vehicleTrackingReceived[i].trip_complete < 0) {
                                    $scope.vehicleTrackingReceived[i].trip_complete = 0;
                                }


                                // $scope.$apply(function () {
                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage;
                                // });

                                if ($scope.vehicleTracking.ideal_time !== '') {
                                    $scope.vehicleTracking.final_delay = trip_duration + $scope.vehicleTracking.ideal_eta - $scope.vehicleTracking.ideal_tat;
                                    $scope.vehicleTracking.final_eta = $scope.vehicleTracking.ideal_eta
                                }
                                else {
                                    var etaRemaining = ((100 - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;

                                    if (etaRemaining > $scope.vehicleTracking.eta_data.current_eta) {
                                        $scope.vehicleTracking.final_eta = etaRemaining;
                                    }

                                    else {
                                        $scope.vehicleTracking.final_eta = $scope.vehicleTracking.eta_data.current_eta;
                                    }


                                    $scope.vehicleTracking.final_delay = (
                                        (time_complete_percentage - $scope.vehicleTracking.trip_complete) * $scope.vehicleTracking.tat) * 36;
                                }

                                if (time_complete_percentage === 100 && $scope.vehicleTracking.trip_complete !== 100) {
                                    var x = trip_duration - parseFloat($scope.vehicleTracking.tat) * 3600;
                                    if (x > 0) {
                                        $scope.vehicleTracking.final_delay = x + $scope.vehicleTracking.final_eta;
                                    }
                                    else {
                                        $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_eta;
                                    }
                                }

                                if ($scope.vehicleTracking.final_eta > 0 && isFinite($scope.vehicleTracking.final_eta)) {
                                    var final_eta_hour = Math.floor($scope.vehicleTracking.final_eta / 3600);
                                    var final_eta_minute = Math.floor(($scope.vehicleTracking.final_eta % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].final_eta = final_eta_hour + "hr "
                                        + final_eta_minute + "min"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].final_eta = 0
                                }

                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null)
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;

                                var total_stoppage = 0;
                                var total_stoppage_previous = 0;
                                angular.forEach($scope.vehicleTracking.order_data.multiple_stopage, function (value) {
                                    total_stoppage_previous = total_stoppage;
                                    try {
                                        if (value.destination_point.stop_time !== undefined) {
                                            console.log("stop_time: ", $scope.vehicleTracking.trip_code, " ", value.destination_point.stop_time);
                                            total_stoppage += value.destination_point.stop_time;
                                        }
                                    }
                                    catch (err) {
                                        total_stoppage = total_stoppage_previous;
                                    }
                                });

                                if (isFinite(total_stoppage)) {
                                    $scope.vehicleTracking.final_delay = $scope.vehicleTracking.final_delay - (total_stoppage * 60);
                                }

                                if ($scope.vehicleTracking.final_delay > 300 && isFinite($scope.vehicleTracking.final_delay)) {
                                    var final_delay_hour = Math.floor($scope.vehicleTracking.final_delay / 3600);
                                    var final_delay_minute = Math.floor(($scope.vehicleTracking.final_delay % 3600) / 60);
                                    $scope.vehicleTrackingReceived[i].delay_status = final_delay_hour + "hr "
                                        + final_delay_minute + "min"
                                }
                                else if ($scope.vehicleTracking.trip_complete === 100) {
                                    $scope.vehicleTrackingReceived[i].delay_status = "Trip Completed"
                                }
                                else {
                                    $scope.vehicleTrackingReceived[i].delay_status = "No Delay"
                                }


                            }
                            // if ($scope.vehicleTracking.current_location) {
                            //     $scope.current_location_lat = +($scope.vehicleTracking.current_location.lat);
                            //     $scope.current_location_long = +($scope.vehicleTracking.current_location.long);
                            //     $scope.origin = {lat: $scope.current_location_lat, lng: $scope.current_location_long};
                            //     $scope.current_eta_lat = $scope.vehicleTracking.current_eta_destination[1];
                            //     $scope.current_eta_long = $scope.vehicleTracking.current_eta_destination[0];
                            //     $scope.destination = {lat: $scope.current_eta_lat, lng: $scope.current_eta_long};
                            //     //console.log('$scope.destination', $scope.destination)
                            //     var service = new google.maps.DistanceMatrixService;
                            //     service.getDistanceMatrix({
                            //
                            //         origins: [$scope.origin],
                            //         destinations: [$scope.destination],
                            //         travelMode: 'DRIVING',
                            //         unitSystem: google.maps.UnitSystem.METRIC,
                            //         avoidHighways: false,
                            //         avoidTolls: false
                            //     }, buildResponseFor(i))
                            // }
                            else {
                                $scope.vehicleTrackingReceived[i].delay_status = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].trip_complete = "Location Not Available";
                                $scope.vehicleTrackingReceived[i].time_complete = "Location Not Available";
                                if ($scope.vehicleTrackingReceived[i].trip_end_date != null) {
                                    $scope.vehicleTrackingReceived[i].final_eta = 0;
                                }

                                else {
                                    $scope.vehicleTrackingReceived[i].final_eta = "Location Not Available";
                                }


                                var time_complete_percentage1 = ($scope.vehicleTracking.trip_duration / ($scope.vehicleTracking.tat * 36)).toFixed(0);
                                // var time_now1 = new Date();
                                // var trip_start_date_time1 = new Date($scope.vehicleTrackingReceived[i].trip_start_date_time + '+05:30');
                                // var trip_duration1 = ((+time_now1) - (+trip_start_date_time1)) / 1000;
                                // var time_complete_percentage1 = (trip_duration1 / ($scope.vehicleTrackingReceived[i].tat * 36)).toFixed(0);
                                if (time_complete_percentage1 > 100) {
                                    time_complete_percentage1 = 100;
                                }

                                $scope.vehicleTrackingReceived[i].time_complete = time_complete_percentage1;
                            }
                        }
                    } else {
                        alert(response.data.results.code.message)
                    }
                })
            }

            /******************************Co-Driver Data Get*******************************************************/

            $scope.coDriverDetailsPlanning = function (orderAssign) {
                trackingServices.coDriverDataPlanning(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };

            $scope.showDLPicture = function (dl_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(dl_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDlPic = response.data.url
                    });
                }
                else {


                    $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic;
                }
            };
            $scope.showDriverWithVehiclePicture = function (driver_vehicle_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverVehiclePic = response.data.url
                    });
                }
                else {


                    $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic;
                }
            };
            $scope.showFrontInvoicePicture = function (front_invoice) {
                $scope.invoiceFront = null;
                if (!front_invoice) {
                    setTimeout(function () {
                        $('#frontInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(front_invoice)) {
                    $scope.invoiceFront = front_invoice;
                    return;
                }

                if (pic) {
                    trackingServices.picLoadServerT(front_invoice).then(function (response) {

                        if (response.data.code.status === 200) {
                            $scope.invoiceFront = response.data.url
                        } else {
                            $('#frontInvoice').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                }
                else {
                    $scope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                }
            };

            $scope.showBackInvoicePicture = function (back_invoice) {
                $scope.invoiceBack = null;
                if (!back_invoice) {
                    setTimeout(function () {
                        $('#backInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(back_invoice)) {
                    $scope.invoiceBack = back_invoice;
                    return;
                }

                if (pic) {
                    trackingServices.picLoadServerT(back_invoice).then(function (response) {

                        if (response.data.code.status === 200) {
                            $scope.invoiceBack = response.data.url
                        } else {
                            $('#backInvoice').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                }
                else {
                    $scope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
                }
            };

            $scope.driverDetailsPlacement = function (orderAssign) {
                trackingServices.driverOnlyDataList(orderAssign.id).then(function (response) {
                    console.log('1', response);
                    if (response.data.code.status == 200) {

                        $scope.driver = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };


            $scope.vehicleDetailsPlacement = function (orderAssign) {
                trackingServices.vehicleDetailsList(orderAssign.id).then(function (response) {
                    console.log('2', response);
                    if (response.data.code.status == 200) {
                        $scope.vehicle = response.data.data_list;

                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                });
            };

            $scope.showInsuPicture = function (insurance_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                        // console.log(response)
                        $scope.showInsurancePic = response.data.url
                    });
                }
                else {
                    $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
                }

            };

            $scope.showFitPicture = function (fitness_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                        // console.log(response)
                        $scope.showFitnessPic = response.data.url
                    });
                }
                else {
                    $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
                }


            };

            $scope.showRcPicture = function (rc_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(rc_pic).then(function (response) {

                        // console.log(response)
                        $scope.showRcPic = response.data.url;
                    });
                }
                else {
                    $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
                }

            };

            $scope.showPermitPicture = function (permit_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(permit_pic).then(function (response) {

                        // console.log(response)
                        $scope.showPermitPic = response.data.url;
                    });
                }
                else {
                    $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
                }


            };

            $scope.vehicletracking = function () {
                $state.reload();
            };


            $scope.dateTime = function (data) {
                //Ashish
                $scope.index = $scope.vehicleTrackingReceived.indexOf(data);
                // $scope.addLoc(data, $scope.index);
                console.log('vehicleTracking', data);
                $scope.index = $scope.vehicleTrackingReceived.indexOf(data);
                $scope.tripId = data.id;
                $scope.order_id = data.order_id;
                $scope.order_data = data.order_data;
                $scope.indent_id = data.order_data.indent_id;
                $scope.vehicleReporteVehicleNo = data.vehicle_no;
                $scope.vehicleReporteStartDateTime = data.trip_start_date_time;
                $scope.dataForvehicleReporting = data;
                console.log('$scope.vehicle_no', $scope.vehicle_no);
                var date = new Date();

                //$scope.$watch('date', function () {
                $scope.ddMMyyyy = $filter('date')(new Date(), 'yyyy-MM-dd');
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');
                console.log('$scope.HHmmss', $scope.HHmmss);
                //})

            };

            $scope.reportingDateTime = function (ddMMyyyy, HHmmss) {
                swal({
                    title: "Are you sure?",
                    text: "You want to report the vehicle!",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Yes, report the vehicle!",
                    cancelButtonText: "No, cancel !",
                    showLoaderOnConfirm: true,
                    closeOnConfirm: false,
                    closeOnCancel: false
                },
                    function (isConfirmed) {
                        if (isConfirmed) {
                            setTimeout(function () {
                                $scope.reportingDateTimeTrip = {
                                    trip_end_date: ddMMyyyy,
                                    trip_end_time: HHmmss + ':00',
                                    id: $scope.tripId,
                                    //Ashish
                                    order_id: $scope.order_id,
                                    order_data: $scope.order_data,
                                    indent_id: $scope.order_data.indent_id
                                };

                                try {
                                    $scope.location_data = {
                                        'lat': $scope.dataForvehicleReporting.destination_point.location_point.coordinates.coordinates[1],
                                        'long': $scope.dataForvehicleReporting.destination_point.location_point.coordinates.coordinates[0]
                                    };
                                }
                                catch (err) {
                                    $scope.location_data = {
                                        'lat': $scope.dataForvehicleReporting.destination_point.location_point.coordinates[1],
                                        'long': $scope.dataForvehicleReporting.destination_point.location_point.coordinates[0]
                                    };
                                }

                                trackingServices.postReportingDateTimeTrip($scope.reportingDateTimeTrip).then(function (response) {
                                    console.log('response herer', response);
                                    // $scope.add_loc($scope.location_data);
                                    if (response.data.code.status == 200) {
                                        $scope.vehicleTrackingReceived[$scope.index].trip_status.status_name = response.data.trip_status;
                                        $scope.vehicleTrackingReceived[$scope.index].trip_end_date = ddMMyyyy;
                                        $scope.vehicleTrackingReceived[$scope.index].trip_end_time = HHmmss + ':00';
                                        swal("Good !", response.data.code.message, "success")

                                    } else {
                                        swal("Cancelled", response.data.code.message, "error");
                                    }
                                    //swal("Good job!", response.message, "success")
                                }, function (error) {
                                    alert('Error Occurred')
                                })
                            }, 200)
                        }
                        else {
                            swal("Cancelled", "Vehicle is not reported :)", "error");
                        }
                    });

            };


            $scope.unlodingDateTime = function (data) {
                $scope.index = $scope.vehicleTrackingReceived.indexOf(data);
                console.log('index', $scope.index);
                $scope.tripId = data.id;
                $scope.tripCompletedVehicleNo = data.vehicle_no;
                $scope.tripCompletedStartDateTime = data.trip_start_date_time;
                var date = new Date();
                $scope.ddMMyyyy = $filter('date')(new Date(), 'yyyy-MM-dd');
                console.log('$scope.ddMMyyyy', $scope.ddMMyyyy);
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');
                console.log('$scope.HHmmss', $scope.HHmmss);
            };

            $scope.unloading = function (ddMMyyyy, HHmmss) {
                swal({
                    title: "Are you sure?",
                    text: "You want to unload the vehicle !",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Yes, Unload the vehicle!",
                    cancelButtonText: "No, cancel !",
                    showLoaderOnConfirm: true,
                    closeOnConfirm: false,
                    closeOnCancel: false
                },
                    function (isConfirmed) {
                        if (isConfirmed) {
                            setTimeout(function () {
                                $scope.unloadingDateTimeTrip = {
                                    unloading_date: ddMMyyyy,
                                    unloading_time: HHmmss + ':00',
                                    id: $scope.tripId
                                };
                                console.log($scope.unloadingDateTimeTrip);
                                var idx = $scope.index;
                                trackingServices.unloadingDateTime($scope.unloadingDateTimeTrip).then(function (response) {
                                    console.log('response', response);
                                    if (response.data.code.status === 200) {
                                        $scope.vehicleTrackingReceived.splice(idx, 1);
                                        swal("Good !", response.data.code.message, "success")
                                    } else if (response.data.code.status === 300) {
                                        swal("oops !", response.data.code.message, "error")
                                    }
                                    else {
                                        swal("oops !", response.data.code.message, "error")
                                    }
                                }, function (error) {
                                    swal('Error Occurred')
                                })
                            }, 200)
                        }
                        else {
                            swal("Cancelled", "Vehicle is not unloaded :)", "error");
                        }
                    });
            };


            $scope.send_alert = function (send_alert) {
                console.log('send_alert', send_alert);
                $scope.driver_no = $rootScope.driver_number;
                console.log('route', route);
                console.log('no', $scope.driver_no);
                trackingServices.sendRouteAlert(route, $scope.driver_no).then(function (response) {
                    console.log('response', response);
                    if (response.data.code.status === 200) {
                        swal("Good !", response.data.code.message, "success")
                    } else if (response.data.code.status === 202) {
                        swal("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };


            /**
             * By Vishnu
             * @param vehicleNo
             * @param data_id
             */
            $scope.vehicleLocationHistory = {};
            $scope.getVehicleLocationHistory = function (vehicleNo, data_id) {
                trackingServices.getVehicleLocationHistoryService(vehicleNo, data_id).then(function (response) {
                    console.log(response);
                    if (response.code.status == 200) {
                        $scope.vehicleLocationHistory = response.current_locations;

                    }
                    else {
                        //Ashish
                        $scope.vehicleLocationHistory = [];
                        swal("Cancelled", response.code.message, "error")
                    }
                });
            };

            $scope.trackingTripvehicle = function (tripId, isMarket) {
                $scope.trip_id = tripId;
                $scope.is_market = isMarket;
                async.parallel([
                    function (callback) {
                        trackingServices.getVehicleStatus($scope.trip_id).then(function (response) {
                            console.log('1', response);
                            if (response.code.status == 200) {
                                try {
                                    $scope.vehicleLocation = response.track_data.current_location.location;
                                    $scope.vehiclePreviousHub = response.track_data.last_hub_data.hub_name;
                                    $scope.vehicleNextHub = response.track_data.next_hub_data.hub_name;
                                    $scope.vehicleStatus = response.track_data.trip_status.status_name;
                                }
                                catch (err) {
                                    $scope.vehicleLocation = 'Currently not available.';
                                    $scope.vehiclePreviousHub = response.track_data.last_hub_data.hub_name;
                                    $scope.vehicleNextHub = response.track_data.next_hub_data.hub_name;
                                    $scope.vehicleStatus = response.track_data.trip_status.status_name;
                                }

                                console.log('$scope.vehicleNextHub', $scope.vehicleNextHub)

                                callback();
                                //swal("Good job!", response.data.code.message, "success")
                            }
                            else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                    function (callback) {
                        trackingServices.getHubInfo($scope.trip_id).then(function (response) {
                            console.log('2', response);
                            if (response.code.status == 200) {
                                $scope.hubInfo = response.track_data.last_hub_data;

                                callback();
                                //swal("Good job!", response.data.code.message, "success")
                            }
                            else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                    function (callback) {
                        trackingServices.getStartEndPoint($scope.trip_id).then(function (response) {
                            console.log('3', response);
                            if (response.code.status == 200) {
                                $scope.StartEndPoint = response.data_list;

                                callback();
                                //swal("Good job!", response.data.code.message, "success")
                            }
                            else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                    function (callback) {
                        trackingServices.getUnScheduleStopage($scope.trip_id).then(function (response) {
                            console.log('4', response);
                            if (response.code.status == 200) {
                                $scope.unScheduleStopage = response.data_list;

                                callback();
                                //swal("Good job!", response.data.code.message, "success")
                            }
                            else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }
                ]);
            };


            $scope.unscheduleStoppagedeatils = function (tripId, isMarket, trip_code, vehicleTracking) {
                $scope.trip_id = tripId;
                $scope.is_market = isMarket;
                $scope.tripCodedata = trip_code;
                $scope.vehicleTracking= vehicleTracking;
                async.parallel([

                         function (callback) {
                        trackingServices.getStoppageAlertNew($scope.tripCodedata).then(function (response) {
                            if (response.code == 202) {
                                $scope.StoppageAlertWithReason = response.details;
                                data = $scope.delayData = response.details;
                                data = $scope.delayData = response.details;
                                function create_duration_str(data){

                                    var totaltime;
                                    var t=0,h,m,d,h,b=0,k=0;
                                    data.forEach(function (value, key) {
                                        if (totaltime !== undefined) {
                                            totaltime= totaltime+" " + value.duration;
                                            const arr=totaltime.split(" ")
                                             d=0
                                             h=0
                                             m=0

                                             for (let i in arr){
                                               if (arr[i]=='days'){
                                                     w=parseInt(arr[i-1])
                                                     d=d+w
                                                     t=d*24*3600;}
                                               else if (arr[i]=='hours'){
                                                     w=parseInt(arr[i-1])
                                                     h=h+w
                                                     k=h*3600;}

                                               else if (arr[i]=='minutes'){
                                                     w=parseInt(arr[i-1])
                                                     m=m+w
                                                     b=m*60;}}
                                             var p= t+k+b // total seconds
                                             var l = p/(3600);//total hours with decimal
                                             p= p%3600
                                             var o = p /60; // total minutes
                                             function float2int (value) {
                                                return value | 0;
                                            }
                                            e=float2int(l);
                                            console.log(e)
                                            console.log(o)
                                            if (e> 0){

                                                totaltime = (e)+ (" hours ")+(o)+(" minutes ");}

                                            else {
                                                totaltime = (o)+(" minutes");
                                                }

                                                }
                                        else {
                                            totaltime = value.duration;
                                        }})

                                    return totaltime}
                                    $scope.final_delay = create_duration_str(data)
                                callback();
                                //swal("Good job!", response.data.code.message, "success")
                            }
                            else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                ]);
            };

            //Ashish
            $scope.vehicleGraph = function (vehicleNo, data_id) {
                trackingServices.get_chart_data(vehicleNo, data_id).then(function (response) {
                    console.log('response', response);
                    if (response.code.status == 200) {
                        $scope.min_date = response.chart_data.date;
                        $scope.mileage = response.chart_data.mileage_list;
                        $scope.location = response.chart_data.location_list;
                        $scope.fuel_reading = response.chart_data.fuel_reading_list;
                        $scope.max = response.chart_data.fuel_max;
                        $scope.min = response.chart_data.fuel_min;
                        $scope.fuel_consumed = $scope.fuel_reading[0] - $scope.fuel_reading[$scope.fuel_reading.length - 1];
                        $scope.vehicle_avg = (response.chart_data.distance_travelled / ($scope.fuel_consumed * 1000)).toFixed(2);
                        $scope.fuel_consumed = $scope.fuel_consumed.toFixed(2);
                        console.log('$scope.fuel_consumed', $scope.fuel_consumed, $scope.vehicle_avg);
                        var myConfig =
                        {
                            "type": "line",
                            "utc": true,
                            "title": {
                                "text": "",
                                "font-size": "24px",
                                "adjust-layout": true
                            },
                            "plotarea": {
                                "margin": "dynamic 45 60 dynamic"
                            },

                            "legend": {
                                "layout": "float",
                                "background-color": "none",
                                "border-width": 0,
                                "shadow": 0,
                                "align": "center",
                                "adjust-layout": true,
                                "item": {
                                    "padding": 7,
                                    "marginRight": 17,
                                    "cursor": "hand"
                                }
                            },
                            "scroll-x": {},
                            "scale-x": {
                                "zooming": true,
                                "min-value": $scope.min_date,
                                "shadow": 0,
                                "step": 300000,
                                "transform": {
                                    "type": "date",
                                    "all": "%D, %d %M<br />%h:%i %A",
                                    "guide": {
                                        "visible": false
                                    },
                                    "item": {
                                        "visible": false
                                    }
                                },
                                "label": {
                                    "visible": false
                                },
                                "minor-ticks": 0
                            },
                            "scale-y": {
                                "values": "0:20:5",
                                "line-color": "#f6f7f8",
                                "shadow": 0,
                                "guide": {
                                    "line-style": "dashed"
                                },
                                "label": {
                                    "text": "MILEAGE"
                                },
                                "minor-ticks": 0,
                                "thousands-separator": ","
                            },
                            "crosshair-x": {
                                "line-color": "#efefef",
                                "plot-label": {
                                    "border-radius": "5px",
                                    "border-width": "1px",
                                    "border-color": "#f6f7f8",
                                    "padding": "10px",
                                    "font-weight": "bold"
                                },
                                "scale-label": {
                                    "font-color": "#000",
                                    "background-color": "#f6f7f8",
                                    "border-radius": "5px"
                                }
                            },
                            "tooltip": {
                                "visible": true,
                                "text": "%data-location"
                            },
                            "plot": {
                                "highlight": false,
                                "tooltip-text": "%t views: %v<br>%k",
                                "shadow": 0,
                                "line-width": "2px",
                                "marker": {
                                    "type": "circle",
                                    "size": 3
                                },
                                "highlight-state": {
                                    "line-width": 3
                                },
                                "animation": {
                                    "effect": 1,
                                    "sequence": 2,
                                    "speed": 100
                                }
                            },
                            "series": [
                                {
                                    "values": $scope.mileage,
                                    "data-location": $scope.location,
                                    "text": "Mileage",
                                    "line-color": "#007790",
                                    "legend-item": {
                                        "background-color": "#007790",
                                        "borderRadius": 5,
                                        "font-color": "white"
                                    },
                                    "legend-marker": {
                                        "visible": false
                                    },
                                    "marker": {
                                        "background-color": "#007790",
                                        "border-width": 1,
                                        "shadow": 0,
                                        "border-color": "#69dbf1"
                                    },
                                    "highlight-marker": {
                                        "size": 6,
                                        "background-color": "#007790"
                                    }
                                }
                            ]
                        };

                        var myConfig1 =
                        {
                            "type": "line",
                            "utc": true,
                            "title": {
                                "text": "",
                                "font-size": "24px",
                                "adjust-layout": true
                            },
                            "plotarea": {
                                "margin": "dynamic 45 60 dynamic"
                            },

                            "legend": {
                                "layout": "float",
                                "background-color": "none",
                                "border-width": 0,
                                "shadow": 0,
                                "align": "center",
                                "adjust-layout": true,
                                "item": {
                                    "padding": 7,
                                    "marginRight": 17,
                                    "cursor": "hand"
                                }
                            },
                            "scroll-x": {},
                            "scale-x": {
                                "zooming": true,
                                "min-value": $scope.min_date,
                                "shadow": 0,
                                "step": 300000,
                                "transform": {
                                    "type": "date",
                                    "all": "%D, %d %M<br />%h:%i %A",
                                    "guide": {
                                        "visible": false
                                    },
                                    "item": {
                                        "visible": false
                                    }
                                },
                                "label": {
                                    "visible": false
                                },
                                "minor-ticks": 0
                            },
                            "scale-y": {
                                "values": $scope.min + ":" + $scope.max + ":10",
                                "line-color": "#f6f7f8",
                                "shadow": 0,
                                "guide": {
                                    "line-style": "dashed"
                                },
                                "label": {
                                    "text": "FUEL READING"
                                },
                                "minor-ticks": 0,
                                "thousands-separator": ","
                            },
                            "crosshair-x": {
                                "line-color": "#efefef",
                                "plot-label": {
                                    "border-radius": "5px",
                                    "border-width": "1px",
                                    "border-color": "#f6f7f8",
                                    "padding": "10px",
                                    "font-weight": "bold"
                                },
                                "scale-label": {
                                    "font-color": "#000",
                                    "background-color": "#f6f7f8",
                                    "border-radius": "5px"
                                }
                            },
                            "tooltip": {
                                "visible": true,
                                "text": "%data-location"
                            },
                            "plot": {
                                "highlight": false,
                                "tooltip-text": "%t views: %v<br>%k",
                                "shadow": 0,
                                "line-width": "2px",
                                "marker": {
                                    "type": "circle",
                                    "size": 3
                                },
                                "highlight-state": {
                                    "line-width": 3
                                },
                                "animation": {
                                    "effect": 1,
                                    "sequence": 2,
                                    "speed": 100
                                }
                            },
                            "series": [
                                {
                                    "values": $scope.fuel_reading,
                                    "data-location": $scope.location,
                                    "text": "Fuel Reading",
                                    "line-color": "#007790",
                                    "legend-item": {
                                        "background-color": "#007790",
                                        "borderRadius": 5,
                                        "font-color": "white"
                                    },
                                    "legend-marker": {
                                        "visible": false
                                    },
                                    "marker": {
                                        "background-color": "#007790",
                                        "border-width": 1,
                                        "shadow": 0,
                                        "border-color": "#69dbf1"
                                    },
                                    "highlight-marker": {
                                        "size": 6,
                                        "background-color": "#007790"
                                    }
                                }
                            ]
                        };

                        zingchart.render({
                            id: 'myChart',
                            data: myConfig,
                            height: '99.99%',
                            width: '99.99%'
                        });

                        zingchart.render({
                            id: 'myChart1',
                            data: myConfig1,
                            height: '99.99%',
                            width: '99.99%'
                        });
                    }
                    else {
                        $('#lineChar').modal('hide');
                        swal("Oops", response.code.message, "error")
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });


            };

            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.vehicleTrackingReceived, query).length;
                });
            };

            $scope.getMultiplePoint = function (data) {
                console.log(data);
                var coordinate_str = '';
                var vehicleTrackingTouchPoints = data;
                $scope.vehicleTrackingTouchPoints = '';
                console.log('vehicleTrackingTouchPoints', vehicleTrackingTouchPoints);
                angular.forEach(vehicleTrackingTouchPoints.order_data.multiple_stopage, function (value, key) {
                    console.log(value, key);
                    try {
                        coordinate_str = value.destination_point.location_point.coordinates.coordinates[1].toString() + ',' + value.destination_point.location_point.coordinates.coordinates[0].toString();
                        vehicleTrackingTouchPoints.order_data.multiple_stopage[key]['coordinates'] = coordinate_str
                    }
                    catch (err) {
                        coordinate_str = value.destination_point.location_point.coordinates[1].toString() + ',' + value.destination_point.location_point.coordinates[0].toString();
                        vehicleTrackingTouchPoints.order_data.multiple_stopage[key]['coordinates'] = coordinate_str
                    }
                });
                $scope.vehicleTrackingTouchPoints = vehicleTrackingTouchPoints;
            };

            $scope.loading_points = function (data, contract_id) {
                console.log('loading_points', data, contract_id);
                $scope.contract_lp = data;
                $scope.contract_id = contract_id;
                if ($scope.contract_lp.length >= 1) {
                    $scope.active = true;
                    $scope.added = false;
                }
                else {
                    $scope.added = true;
                }
            };

            $scope.driverReg = {};

            $scope.addDriver = function (trip_id) {
                $scope.driverReg = {};
                $scope.driverReg.trip_id = trip_id;
                trackingServices.getNewDriver(trip_id).then(function (response) {
                    console.log('response', response);
                    if (response.code.status === 200) {
                        $scope.driverReg.license_number1 = response.driver_data.d1.l1;
                        $scope.driverReg.pilot_phone1 = response.driver_data.d1.p1;
                        $scope.driverReg.pilot_name1 = response.driver_data.d1.n1;
                        $scope.driverReg.license_number2 = response.driver_data.d2.l2;
                        $scope.driverReg.pilot_phone2 = response.driver_data.d2.p2;
                        $scope.driverReg.pilot_name2 = response.driver_data.d2.n2;
                    } else {
                        swal("oops !", response.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            $scope.add_driver = function (data, pilotRegForm) {
                console.log('driverReg', data);
                $scope.pilotRegForm = pilotRegForm;
                if (!$scope.pilotRegForm.$invalid) {
                    if (data.pilot_phone2 || data.pilot_name2) {
                        if (!data.license_number2) {
                            swal("Please Fill Licence number.", "", "error");
                            return
                        }
                    }
                    trackingServices.updateDriver(data).then(function (response) {
                        console.log('response', response);
                        if (response.code.status === 200) {
                            $('#add_driver').modal('hide');
                            swal("Good!", response.code.message, "success");
                        } else {
                            swal("oops !", response.code.message, "error")
                        }
                    }, function (error) {
                        alert('Error Occurred')
                    })
                }
                else {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                }
            };

            $scope.contract_lp = [{ name: '', lat: '', lng: '' }];
            $scope.addMoreLp = function () {
                if ($scope.contract_lp.length >= 1) {
                    $scope.active = true;
                }
                $scope.added = false;
                $scope.contract_lp.push({ name: '', lat: '', lng: '' });
            };

            $scope.rLp = function (index, loading_point) {
                swal({
                    title: "Are you sure?",
                    text: "You want to delete data !",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Yes, delete it!",
                    cancelButtonText: "No, cancel !",
                    closeOnConfirm: false,
                    closeOnCancel: false
                },
                    function (isConfirm) {
                        if (isConfirm) {
                            loading_point.contract_id = $scope.contract_id;
                            trackingServices.removeLoadPoints(loading_point).then(function (response) {
                                console.log('response', response);
                                if (response.data.code.status === 200) {
                                    $scope.contract_lp.splice(index, 1);
                                    if ($scope.contract_lp.length < 1) {
                                        $scope.active = false;
                                        $scope.added = true;
                                    }
                                    swal("Good !", response.data.code.message, "success");
                                } else if (response.data.code.status === 202) {
                                    swal("oops !", response.data.code.message, "error")
                                }
                            }, function (error) {
                                alert('Error Occurred')
                            })
                        }
                        else {
                            swal("Not cancelled!", "", "success");
                        }
                    });
            };

            $scope.updateLoadingPoints = function (data, loadingPointsForm) {
                $scope.loadingPointsForm = loadingPointsForm;
                if (!$scope.loadingPointsForm.$invalid) {
                    $scope.loadingBtn = true;
                    data[0].contract_id = $scope.contract_id;
                    trackingServices.updateLoadPoints(data).then(function (response) {
                        $scope.loadingBtn = false;
                        $('#loading_points').modal('hide');
                        console.log('response', response);
                        if (response.data.code.status === 200) {
                            swal("Good !", response.data.code.message, "success");
                        } else {
                            $scope.contract_lp.splice($scope.contract_lp.length - 1, 1);
                            swal("oops !", response.data.code.message, "error")
                        }
                    }, function (error) {
                        alert('Error Occurred')
                    })
                }
                else {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                }
            };

            $scope.updateMultipleStoppage = function (data) {
                console.log('gdfg', data);
                $scope.loadingBtn = true;
                trackingServices.updateMultiPoints(data).then(function (response) {
                    $scope.loadingBtn = false;
                    $('#touchPoints').modal('hide');
                    console.log('response', response);
                    if (response.data.code.status === 200) {
                        swal("Good !", response.data.code.message, "success");
                        $scope.vehicleTrackingFilterAfterContractChange($scope.pagination.current, $scope.filterDataObject);
                    } else {
                        swal("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            }

            $scope.driverModalData = {
                trip_id: '',
                new_number: '',
                old_number: ''
            }
        
            $scope.setModalData = function(tripData){
                $scope.selectedItem = tripData;
                $scope.driverModalData = {
                    trip_id: tripData.id,
                    old_number: tripData.order_data.driver_no,
                    new_number: '',
                }
            }

            $scope.filterValueDigits = function ($event) {
                if (isNaN(String.fromCharCode($event.keyCode))) {
                    $event.preventDefault();
                }
            };
        
            $scope.changeDriverMobileNumber = function() {
                const payload = {
                    trip_id: $scope.driverModalData.trip_id,
                    new_number: $scope.driverModalData.new_number,
                }
                var index = $scope.vehicleTrackingReceived.indexOf($scope.selectedItem);
                $scope.loading = true;
                trackingServices.changeDriverMobileNumber(payload).then(function(response) {
                    if(response){
                        if (response.data.code.status === 200) {
                            $scope.vehicleTrackingReceived[index].order_data.driver_no = $scope.driverModalData.new_number;
                            angular.element('#updateDriverPhone').modal('hide');
                            swal("Good !", response.data.code.message, "success");
                        } else {
                            swal("oops !", response.data.code.message, "error")
                        }
                        $scope.loading = false;
                    }
                }, function(error){
                    $scope.loading = false;
                    swal("Oops!", 'Connection timeout.', "error");
                })
            }


        }
    ])

    .controller('placementAssurance.listing', ['$scope', '$state', '$rootScope', '$location', 'trackingServices', '$sce', '$cookies', '$filter', function ($scope, $state, $rootScope, $location, trackingServices, $sce, $cookies, $filter) {
        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Data Connection Lost")
            }

        });
        $scope.pod = {};
        $scope.assignOrderList = function () {
            $scope.loading = true;
            $scope.asyncRequestForFilter();
            $scope.searchText = '';
            trackingServices.getAssignOrderList().then(function (response) {
                console.log(response)
                $scope.loading = false;
                if (response.data.results.code.status == 200) {
                    $scope.orderAssigns = response.data.results.trip_data;
                }
            }, function (err) {
                alert("Error Occurred!")
            })
        };

        $scope.showDLPicture = function (dl_pic) {
            if (pic) {
                trackingServices.picLoadServerT(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            }
            else {

                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic;
            }
        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url
                });
            }
            else {


                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic;
            }
        };
        $scope.showRCPicture = function (rc_pic) {
            if (pic) {
                trackingServices.picLoadServerT(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url
                });
            }
            else {


                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic;
            }
        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url
                });
            }
            else {

                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic;
            }
        };
        $scope.showLRPicture = function (lr_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url
                });
            }
            else {


                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic;
            }
        };
        $scope.showDriverWithVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url
                });
            }
            else {

                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic;

            }
        };
        $scope.showLrNumPicture = function (lr_num_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url
                });
            }
            else {


                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic;
            }
        };
        $scope.showLrNumberPicture = function (lr_num_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url
                });
            }
            else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }

        };

        /**
         * Prafull
         * @param orderAssign
         */
        $scope.driverDetailsPlacement = function (orderAssign) {
            trackingServices.driverOnlyDataList(orderAssign.id).then(function (response) {
                console.log('1', response);
                if (response.data.code.status == 200) {

                    $scope.driver = response.data.data_list
                }
                else {
                    swal("Cancelled", response.code.message, "error")
                }
            }
            );
        };

        $scope.vehicleDetailsPlacement = function (orderAssign) {
            trackingServices.vehicleDetailsList(orderAssign.id).then(function (response) {
                console.log('2', response);
                if (response.data.code.status == 200) {
                    $scope.vehicle = response.data.data_list;

                }
                else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };


        $scope.getAssignOrderAlertHistory = function (order_id) {
            $scope.orderID = order_id;
            trackingServices.assignOrderAlertHistory(order_id).then(function (response) {
                console.log('alertHistory', response)
                if (response.code.status === 200) {

                    $scope.alertHistory = response.alert;
                    console.log('$scope.alertHistory', $scope.alertHistory)
                } else {
                    swal("oops!", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.source_name = $cookies.get('username');
        $scope.txtcomment = { name: '' };
        $scope.checkHistory = function (orderID, txtcomment) {

            var date = new Date();
            $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');

            $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');

            $scope.alertHistory = $scope.alertHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';
            $scope.txtcomment = { name: '' };

            $scope.alertHistorys = {
                order_id: orderID,
                assign_order_alert_history: $scope.alertHistory
            };

            trackingServices.postCheckHistory($scope.alertHistorys).then(function (response) {
                console.log(response)
                if (response.data.code.status == 200) {

                }
                else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.toTrusted = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
        };

        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.orderAssigns, query).length;
            });
        };

        $scope.getAllOrderIdsLists = function (value) {
            trackingServices.getAllOrderIdsLists(value).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.order_ids = response.data.order_ids;
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No Internet connection.', 'error')
            });
        }

        //Mukesh Gautam
        $scope.asyncRequestForFilter = function () {
            async.parallel([

                //function (callback) {
                //    trackingServices.getAllOrderIdsLists().then(function(response){
                //        console.log('response',response)
                //        if (response.data.code.status === 200){
                //            $scope.order_ids = response.data.order_ids;
                //            callback();
                //        }else{
                //            alert(response.data.code.message)
                //        }
                //    },function(err){
                //        swal("Oops", 'No Internet connection.','error')
                //    });
                //},

                function (callback) {
                    //request no 2
                    trackingServices.getAllCustomerLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.customerName = response.data.customer;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 3
                    trackingServices.getAllLocationLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.locationNmae = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 4
                    trackingServices.getAllVehicleLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleNo = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }
            ])
        };

        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            trackingServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};

        $scope.pagination = {};
        $scope.filterData = {
            order_id: '',
            customer: '',
            origin: '',
            destination: '',
            vehicle_no: ''
        };

        $scope.placementAccurance = {};
        $scope.placementAssuranceFilter = function (count, filterData) {
            $scope.loading = true;
            $scope.order_id = '';
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.customer_id = '';

            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

            if (filterData.order_id)
                $scope.order_id = filterData.order_id.order_id;

            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;

            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;

            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;

            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                order_id: $scope.order_id,
            };
            trackingServices.placementAssuranceService(count, $scope.filterDataObject).then(function (response) {
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {
                    $scope.placementAccurance.count = response.data.count;
                    $scope.orderAssigns = response.data.results.trip_data;
                    $scope.wf = response.data.results.wf;

                } else {
                    alert(response.data.results.code.message)
                }
            })
        };

        $scope.clearFilterAssurance = function () {
            $scope.filterData = {};
            $scope.pagination.current = 1;
            $scope.assignOrderList();
        };


    }])

    .controller('completed.listing', ['$scope', '$state', '$rootScope', 'trackingServices', '$location', '$filter',
        function ($scope, $state, $rootScope, trackingServices, $location, $filter) {
            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            //Ashish
            $scope.getAdvancePayDetails = function (order_code) {
                trackingServices.advancePayDetails(order_code).then(function (response) {
                    console.log('response_here', response);
                    if (response.data.code.status == 200) {
                        $scope.broker_rate = response.data.details.broker_rate;
                        $scope.cash_adv = response.data.details.cash_adv;
                        $scope.broker_advance = response.data.details.broker_advance;
                        $scope.balance_paid = response.data.details.payment_due;
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            //Ashish
            $scope.markIdeal = function (trip_id) {
                trackingServices.markIdealTrip(trip_id).then(function (response) {
                    console.log('response_makIdeal', response);
                    if (response.data.code.status == 200) {
                        swal("Marked!", "", "success")
                    }
                    else {
                        swal("Not Marked!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.getOwnPayDetails = function (order_code) {
                trackingServices.ownAdvancePayDetails(order_code).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.ownAdvanceData = response.data.details;
                        $scope.ownAdvanceData.totalEntry = 0;
                        $scope.ownAdvanceData.totalFuel = 0;
                        $scope.ownAdvanceData.totalMeals = 0;
                        $scope.ownAdvanceData.totalPolice = 0;
                        $scope.ownAdvanceData.totalToll = 0;
                        $scope.ownAdvanceData.totalAdvance = 0;
                        $scope.ownAdvanceData.totalExpenses = 0;
                        angular.forEach($scope.ownAdvanceData, function (value) {
                            console.log('value', value);
                            if (value.advance_data.advance_for_entry != '')
                                $scope.ownAdvanceData.totalEntry += parseInt(value.advance_data.advance_for_entry);
                            if (value.advance_data.advance_for_fuel != '')
                                $scope.ownAdvanceData.totalFuel += parseInt(value.advance_data.advance_for_fuel);
                            if (value.advance_data.advance_for_meals != '')
                                $scope.ownAdvanceData.totalMeals += parseInt(value.advance_data.advance_for_meals);
                            if (value.advance_data.advance_for_police != '')
                                $scope.ownAdvanceData.totalPolice += parseInt(value.advance_data.advance_for_police);
                            if (value.advance_data.total_advance != '')
                                $scope.ownAdvanceData.totalAdvance += parseInt(value.advance_data.total_advance);
                            if (value.actual_data.total_actual != '')
                                $scope.ownAdvanceData.totalExpenses += parseInt(value.actual_data.total_actual);
                            if (value.advance_data.advance_for_toll != '')
                                $scope.ownAdvanceData.totalToll += parseInt(value.advance_data.advance_for_toll);

                        });
                        console.log('$scope.ownAdvanceData', $scope.ownAdvanceData)
                    }
                    else {
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.showDlPicture = function (dl_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(dl_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDlPic = response.data.url
                    });
                }
                else {
                    $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
                }

            };
            $scope.showInsuPicture = function (insurance_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                        // console.log(response)
                        $scope.showInsurancePic = response.data.url
                    });
                }
                else {


                    $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
                }
            };
            $scope.showRcPicture = function (rc_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(rc_pic).then(function (response) {

                        // console.log(response)
                        $scope.showRcPic = response.data.url
                    });
                }
                else {


                    $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
                }
            };
            $scope.showFitPicture = function (fitness_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                        // console.log(response)
                        $scope.showFitnessPic = response.data.url
                    });
                }
                else {


                    $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
                }
            };
            $scope.showLrPicture = function (lr_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrPic = response.data.url
                    });
                }
                else {
                    $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
                }

            };
            $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverVehiclePic = response.data.url
                    });
                }
                else {


                    $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
                }
            };
            $scope.showLrNumPicture = function (lr_num_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                }
                else {


                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }
            };
            $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehi_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverAndVehiclePic = response.data.url
                    });
                }
                else {


                    $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic

                }
            };
            $scope.showLrNumberPicture = function (lr_num_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                }
                else {
                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }


            };
            $scope.showPermitPicture = function (permit_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(permit_pic).then(function (response) {

                        // console.log(response)
                        $scope.showPermitPic = response.data.url
                    });
                }
                else {

                    $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
                }
            };

            // Mukesh
            /**********************************Driver Details Get**********8*************************************/

            $scope.driverDetailsTracking = function (orderAssign) {
                trackingServices.driverDataTracking(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                        console.log('driver', $scope.driver)
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };

            /****************************** Vehicle Data Get*******************************************************/

            $scope.vehicleDetailsTracking = function (vehicleData) {
                trackingServices.vehicleDataTracking(vehicleData.id).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.vehicle = response.data.data_list;
                        console.log('vehicle', $scope.vehicle)
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            /**
             * functionName:completedTrip_listing
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 21/07/2017
             * testerName:
             * testDate:
             */

            $scope.completedTripPage = { 'next': '', 'previous': '', 'count': '' };
            $scope.completedTrip_listing = function (count) {
                $scope.loading = true;
                trackingServices.completedTrip(count).then(function (response) {
                    console.log('reponse_here', response);
                    $scope.loading = false;
                    if (response.results.code.status === 200) {
                        $scope.completedTrips = response.results.trip_data;
                        $scope.completedTripPage.count = response.count;
                        $scope.wf = response.results.wf;

                        for (var i = 0; i < $scope.completedTrips.length; i++) {
                            var start_date_time = new Date($scope.completedTrips[i].trip_start_date.split('T')[0] + " " + $scope.completedTrips[i].trip_start_time.split('T')[1]);
                            var end_date_time = new Date($scope.completedTrips[i].trip_end_date + " " + $scope.completedTrips[i].trip_end_time);
                            diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
                            diff /= 60;
                            num = Math.abs(Math.round(diff));
                            var hours = (num / 60);
                            var rhours = Math.floor(hours);
                            var minutes = (hours - rhours) * 60;
                            var rminutes = Math.round(minutes);
                            //num + " minutes = " + rhours + " hour(s) and   " + rminutes + " minute(s).";


                            //var start_date_time = new Date($scope.completedTrips[i].trip_start_date);

                            //var total_hours = Math.floor(((+end_date_time) - (+start_date_time)) / (1000 * 3600));
                            //var total_mins = Math.floor((((+end_date_time) - (+start_date_time)) % 3600) / 60);
                            $scope.completedTrips[i].total_time = rhours + "hr " + rminutes + "min";
                        }
                        $scope.asyncRequestForFilter();
                    } else {
                        alert(response.code.message)
                    }
                })
            };

            $scope.podData = function (completedTrip) {
                $scope.tripCompleted = completedTrip;
                console.log('$scope.tripCompleted', $scope.tripCompleted)
            };

            $scope.podEdit = function (tripCompleted) {
                trackingServices.putPodEdit(tripCompleted).then(function (response) {
                    console.log(response)
                    if (response.data.code.status == 200) {
                        swal("Good job!", response.data.code.message, "success")
                        $state.go('broker-list')
                    }
                    else {
                        swal("Cancelled", response.data.code.message, "error");
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            /**
             *Filter
             *Prafull
             */

            $scope.asyncRequestForFilter = function () {
                //Write here function for async request
                async.parallel([
                    // function (callback) {
                    //     // request no 1
                    //     trackingServices.getAllLrLists().then(function (response) {
                    //
                    //         if (response.data.results.code.status === 200) {
                    //             $scope.lrName = response.data.results.data_list;
                    //             callback();
                    //         } else {
                    //             alert(response.data.results.code.message)
                    //         }
                    //     }, function (err) {
                    //         swal("Oops", 'No internet connection.', "error")
                    //     });
                    // },

                    function (callback) {
                        //request no 2
                        trackingServices.getAllCustomerLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.customerName = response.data.customer;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 3
                        trackingServices.getAllLocationLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.locationNmae = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 4
                        trackingServices.getAllBrokerList().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.brokerName = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 5
                        trackingServices.getAllVehicleLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.vehicleNo = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }

                ])
            };

            $scope.getLrNumbers = function (data) {
                if (data.length > 3) {
                    var lr = {
                        'lr_num': data
                    };
                    trackingServices.getLrNumSerTrc(lr).then(function (response) {
                        console.log('response', response)
                        if (response.data.code.status === 200) {
                            $scope.lrName = response.data.lr;
                        }
                        else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                }
            };


            /**
             * functionName:getLocation
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 07/02/2018
             * testerName:
             * testDate:
             */
            $scope.getLocation = function (customerName) {
                //$scope.billModel.origin_location = '';
                //$scope.billModel.destination_location = '';
                trackingServices.getLocationLists(customerName).then(function (response) {
                    if (response.data.code.status === 200) {
                        //$scope.locations = response.data.location;
                        $scope.locationNmae = response.data.location;
                        //angular.forEach($scope.locationNmae, function (value, key) {
                        //    $scope.locationNmae[key].location_id = value.id;
                        //});
                        //console.log('new',$scope.locationNmae)
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            };


            //$scope.distLocation = function (location) {
            //    angular.forEach($scope.locationNmae, function (value, key) {
            //        if (value.location_name == location.location_name)
            //            $scope.locationNmae.splice(key, 1);
            //    });
            //};


            $scope.clearFilter = function () {
                //Ashish
                $scope.pagination.current = 1;
                $scope.searchText = '';
                $scope.completedTrip_listing();
                $scope.filterData = {
                    lr_no: '',
                    customer: '',
                    origin: '',
                    destination: '',
                    broker: '',
                    date_vehicle_required: '',
                    to_date_vehicle_required: '',
                    vehicle_no: ''
                };
            };
            /**
             * functionName:completedTripFilter
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 21/07/2017
             * testerName:
             * testDate:
             */
            $scope.pagination = {};
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                vehicle_no: ''
            };

            $scope.completedTripFilter = function (count, filterData) {
                $scope.loading = true;
                $scope.vehicle_no = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';
                $scope.lr_no = '';
                $scope.broker_id = '';
                $scope.customer_id = '';
                $scope.distance = '';


                if (filterData.distance) {
                    var d = filterData.distance;
                    d = d.split('-');
                    if (d.length == 2) {
                        if (parseInt(d[0]) > parseInt(d[1])) {
                            $scope.loading = false;
                            swal("Oops", 'Range should be in increasing order.', "error");
                            return
                        }
                    }
                    else {
                        $scope.loading = false;
                        swal("Oops", 'Valid format is 2000-3000', "error");
                        return
                    }
                    $scope.distance = filterData.distance;
                }

                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required,
                    lr: $scope.lr_no,
                    distance: $scope.distance,
                    broker: $scope.broker_id
                };
                //Ashish
                $scope.completedTripFilterPage = { 'next': '', 'previous': '', 'count': '' };
                $scope.completedTrips = '';
                $scope.searchText = '';
                trackingServices.completedTripFilterService(count, $scope.filterDataObject).then(function (response) {
                    console.log('response', response);
                    $scope.loading = false;
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    if (response.data.results.code.status === 200) {

                        $scope.completedTrips = response.data.results.trip_data;
                        $scope.completedTripPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                        for (var i = 0; i < $scope.completedTrips.length; i++) {
                            var start_date_time = new Date($scope.completedTrips[i].trip_start_date.split('T')[0] + " " + $scope.completedTrips[i].trip_start_time.split('T')[1]);
                            var end_date_time = new Date($scope.completedTrips[i].trip_end_date + " " + $scope.completedTrips[i].trip_end_time);
                            diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
                            diff /= 60;
                            num = Math.abs(Math.round(diff));
                            var hours = (num / 60);
                            var rhours = Math.floor(hours);
                            var minutes = (hours - rhours) * 60;
                            var rminutes = Math.round(minutes);
                            //num + " minutes = " + rhours + " hour(s) and   " + rminutes + " minute(s).";


                            //var start_date_time = new Date($scope.completedTrips[i].trip_start_date);

                            //var total_hours = Math.floor(((+end_date_time) - (+start_date_time)) / (1000 * 3600));
                            //var total_mins = Math.floor((((+end_date_time) - (+start_date_time)) % 3600) / 60);
                            $scope.completedTrips[i].total_time = rhours + "hr " + rminutes + "min";
                        }
                    } else {
                        alert(response.data.results.code.message)
                    }
                });
            };
            $scope.sortColumn = '';
            $scope.reverseSort = false;
            $scope.sortData = function (column) {
                $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
                $scope.sortColumn = column
            };

            $scope.getSortClass = function (column) {
                if ($scope.sortColumn == column) {
                    //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                    return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
                } else {
                    return 'fa fa-sort'
                }
            };
            $scope.completedExportData = function (filterData) {
                $scope.vehicle_no = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';
                $scope.lr_no = '';
                $scope.broker_id = '';
                $scope.customer_id = '';

                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required,
                    lr: $scope.lr_no,
                    broker: $scope.broker_id
                };
                $scope.csvloading = true;

                var generate_completed_trip_csv = function (completedTripExport) {
                    $scope.csvloading = false;
                    $scope.completeExport = completedTripExport;
                    $scope.downloadCompletedCsv = [];

                    angular.forEach($scope.completeExport, function (value, key) {
                        $scope.lr_no = "";
                        angular.forEach(value.lr.lr_data, function (value, key) {
                            $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                        });

                        if (value.trip_end_date === null) {
                            $scope.trip_end_date = ''
                        } else {
                            $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                        }
                        if (value.is_market === true) {
                            $scope.market = 'yes'
                        } else {
                            $scope.market = 'no'
                        }
                        if (value.lane.lane_code === undefined) {
                            $scope.route = ''
                        } else {
                            $scope.route = value.lane.lane_code
                        }
                        try {
                            var start_date_time = new Date(value.trip_start_date.split('T')[0] + " " + value.trip_start_time.split('T')[1]);
                            var end_date_time = new Date(value.trip_end_date + " " + value.trip_end_time);
                            diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
                            diff /= 60;
                            num = Math.abs(Math.round(diff));
                            var hours = (num / 60);
                            var rhours = Math.floor(hours);
                            var minutes = (hours - rhours) * 60;
                            var rminutes = Math.round(minutes);
                            var total_time = rhours + "hr " + rminutes + "min";
                        }
                        catch (err) {
                            total_time = ''
                        }

                        $scope.completedExportValue = {
                            "Trip Code": value.trip_code,
                            "Market": $scope.market,
                            "Lr No": $scope.lr_no,
                            "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                            "Origin": value.order_data.origin.location_name,
                            "Destination": value.order_data.destination.location_name,
                            "Driver Nname": value.order_data.driver_name + '/' + value.order_data.driver_no,
                            "Vehicle No": value.order_data.vehicle_no,
                            "Route": $scope.route,
                            "Trip Status": value.trip_status.status_name,
                            "Placement Date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
                            "Start Date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
                            "Reporting Date": $scope.trip_end_date,
                            'Total Time': total_time,
                            "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
                        };
                        $scope.downloadCompletedCsv.push($scope.completedExportValue)
                    });
                    var mystyle = {
                        headers: true,
                        column: { style: { Font: { Bold: "1" } } }
                    };
                    alasql('SELECT * INTO XLS("Completed Trips.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'placement_date')]);

                };
                var num_of_pages = 1;
                var completedTripExport = [];
                var recur_completed_trip = function () {
                    trackingServices.completedTripFilterService(num_of_pages, $scope.filterDataObject).then(function (response) {
                        if (response.data.results.code.status === 200) {
                            var count = response.data.count;
                            var total_pages = Math.ceil(count / response.data.pagesize);
                            num_of_pages = num_of_pages + 1;
                            if (num_of_pages <= total_pages) {
                                completedTripExport = completedTripExport.concat(response.data.results.trip_data);
                                recur_completed_trip()
                            }
                            else {
                                completedTripExport = completedTripExport.concat(response.data.results.trip_data);
                                generate_completed_trip_csv(completedTripExport)
                            }
                            console.log('count', count);
                            $scope.progress_bar_per = ((completedTripExport.length / count) * 100).toFixed(2);
                        }
                        else {
                            swal("Cancelled", response.data.results.code.message, "error")
                        }

                    });


                };
                recur_completed_trip();
                // recur_completed_trip()
                // trackingServices.postCompleteExportData($scope.filterDataObject).then(function (response) {
                //     $scope.csvloading = false;
                //     if (response.data.code.status === 200) {
                //         $scope.completeExport = response.data.trip_data;
                //         $scope.downloadCompletedCsv = [];
                //
                //         angular.forEach($scope.completeExport, function (value, key) {
                //             $scope.lr_no = "";
                //             angular.forEach(value.lr.lr_data, function (value, key) {
                //                 $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                //             });
                //
                //             if (value.trip_end_date === null) {
                //                 $scope.trip_end_date = ''
                //             } else {
                //                 $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                //             }
                //             if (value.is_market === true) {
                //                 $scope.market = 'yes'
                //             } else {
                //                 $scope.market = 'no'
                //             }
                //             if (value.lane.lane_code === undefined) {
                //                 $scope.route = ''
                //             } else {
                //                 $scope.route = value.lane.lane_code
                //             }
                //             try {
                //                 var start_date_time = new Date(value.trip_start_date.split('T')[0] + " " + value.trip_start_time.split('T')[1]);
                //                 var end_date_time = new Date(value.trip_end_date + " " + value.trip_end_time);
                //                 diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
                //                 diff /= 60;
                //                 num = Math.abs(Math.round(diff));
                //                 var hours = (num / 60);
                //                 var rhours = Math.floor(hours);
                //                 var minutes = (hours - rhours) * 60;
                //                 var rminutes = Math.round(minutes);
                //                 var total_time = rhours + "hr " + rminutes + "min";
                //             }
                //             catch (err) {
                //                 total_time = ''
                //             }
                //
                //             $scope.completedExportValue = {
                //                 "Trip Code": value.trip_code,
                //                 "Market": $scope.market,
                //                 "Lr No": $scope.lr_no,
                //                 "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                //                 "Origin": value.order_data.origin.location_name,
                //                 "Destination": value.order_data.destination.location_name,
                //                 "Driver Nname": value.order_data.driver_name + '/' + value.order_data.driver_no,
                //                 "Vehicle No": value.order_data.vehicle_no,
                //                 "Route": $scope.route,
                //                 "Trip Status": value.trip_status.status_name,
                //                 "Placement Date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
                //                 "Start Date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
                //                 "Reporting Date": $scope.trip_end_date,
                //                 'Total Time': total_time,
                //                 "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
                //             };
                //             $scope.downloadCompletedCsv.push($scope.completedExportValue)
                //         });
                //         var mystyle = {
                //             headers: true,
                //             column: {style: {Font: {Bold: "1"}}}
                //         };
                //         alasql('SELECT * INTO XLS("Completed Trips.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'placement_date')]);
                //     }
                // })
            };

            /******************************Co-Driver Data Get*******************************************************/

            $scope.coDriverDetailsPlanning = function (orderAssign) {
                trackingServices.coDriverDataPlanning(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };


            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.completedTrips, query).length;
                });
            };


        }])

    .controller('ongoing.listing', ['$scope', '$state', '$rootScope', 'trackingServices', '$filter', '$location',
        function ($scope, $state, $rootScope, trackingServices, $filter, $location) {
            $scope.$watch('online', function (newStatus) {
                console.log(newStatus)
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }

            });
            $scope.showDlPicture = function (dl_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(dl_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDlPic = response.data.url
                    });
                }
                else {
                    $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
                }

            };
            $scope.showInsuPicture = function (insurance_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                        // console.log(response)
                        $scope.showInsurancePic = response.data.url
                    });
                } else {
                    $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
                }

            };
            $scope.showRcPicture = function (rc_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(rc_pic).then(function (response) {

                        // console.log(response)
                        $scope.showRcPic = response.data.url
                    });
                }
                else {


                    $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
                }
            };
            $scope.showFitPicture = function (fitness_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                        // console.log(response)
                        $scope.showFitnessPic = response.data.url
                    });
                }
                else {


                    $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
                }
            };
            $scope.showLrPicture = function (lr_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrPic = response.data.url
                    });
                }
                else {

                    $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
                }

            };
            $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverVehiclePic = response.data.url
                    });
                }
                else {


                    $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
                }
            };
            $scope.showLrNumPicture = function (lr_num_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                }
                else {


                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }
            };
            $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehi_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverAndVehiclePic = response.data.url
                    });
                }
                else {

                    $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic

                }
            };
            $scope.showLrNumberPicture = function (lr_num_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                } else {
                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }


            };
            $scope.showPermitPicture = function (permit_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(permit_pic).then(function (response) {

                        // console.log(response)
                        $scope.showPermitPic = response.data.url
                    });
                }
                else {


                    $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic

                }
            };
            /**********************************Driver Details Get**********8*************************************/

            $scope.driverDetailsTracking = function (orderAssign) {
                trackingServices.driverDataTracking(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };

            /****************************** Vehicle Data Get*******************************************************/

            $scope.vehicleDetailsTracking = function (vehicleData) {
                trackingServices.vehicleDataTracking(vehicleData.id).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.vehicle = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            /**
             * Pagination in ongoing trips.
             * @type {boolean}
             */

            $scope.loading = true;
            $scope.ongoingTrip_listing = function () {
                //$state.reload();
            };

            $scope.ongoingTripPage = { 'next': '', 'previous': '', 'count': '' };
            $scope.ongoing_track = function () {
                trackingServices.onGoingTrip($scope.ongoingTripPage.next).then(function (response) {
                    if (response.results.code.status == 200) {
                        $scope.loading = false;
                        try {
                            if ($scope.onGoingTrips.length) {
                                $scope.onGoingTrips = $scope.onGoingTrips.concat(response.results.trip_data)
                            }
                            else {
                                $scope.searchText = '';
                                $scope.onGoingTrips = response.results.trip_data;
                            }
                        }
                        catch (err) {
                            $scope.onGoingTrips = response.results.trip_data;
                        }
                        $scope.ongoingTripPage.next = response.next;
                        $scope.ongoingTripPage.previous = response.previous;
                        $scope.ongoingTripPage.count = response.count;
                        if ($scope.ongoingTripPage.next != null) {
                            $scope.ongoing_track()
                        }
                    }
                    else {
                        alert(response.results.code.message)
                    }
                })
            };
            $scope.ongoing_track();

            $scope.trackTrip = function (trip) {
                console.log("track_trip", trip);
                $scope.track_wayPoints = [];
                $scope.track_lane = trip.lane;
                $scope.track_route = trip.route;
                if ($scope.track_route.origin_start && $scope.track_lane.lane_name) {
                    $scope.track_origin = trip.route.origin_start.location_city.name;
                    $scope.track_destination = trip.route.destination.location_city.name;
                    $scope.track_lane.location.data.forEach(function (item) {
                        $scope.track_wayPoints.push({
                            location: {
                                lat: item.location_point.coordinates.coordinates[1],
                                lng: item.location_point.coordinates.coordinates[0]
                            }, stopover: true
                        })
                    });
                }
            };
            $scope.dateTime = function (data) {

                $scope.index = $scope.onGoingTrips.indexOf(data);
                console.log('index', $scope.index)
                $scope.tripId = data.id;
                var date = new Date();
                $scope.ddMMyyyy = $filter('date')(new Date(), 'yyyy-MM-dd');
                console.log('$scope.ddMMyyyy', $scope.ddMMyyyy)
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm:ss');
                console.log('$scope.HHmmss', $scope.HHmmss);

            };

            $scope.reportingDateTime = function (ddMMyyyy, HHmmss) {

                $scope.reportingDateTimeTrip = {
                    trip_end_date: ddMMyyyy,
                    trip_end_time: HHmmss,
                    id: $scope.tripId
                };
                $scope.onGoingTrips[$scope.index].trip_end_date = ddMMyyyy;
                $scope.onGoingTrips[$scope.index].trip_end_time = HHmmss;
                //console.log('cjh', $scope.orderAssigns[$scope.index])
                //console.log($scope.orderAssigns)
                trackingServices.postReportingDateTimeTrip($scope.reportingDateTimeTrip).then(function (response) {
                    console.log('response', response);
                    if (response.data.code.status === 200) {
                        $scope.onGoingTrips[$scope.index].trip_status.status_name = response.data.trip_status;
                        swal("Good !", response.data.code.message, "success")
                    } else if (response.data.code.status === 202) {
                        alert("oops !", response.data.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            $scope.unlodingDateTime = function (data) {
                $scope.index = $scope.onGoingTrips.indexOf(data);
                console.log('index', $scope.index)
                $scope.tripId = data.id;
                var date = new Date();
                $scope.ddMMyyyy = $filter('date')(new Date(), 'yyyy-MM-dd');
                console.log('$scope.ddMMyyyy', $scope.ddMMyyyy)
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm:ss');
                console.log('$scope.HHmmss', $scope.HHmmss);
            };

            $scope.unloading = function (ddMMyyyy, HHmmss) {
                $scope.unloadingDateTimeTrip = {
                    unloading_date: ddMMyyyy,
                    unloading_time: HHmmss,
                    id: $scope.tripId
                };
                var idx = $scope.index;
                trackingServices.unloadingDateTime($scope.unloadingDateTimeTrip).then(function (response) {
                    console.log('response', response);
                    if (response.data.code.status === 200) {
                        $scope.onGoingTrips.splice(idx, 1);
                        swal("Good !", response.data.code.message, "success")
                    } else if (response.data.code.status === 202) {
                        swal("oops !", response.data.code.message, "error")
                    }
                    //swal("Good job!", response.message, "success")
                }, function (error) {
                    swal('Error Occurred')
                })
            };

            /**
             *Filter
             *Prafull
             */


            $scope.asyncRequestForFilter = function () {
                //Write here function for async request
                async.parallel([
                    function (callback) {
                        // request no 1
                        trackingServices.getAllLrOngoingLists().then(function (response) {

                            if (response.data.code.status === 200) {
                                $scope.lrName = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 2
                        trackingServices.getAllCustomerLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.customerName = response.data.customer;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 3
                        trackingServices.getAllLocationLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.locationNmae = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 4
                        trackingServices.getAllBrokerList().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.brokerName = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 5
                        trackingServices.getAllVehicleLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.vehicleNo = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }

                ])
            };

            $scope.asyncRequestForFilter();

            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                vehicle_no: ''
            };

            $scope.ongoingTripFilter = function (filterData) {
                $scope.vehicle_no = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';
                $scope.lr_no = '';
                $scope.broker_id = '';
                $scope.customer_code = '';


                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id;

                $scope.filterDataObject = {
                    customer: $scope.customer_code,
                    vehicle_no: $scope.vehicle_no,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required,
                    lr: $scope.lr_no,
                    broker: $scope.broker_id
                };
                console.log('$scope.filterDataObject', $scope.filterDataObject);
                $scope.ongoingTripFilterPage = { 'next': '', 'previous': '', 'count': '' };
                $scope.onGoingTrips = '';
                $scope.ongoingTripFilterListing = function () {
                    trackingServices.ongoingTripFilterService($scope.ongoingTripFilterPage.next, $scope.filterDataObject).then(function (response) {
                        console.log('response', response);
                        if (response.data.results.code.status == 200) {
                            $scope.loading = false;
                            try {
                                if ($scope.onGoingTrips.length) {
                                    $scope.onGoingTrips = $scope.onGoingTrips.concat(response.data.results.trip_data)
                                }
                                else {
                                    $scope.onGoingTrips = response.data.results.trip_data;
                                }
                            }
                            catch (err) {
                                $scope.onGoingTrips = response.data.results.trip_data;
                            }
                            $scope.ongoingTripFilterPage.next = response.data.next;
                            $scope.ongoingTripFilterPage.previous = response.data.previous;
                            $scope.ongoingTripFilterPage.count = response.data.count;
                            if ($scope.ongoingTripFilterPage.next != null) {
                                $scope.ongoingTripFilterListing()
                            }
                        }
                        else {
                            swal(response.data.results.code.message)
                        }
                    })
                };
                $scope.ongoingTripFilterListing();
            };
            $scope.clearFilterOngoing = function () {
                $scope.searchText = '';
                $scope.ongoing_track();
                $scope.filterData = {
                    lr_no: '',
                    customer: '',
                    origin: '',
                    destination: '',
                    broker: '',
                    date_vehicle_required: '',
                    to_date_vehicle_required: '',
                    vehicle_no: ''
                };
            };


            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.orderAssigns, query).length;
                });
            };
            /******************************Co-Driver Data Get*******************************************************/

            $scope.coDriverDetailsPlanning = function (orderAssign) {
                trackingServices.coDriverDataPlanning(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };
        }])

    .controller('waiting-cwh.listing', ['$scope', '$state', '$location', 'trackingServices', '$sce', '$cookies', '$filter', function ($scope, $state, $location, trackingServices, $sce, $cookies, $filter) {

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Data Connection Lost")
            }
        });
        /**
         * Prafull
         * @param dl_pic
         */

        $scope.showDLPicture = function (dl_pic) {
            if (pic) {
                trackingServices.picLoadServerT(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            }
            else {


                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
                console.log('$scope.showDlPic', $scope.showDlPic)
            }
        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url
                });
            }
            else {


                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
                console.log('$scope.showInsurancePic', $scope.showInsurancePic)
            }
        };
        $scope.showRCPicture = function (rc_pic) {
            if (pic) {
                trackingServices.picLoadServerT(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url
                });
            }
            else {


                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
                console.log('$scope.showRcPic', $scope.showRcPic)
            }
        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url
                });
            }
            else {


                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
                console.log('$scope.showFitnessPic', $scope.showFitnessPic)
            }
        };
        /**
         *
         * @param lr_pic
         */
        $scope.showLRPicture = function (lr_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url
                });
            }
            else {


                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
                // console.log('$scope.showLrPic', $scope.showLrPic)
            }
        };
        $scope.showDriverWithVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url
                });
            }
            else {
                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
                console.log('$scope.showDriverVehiclePic', $scope.showDriverVehiclePic)
            }
        };
        $scope.showLrNumPicture = function (lr_num_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url
                });
            }
            else {


                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }
            console.log('$scope.showLrNumPic', $scope.showLrNumPic)
        };

        $scope.driverDetails = function (driverTripData) {
            $scope.driver = driverTripData;
            console.log('driverTripData', driverTripData)
        };
        $scope.vehicleDetails = function (vehicleTripData) {
            $scope.vehicle = vehicleTripData;
            console.log('vehicle', $scope.vehicle)
        };
        $scope.showLrNumberPicture = function (lr_num_pic) {
            if (pic) {
                trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url
                });
            }
            else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }


        };

        $scope.driverDetails = function (orderAssign) {
            trackingServices.driverOnlyDataList(orderAssign.id).then(function (response) {
                console.log('1', response);
                if (response.data.code.status == 200) {

                    $scope.driver = response.data.data_list;
                }
                else {
                    swal("Cancelled", response.code.message, "error")
                }
            }
            );
        };

        $scope.vehicleDetails = function (orderAssign) {
            trackingServices.vehicleDetailsList(orderAssign.id).then(function (response) {
                console.log('2', response);
                if (response.data.code.status == 200) {
                    $scope.vehicle = response.data.data_list;

                }
                else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };

        $scope.waitingAtCwh = function () {
            $scope.loading = true;
            $scope.searchText = '';
            trackingServices.waitingAtCWH().then(function (response) {
                console.log('response', response);
                $scope.loading = false;
                $scope.asyncRequestForFilter();
                if (response.code.status == 200) {
                    $scope.waitingCwh = response.trip_data;
                }
                else {
                    swal("Oops", response.code.message, "error")
                }
            })
        };


        $scope.getWaitingAtCwhHistory = function (id) {
            $scope.trip_id = id;
            trackingServices.waitingCwhAlertHistory($scope.trip_id).then(function (response) {
                console.log('alertHistory', response);
                if (response.code.status === 200) {

                    $scope.alertHistory = response.alert
                    console.log('$scope.alertHistory', $scope.alertHistory)
                } else {
                    swal("oops!", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.source_name = $cookies.get('username');
        console.log('$scope.source_name', $scope.source_name)
        $scope.txtcomment = { name: '' };
        $scope.checkHistory = function (txtcomment) {

            var date = new Date();
            $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');

            $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');

            $scope.alertHistory = $scope.alertHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';

            $scope.txtcomment = { name: '' };

            $scope.alertHistorys = {
                trip_id: $scope.trip_id,
                assign_cwh_waiting_alert_history: $scope.alertHistory

            };
            console.log('$scope.alertHistorys', $scope.alertHistorys);
            trackingServices.postWaitingCwhAlertHistory($scope.alertHistorys).then(function (response) {
                console.log(response)
                if (response.code.status == 200) {

                }
                else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.toTrusted = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
        };

        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.waitingCwh, query).length;
            });
        };
        $scope.waitingAtCwh();

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([
                function (callback) {
                    // request no 1
                    trackingServices.getAllLrOngoingLists().then(function (response) {
                        if (response.data.results.code.status === 200) {
                            $scope.lrName = response.data.results.data_list;
                            callback();
                        } else {
                            alert(response.data.results.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 2
                    trackingServices.getAllCustomerLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.customerName = response.data.customer;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 3
                    trackingServices.getAllLocationLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.locationNmae = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 4
                    trackingServices.getAllBrokerList().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.brokerName = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 5
                    trackingServices.getAllVehicleLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleNo = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }
            ])
        };

        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            trackingServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};


        $scope.pagination = {};
        $scope.filterData = {
            lr_no: '',
            customer: '',
            origin: '',
            destination: '',
            date_vehicle_required: '',
            to_date_vehicle_required: '',
            vehicle_no: ''
        };

        $scope.getLrNumbers = function (data) {
            if (data.length > 3) {
                var lr = {
                    'lr_num': data
                };
                trackingServices.getLrNumSerTrc(lr).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.lrName = response.data.lr;
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })
            }
        };
        //$scope.getLrNumbers();

        $scope.cwhFilter = function (count, filterData) {
            $scope.loading = true;
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.customer_id = '';

            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;

            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;

            if (filterData.date_vehicle_required)
                $scope.date_vehicle_required = filterData.date_vehicle_required;

            if (filterData.to_date_vehicle_required)
                $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

            if (filterData.lr)
                $scope.lr_no = filterData.lr.lr_reciept;

            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;

            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no
            };
            trackingServices.cwhFilterService(count, $scope.filterDataObject).then(function (response) {
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {
                    $scope.waitingCwh = response.data.results.trip_data;
                } else {
                    alert(response.data.code.message)
                }
            })
        };

        $scope.clearCwh = function () {
            $scope.pagination.current = 1;
            $scope.searchText = '';
            $scope.waitingAtCwh();
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                vehicle_no: ''
            };
        };


    }])
    .controller('hubWaiting.listing', ['$scope', 'trackingServices', '$sce', '$cookies', '$filter', function ($scope, trackingServices, $sce, $cookies, $filter) {

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Data Connection Lost")
            }
        });

        $scope.hubWaiting = function () {
            $scope.searchText = '';
            $scope.loading = true;
            trackingServices.getHubWaiting().then(function (response) {
                console.log(response)
                $scope.loading = false;
                if (response.code.status == 200) {
                    $scope.hubWaitingTime = response.waiting_list;
                }
                else {
                    swal("Oops", response.code.message, "error")
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.getHubWaitingHistory = function (hubData) {
            $scope.trip_id = hubData.trip_id;
            $scope.hub_id = hubData.pit_stop.hub_id;

            trackingServices.hubWaitingAlertHistory($scope.trip_id, $scope.hub_id).then(function (response) {
                console.log('alertHistory', response)
                if (response.code.status === 200) {

                    $scope.alertHistory = response.alert
                    console.log('$scope.alertHistory', $scope.alertHistory)
                } else {
                    swal("oops!", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.source_name = $cookies.get('username');
        $scope.txtcomment = { name: '' };
        $scope.checkHistory = function (txtcomment) {

            var date = new Date();
            $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');

            $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');

            $scope.alertHistory = $scope.alertHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';

            $scope.txtcomment = { name: '' };
            $scope.alertHistorys = {
                trip_id: $scope.trip_id,
                hub_id: $scope.hub_id,
                assign_hub_waiting_alert_history: $scope.alertHistory

            };
            console.log('$scope.alertHistorys', $scope.alertHistorys)
            trackingServices.postHubWaitingHistory($scope.alertHistorys).then(function (response) {
                console.log(response)
                if (response.code.status == 200) {

                }
                else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.toTrusted = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
        };
        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.hubWaitingTime, query).length;
            });
        };

    }])

    .controller('unscheduledStoppage.listing', ['$scope', 'trackingServices', '$sce', '$cookies', '$filter', function ($scope, trackingServices, $sce, $cookies, $filter) {


        $scope.alertStatus = [{ 'statusName': "ON" }, { "statusName": "SNOOZE" }, { "statusName": "OFF" }];

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Data Connection Lost")
            }
        });

        $scope.unScheduledStopage = function () {
            $scope.searchText = '';
            $scope.loading = true;
            trackingServices.getUnScheduledStopage().then(function (response) {
                console.log('unScheduledStopage', response);
                $scope.loading = false;
                if (response.code.status == 200) {
                    $scope.unScheduledStopageTime = response.alert_data;
                    console.log('$scope.unScheduledStopageTime', $scope.unScheduledStopageTime)
                }
                else {
                    swal("Oops", response.code.message, "error")
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.filterData = {}
        $scope.unScheduledStopageFilter = function (filterData) {

            if (filterData.vehicle_number || filterData.customer || filterData.driver_code) {
                console.log('FILTERDATA', filterData)
                $scope.loading = true;
                $scope.vehicle_no = '';
                $scope.customer_id = '';
                $scope.driver_code = '';
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;
                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;
                if (filterData.driver_code)
                    $scope.driver_code = filterData.driver_code.driver_code;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    driver_code: $scope.driver_code
                };
                $scope.searchText = '';
                trackingServices.unscheduleFilter($scope.filterDataObject).then(function (response) {
                    $scope.loading = false;
                    console.log('vehicleTrackingFilter', response);
                    if (response.code.status === 200) {
                        $scope.unScheduledStopageTime = response.trip_data;
                    } else {
                        alert(response.code.message)
                    }
                })
            }
            else {
                swal("Empty filter can not applied !", 'Atleast select one filter.', "error")
            }

        };


        $scope.unScheduledStopage();

        $scope.getUnScheduledHistory = function (unScheduldedData) {

            $scope.unschedule_id = unScheduldedData.unschedule_id;
            $scope.unScheduldedData = unScheduldedData;

            trackingServices.unScheduledAlertHistory($scope.unschedule_id).then(function (response) {
                console.log('alertHistory', response)
                if (response.code.status === 200) {

                    $scope.alertHistory = response.alert;
                    console.log('$scope.alertHistory', $scope.alertHistory)
                } else {
                    swal("oops!", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.source_name = $cookies.get('username');
        $scope.txtcomment = { name: '' };
        $scope.checkHistorys = function (txtcomment) {

            var date = new Date();
            $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');

            $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');

            $scope.alertHistory = $scope.alertHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';

            $scope.unScheduldedData.reason = txtcomment;
            $scope.txtcomment = { name: '' };
            $scope.alertHistorys = {
                unschedule_id: $scope.unschedule_id,
                assign_unschedule_alert_history: $scope.alertHistory,
                reason: $scope.unScheduldedData.reason
            };
            console.log('$scope.alertHistorys', $scope.alertHistorys);
            trackingServices.postUnScheduledHistory($scope.alertHistorys).then(function (response) {
                console.log(response)
                if (response.code.status == 200) {

                }
                else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.toTrusted = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
            //return '';
        };
        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.unScheduledStopageTime, query).length;
            });
        };
        $scope.pagination = {};
        $scope.clearCwh = function () {
            $scope.pagination.current = 1;
            $scope.searchText = '';
            $scope.unScheduledStopage();
            $scope.filterData = {
                customer: '',
                driver_code: '',
                vehicle_number: ''
            };
        };


        $scope.unScheduledStopageExportData = function (filterData) {
            console.log('FILTERDATA', filterData)
            $scope.vehicle_no = '';
            $scope.customer_id = '';
            $scope.driver_code = '';
            if (filterData.vehicle_number || filterData.customer || filterData.driver_code) {
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;
                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;
                if (filterData.driver_code)
                    $scope.driver_code = filterData.driver_code.driver_code;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    driver_code: $scope.driver_code
                };
                $scope.searchText = '';
                trackingServices.unscheduleFilter($scope.filterDataObject).then(function (response) {
                    $scope.loading = false;
                    console.log('vehicleTrackingFilter', response);
                    if (response.code.status === 200) {
                        $scope.unScheduledStopageTimeExport = response.trip_data;
                    } else {
                        alert(response.code.message)
                    }
                    $scope.csvloading = true;
                    $scope.downloadunScheduledStopageCsv = [];
                    angular.forEach($scope.unScheduledStopageTimeExport, function (value, key) {
                        $scope.unScheduledStopageExportValue = {
                            "Trip Code": value.trip_code,
                            "Vehicle No.": value.vehicle_no,
                            "Driver Name/No.": value.driver_name + '/' + value.driver_number,
                            "Customer": value.client_data.company + '<' + value.client_data.customer_code + '>',
                            "Location": value.location,
                            "TimeStamp": value.date.split('T')[0] + '/' + value.date[1],
                            "Stoppage time (min.)": value.duration / 60,
                            "Nearest hub": value.n_pit_stop ? value.n_pit_stop.hub_name : '',
                            " Distance from hub(Km)": value.n_pit_stop ? value.n_pit_stop.distance : '',
                            "Hub Manager": value.hub_manager + '/' + value.hubmanager_num,
                        };
                        $scope.downloadunScheduledStopageCsv.push($scope.unScheduledStopageExportValue);
                    });


                    var mystyle = {
                        headers: true,
                        column: { style: { Font: { Bold: "1" } } }
                    };
                    alasql('SELECT * INTO XLS("unScheduledStopage.xls",?) FROM ?', [mystyle, $scope.downloadunScheduledStopageCsv = $filter('orderBy')($scope.downloadunScheduledStopageCsv, 'placement_date')]);
                    $scope.csvloading = false;
                })
            }
            else {
                $scope.csvloading = true;
                $scope.downloadunScheduledStopageCsv = [];
                angular.forEach($scope.unScheduledStopageTime, function (value, key) {
                    $scope.unScheduledStopageExportValue = {
                        "Trip Code": value.trip_code,
                        "Vehicle No.": value.vehicle_no,
                        "Driver Name/No.": value.driver_name + '/' + value.driver_number,
                        "Customer": value.client_data.company + '<' + value.client_data.customer_code + '>',
                        "Location": value.location,
                        "TimeStamp": value.date.split('T')[0] + '/' + value.date[1],
                        "Stoppage time (min.)": value.duration / 60,
                        "Nearest hub": value.n_pit_stop ? value.n_pit_stop.hub_name : '',
                        " Distance from hub(Km)": value.n_pit_stop ? value.n_pit_stop.distance : '',
                        "Hub Manager": value.hub_manager + '/' + value.hubmanager_num,
                    };
                    $scope.downloadunScheduledStopageCsv.push($scope.unScheduledStopageExportValue);
                });


                var mystyle = {
                    headers: true,
                    column: { style: { Font: { Bold: "1" } } }
                };
                alasql('SELECT * INTO XLS("unScheduledStopage.xls",?) FROM ?', [mystyle, $scope.downloadunScheduledStopageCsv = $filter('orderBy')($scope.downloadunScheduledStopageCsv, 'placement_date')]);
                $scope.csvloading = false;
            }

        };

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([

                function (callback) {
                    //request no 2
                    trackingServices.getAllCustomerLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.customerName = response.data.customer;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 5
                    trackingServices.getAllVehicleLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleNo = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    trackingServices.getOwnDriverLists().then(function (response) {
                        console.log('driverList', response)
                        if (response.code.status === 200) {
                            $scope.driverList = response.driver_list;
                            callback();
                        } else {
                            alert(response.code.message);
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    trackingServices.getTripLists().then(function (response) {
                        console.log('triplist', response)
                        if (response.code === 202) {
                            $scope.tripList = response.details.alertData;
                            callback();
                        } else {
                            alert(response.message);
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }
            ])
        };

        $scope.clearStoppageListing = function () {
            $scope.filterData = { 'customerCode': '', 'vehicleCode': '', 'driverCode': '', 'status': '', 'tripCode': ''  }
            filter_data = { 'customerCode': "", 'vehicleCode': "", 'driverCode': "", page: 1, 'status': '', 'tripCode': ""  };
            trackingServices.getStoppageAlert(1,filter_data).then(function (response) {
                console.log('stoppage lists', response)
                $scope.stoppageAlert = response.data.details.alertData
                $scope.stoppageAlertPagination = response.data.details.pagination


            }, function (err) {
                console.log("Error", err)
                swal("Oops", err.statusText, "error")
            });
        }


        $scope.filterData = { 'customerCode': '', 'vehicleCode': '', 'driverCode': '', 'status': ''  }
        $scope.stoppageListing = function (page, filterData) {
            console.log('Stoppage test++++', filterData);

            $scope.defineData = {}

            if (filterData.vehicleCode === undefined || filterData.vehicleCode == "") {
                $scope.defineData.vehicleCode = { 'code': "" };
            }
            else {
                $scope.defineData.vehicleCode = filterData.vehicleCode
            }
            if (filterData.customerCode === undefined || filterData.customerCode == "") {
                $scope.defineData.customerCode = { 'customer_code': "" };
            }
            else {
                $scope.defineData.customerCode = filterData.customerCode
            }
            if (filterData.driverCode === undefined || filterData.driverCode == "") {
                $scope.defineData.driverCode = { 'driver_code': "" };
            }
            else {
                $scope.defineData.driverCode = filterData.driverCode
            }

            if (filterData.status === undefined || filterData.status == "") {
                $scope.defineData.status = { 'statusName': "" };
            }
            else {
                $scope.defineData.status = filterData.status
            }
            if (filterData.fromDateTime === undefined || filterData.fromDateTime == "") {
                $scope.defineData.fromDateTime = "";
            }
            else {
                $scope.defineData.fromDateTime = filterData.fromDateTime
            }
            if (filterData.toDateTime === undefined || filterData.toDateTime == "") {
                $scope.defineData.toDateTime = "";
            }
            else {
                $scope.defineData.toDateTime = filterData.toDateTime
            }
            if (filterData.tripCode === undefined || filterData.tripCode == "") {

                $scope.defineData.tripCode = "";
            }
            else {
                $scope.defineData.tripCode = filterData.tripCode;
            }
            $scope.loading = true;
            filter_data = {
                'customerCode': $scope.defineData.customerCode.customer_code, 'vehicleCode': $scope.defineData.vehicleCode.code, 'driverCode': $scope.defineData.driverCode.driver_code, 'page': page, 'status': $scope.defineData.status.statusName,
                'fromDateTime': $scope.defineData.fromDateTime, 'toDateTime': $scope.defineData.toDateTime, 'tripCode': $scope.defineData.tripCode

            }
            trackingServices.getStoppageAlert(1,filter_data).then(function (response) {
                $scope.loading = false;
                console.log('stoppage lists', response)
                $scope.stoppageAlert = response.data.details.alertData
                $scope.stoppageAlertPagination = response.data.details.pagination


            }, function (err) {
                $scope.loading = false;
                console.log("Error", err)
                swal("Oops", err.data.message, "error")
            });
        }

        $scope.stoppageReasonSelect = {};
        $scope.onSelected = function (selectedItem) {
            if (selectedItem === undefined) {
                $scope.stoppageReasonSelect.cooling = null
            }
            else {
                $scope.stoppageReasonSelect.cooling = selectedItem.coolingPeriod;
            }
            console.log("selected item, ", selectedItem)

        }

        $scope.getAlertReasonDetails = function () {
            $scope.stoppageReasonSelect = {}
            $scope.remarks = {}

            console.log('Alert Reasons Details++++')
            trackingServices.getStoppageReason().then(function (response) {

                $scope.stoppageReason = response.data.details
            }, function (err) {
                console.log("Error", err)
                swal("Oops", response.data.message, "error")
            });
        }

        $scope.stoppageAckCount = {}
        $scope.getReasonsCount = function (rowData) {
            $scope.rowData = rowData
            console.log(rowData,'asc')
            console.log('Reasons Count++++', rowData)
            trackingServices.getAcknowledgementCount($scope.rowData.id).then(function (response) {
                console.log('Reasons Count++++', response)
                $scope.stoppageAckCount = response.data.details
            }, function (err) {
                console.log("Error", err)
                swal("Oops", err.statusText, "error")
            });
        }
        // rishabh
        $scope.LatestAlertData = {}
        $scope.getLatestAlertData = function (rowData) {
            $scope.rowData = rowData
            trackingServices.getLatestAlert($scope.rowData.id).then(function (response) {
                console.log('Reasons Count++++---', response)
                $scope.LatestAlertData = response.data.details
                $scope.FinalRemarks = response.data.details.remarks
            }, function (err) {
                console.log("Error", err)
                swal("Oops", err.statusText, "error")
            });
        }
        $scope.remarks = {}
        $scope.remarksdata = function (r) {
            $scope.remarks.test= r
        };

        $scope.onReasonSubmit = function (alertData, ReasonData, remarks) {
            $scope.index = $scope.stoppageAlert.indexOf(alertData);
            // console.log('On Submit ', remarks)
            // console.log('On Submit ', alertData)
            if (ReasonData.reason === undefined) {
                swal("Oops", "Please select Reason", "error");
                return
            }

            console.log('Alert Reasons Details++++')
            trackingServices.submitAcknowledgement({ 'alertsId': alertData.id, 'alertReasonId': ReasonData.reason.id,'remarks': remarks.r }).then(function (response) {
                console.log('On Reason Submit+++', response)
                swal("Good job!", "Done", "success")
                angular.element('#unScheduledHistory').modal('hide');
                $scope.stoppageAlert[$scope.index].status = 'Snooze'
            }, function (err) {
                console.log("Error", err)
                swal("Oops", err.statusText, "error")
            });
        }

        $scope.asyncRequestForFilter()

        // rishabh
        $scope.filterData = { 'customerCode': '', 'vehicleCode': '', 'driverCode': '', 'status': ''  }
        $scope.unScheduledStopageExportDataCsv = async function (page,filterData) {

            $scope.defineData = {}
            $scope.progress_bar_per = 0

            if (filterData.vehicleCode === undefined || filterData.vehicleCode == "") {
                $scope.defineData.vehicleCode = { 'code': "" };
            }
            else {
                $scope.defineData.vehicleCode = filterData.vehicleCode
            }
            if (filterData.customerCode === undefined || filterData.customerCode == "") {
                $scope.defineData.customerCode = { 'customer_code': "" };
            }
            else {
                $scope.defineData.customerCode = filterData.customerCode
            }
            if (filterData.driverCode === undefined || filterData.driverCode == "") {
                $scope.defineData.driverCode = { 'driver_code': "" };
            }
            else {
                $scope.defineData.driverCode = filterData.driverCode
            }

            if (filterData.status === undefined || filterData.status == "") {
                $scope.defineData.status = { 'statusName': "" };
            }
            else {
                $scope.defineData.status = filterData.status
            }
            if (filterData.fromDateTime === undefined || filterData.fromDateTime == "") {
                swal("Oops", 'Export data can not applied. Date range should be entered between 30 days.', "error");
                    $scope.loading = false;
                    return false
            }

            if (filterData.toDateTime === undefined || filterData.toDateTime == "") {
                swal("Oops", 'Export data can not applied. Date range should be entered between 30 days.', "error");
                    $scope.loading = false;
                    return false
            }

            if (filterData.fromDateTime != "" & filterData.fromDateTime != undefined & filterData.toDateTime!= "" & filterData.toDateTime != undefined){
                $scope.defineData.fromDateTime = filterData.fromDateTime
                $scope.defineData.toDateTime = filterData.toDateTime

                var previousdate = new Date($scope.defineData.fromDateTime)
                console.log("previousdata data",previousdate)
                var nowdate = new Date($scope.defineData.toDateTime)
                console.log('fromdate',nowdate)
                console.log('todate',previousdate)
                var diff = Math.round((nowdate-previousdate)/(1000*60*60*24));
                console.log('difference',diff)
                if (diff > 30){
                    swal("Oops", 'Export data can not applied. Date range should be less then 30 days.', "error");
                    $scope.loading = false;
                    return false
                }
                else if(Object.is(diff,NaN)){
                    swal("Oops", 'Export data can not applied. Date range is incorrect', "error");
                    return false
                }


            }
            if (filterData.tripCode === undefined || filterData.tripCode == "") {
                $scope.defineData.tripCode = "";
            }
            else {
                $scope.defineData.tripCode = filterData.tripCode;
            }
            $scope.loading = true;
            $scope.filter_data = {
                'customerCode': $scope.defineData.customerCode.customer_code, 'vehicleCode': $scope.defineData.vehicleCode.code, 'driverCode': $scope.defineData.driverCode.driver_code, 'page': page, 'status': $scope.defineData.status.statusName,
                'fromDateTime': $scope.defineData.fromDateTime, 'toDateTime': $scope.defineData.toDateTime, 'tripCode': $scope.defineData.tripCode
            }
            var completedExport = [];
            num_of_pages=1;
            var recur_tracking_menu = async function () {
                await trackingServices.getStoppageAlert(num_of_pages,$scope.filter_data).then(function (response) {
                if(response.data.code == 202 && response.data.details.alertData != undefined){
                    var next = response.data.details.pagination.next;
                    num_of_pages = num_of_pages + 1;
                    var count = response.data.details.pagination.count;
                    var finalExport=[]
                    if (next != null & num_of_pages <= next) {
                            completedExport = completedExport.concat(response.data.details.alertData);
                            recur_tracking_menu()
                        }
                        else {
                            completedExport = completedExport.concat(response.data.details.alertData);
                            angular.forEach(completedExport, function (value, key) {
                                $scope.unScheduledStopageExportValue = {
                                    "Status": value.status,
                                    "Trip Code": value.tripCode,
                                    "Vehicle Number": value.entity,
                                    "Location": value.address,
                                    "Start Point": value.laneOrigin,
                                    "Destination Point": value.laneDestination,
                                    "Stoppage Time" : value.duration,
                                    "Timestamp": moment(value.fromDateTime).format("YYYY-MM-DD HH:mm:ss"),


                            };
                            finalExport.push($scope.unScheduledStopageExportValue);

                        });

                            var mystyle = {
                                headers: true,
                                column: { style: { Font: { Bold: "1" } } }
                            };
                            alasql('SELECT * INTO XLS("unScheduledStopageAlert.xls",?) FROM ?', [mystyle, finalExport = $filter('orderBy')(finalExport, 'Timestamp')]);
                            $scope.csvloading = false;
                        }
                    $scope.progress_bar_per = ((finalExport.length / count) * 100).toFixed(2);
                }
                else {
                    swal("Oops", 'Export data can not applied. No Data is present in the given date-time frame', "error")
                }

                $scope.loading = false;
                });

        }
        recur_tracking_menu()
        }


    }])

    .controller('completedHistory.listing', ['$scope', '$state', '$rootScope', '$location', 'trackingServices', '$filter',
        function ($scope, $state, $rootScope, $location, trackingServices, $filter) {
            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            //Ashish

            $scope.completedTripPage = {};
            $scope.pagination = {};
            $scope.completedTripTrackingHistory = function (count) {
                $scope.loading = true;
                $scope.Count = count;
                trackingServices.getCompletedTripHistory($scope.Count).then(function (response) {
                    $scope.loading = false;
                    console.log('suuu', response);
                    if (response.code.status === 200) {
                        $scope.completedTripsHistory = response.trip_data;
                        $scope.completedTripPage.count = response.count;
                        $scope.wf = response.wf;
                    } else {
                        alert(response.code.message)
                    }
                })
            };

            //Ashish
            $scope.getAdvancePayDetails = function (order_code) {
                trackingServices.advancePayDetails(order_code).then(function (response) {
                    console.log('response_here', response);
                    if (response.data.code.status == 200) {
                        $scope.broker_rate = response.data.details.broker_rate;
                        $scope.cash_adv = response.data.details.cash_adv;
                        $scope.broker_advance = response.data.details.broker_advance;
                        $scope.balance_paid = response.data.details.payment_due;
                    }
                    else {
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.getOwnPayDetails = function (order_code) {
                trackingServices.ownAdvancePayDetails(order_code).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.ownAdvanceData = response.data.details;
                        $scope.ownAdvanceData.totalEntry = 0;
                        $scope.ownAdvanceData.totalFuel = 0;
                        $scope.ownAdvanceData.totalMeals = 0;
                        $scope.ownAdvanceData.totalPolice = 0;
                        $scope.ownAdvanceData.totalToll = 0;
                        $scope.ownAdvanceData.totalAdvance = 0;
                        $scope.ownAdvanceData.totalExpenses = 0;
                        angular.forEach($scope.ownAdvanceData, function (value) {
                            console.log('value', value);
                            if (value.advance_data.advance_for_entry != '')
                                $scope.ownAdvanceData.totalEntry += parseInt(value.advance_data.advance_for_entry);
                            if (value.advance_data.advance_for_fuel != '')
                                $scope.ownAdvanceData.totalFuel += parseInt(value.advance_data.advance_for_fuel);
                            if (value.advance_data.advance_for_meals != '')
                                $scope.ownAdvanceData.totalMeals += parseInt(value.advance_data.advance_for_meals);
                            if (value.advance_data.advance_for_police != '')
                                $scope.ownAdvanceData.totalPolice += parseInt(value.advance_data.advance_for_police);
                            if (value.advance_data.total_advance != '')
                                $scope.ownAdvanceData.totalAdvance += parseInt(value.advance_data.total_advance);
                            if (value.actual_data.total_actual != '')
                                $scope.ownAdvanceData.totalExpenses += parseInt(value.actual_data.total_actual);
                            if (value.advance_data.advance_for_toll != '')
                                $scope.ownAdvanceData.totalToll += parseInt(value.advance_data.advance_for_toll);

                        });
                        console.log('$scope.ownAdvanceData', $scope.ownAdvanceData)
                    }
                    else {
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.filterData = {
                lr: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: ''
            };

            $scope.getSelectCustomerVal = function (count, filterData) {
                $scope.filter_applied = 1;
                $scope.lr_no = '';
                $scope.customer_id = '';
                $scope.origin_name = '';
                $scope.destination_name = '';
                $scope.broker = '';
                $scope.date_vehicle_required = '';
                $scope.to_date_vehicle_required = '';

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;
                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;
                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;
                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;
                if (filterData.broker)
                    $scope.broker = filterData.broker.id;
                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;
                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                $scope.filterDataObject = {
                    lr: $scope.lr_no,
                    customer: $scope.customer_id,
                    origin: $scope.origin_name,
                    destination: $scope.destination_name,
                    broker: $scope.broker,
                    from_date: $scope.date_vehicle_required,
                    to_date: $scope.to_date_vehicle_required
                };
                trackingServices.completedHistoryFilterData(count, $scope.filterDataObject).then(function (response) {
                    console.log('response', response);
                    if (response.data.results.code.status === 200) {
                        $scope.completedTripsHistory = response.data.results.trip_data;
                        $scope.completedTripPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    }
                    else {
                        alert(response.data.results.code.message)
                    }
                })
            };


            $scope.clearFilter = function () {
                //Ashish
                console.log('here');
                $scope.completedTripPage.next = '';
                $scope.filter_applied = 0;
                $scope.completedTripTrackingHistory();
                $scope.searchText = '';
                $scope.filterData = {
                    lr: '',
                    customer: '',
                    origin: '',
                    destination: '',
                    date_vehicle_required: '',
                    to_date_vehicle_required: ''
                }
            };

            $scope.asyncRequestForFilter = function () {
                //Write here function for async request
                async.parallel([
                    // function (callback) {
                    //     // request no 1
                    //     trackingServices.getAllLrHistoryLists().then(function (response) {
                    //
                    //         if (response.data.results.code.status === 200) {
                    //             $scope.lrName = response.data.results.data_list;
                    //             callback();
                    //         } else {
                    //             alert(response.data.results.code.message)
                    //         }
                    //     }, function (err) {
                    //         swal("Oops", 'No internet connection.', "error")
                    //     });
                    // },

                    function (callback) {
                        //request no 2
                        trackingServices.getAllCustomerLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.customerName = response.data.customer;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 3
                        trackingServices.getAllLocationLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.locationNmae = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 4
                        trackingServices.getAllBrokerList().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.brokerName = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },

                    function (callback) {
                        //request no 5
                        trackingServices.getAllVehicleLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.vehicleNo = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }

                ])
            };


            $scope.getLrNumbers = function (data) {
                if (data.length > 3) {
                    var lr = {
                        'lr_num': data
                    };
                    trackingServices.getLrNumSerTrc(lr).then(function (response) {
                        console.log('response', response)
                        if (response.data.code.status === 200) {
                            $scope.lrName = response.data.lr;
                        }
                        else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                }
            };

            $scope.asyncRequestForFilter();

            /**
             * functionName:getLocation
             * inputType:
             * outputType:
             * ownerName: Sushil
             * developedDate: 07/02/2018
             * testerName:
             * testDate:
             */
            $scope.getLocation = function (customerName) {
                //$scope.billModel.origin_location = '';
                //$scope.billModel.destination_location = '';
                trackingServices.getLocationLists(customerName).then(function (response) {
                    if (response.data.code.status === 200) {
                        //$scope.locations = response.data.location;
                        $scope.locationNmae = response.data.location;
                        //angular.forEach($scope.locationNmae, function (value, key) {
                        //    $scope.locationNmae[key].location_id = value.id;
                        //});
                        //console.log('new',$scope.locationNmae)
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (error) {
                    swal("Oops", 'No internet connection.', error)
                })
            };


            //$scope.distLocation = function (location) {
            //    angular.forEach($scope.locationNmae, function (value, key) {
            //        if (value.location_name == location.location_name)
            //            $scope.locationNmae.splice(key, 1);
            //    });
            //};

            $scope.showDlPicture = function (dl_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(dl_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDlPic = response.data.url
                    });
                }
                else {
                    $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
                }

            };
            $scope.showInsuPicture = function (insurance_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(insurance_pic).then(function (response) {

                        // console.log(response)
                        $scope.showInsurancePic = response.data.url
                    });
                } else {


                    $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
                }
            };
            $scope.showRcPicture = function (rc_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(rc_pic).then(function (response) {

                        // console.log(response)
                        $scope.showRcPic = response.data.url
                    });
                }
                else {


                    $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
                }
            };
            $scope.showFitPicture = function (fitness_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(fitness_pic).then(function (response) {

                        // console.log(response)
                        $scope.showFitnessPic = response.data.url
                    });
                }
                else {


                    $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
                }
            };
            $scope.showLrPicture = function (lr_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrPic = response.data.url
                    });
                }
                else {


                    $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
                }
            };
            $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehicle_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverVehiclePic = response.data.url
                    });
                }
                else {


                    $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
                }
            };
            $scope.showLrNumPicture = function (lr_num_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                }
                else {


                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }
            };
            $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(driver_vehi_pic).then(function (response) {

                        // console.log(response)
                        $scope.showDriverAndVehiclePic = response.data.url
                    });
                }
                else {

                    $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic

                }
            };
            $scope.showLrNumberPicture = function (lr_num_pic) {


                if (pic) {
                    trackingServices.picLoadServerT(lr_num_pic).then(function (response) {

                        // console.log(response)
                        $scope.showLrNumPic = response.data.url
                    });
                }
                else {
                    $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
                }


            };
            $scope.showPermitPicture = function (permit_pic) {
                if (pic) {
                    trackingServices.picLoadServerT(permit_pic).then(function (response) {

                        // console.log(response)
                        $scope.showPermitPic = response.data.url
                    });
                }
                else {

                    $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
                }
            };

            // Mukesh
            /**********************************Driver Details Get**********8*************************************/

            $scope.driverDetailsTracking = function (orderAssign) {
                trackingServices.driverDataTracking(orderAssign.id).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.driver = response.data.data_list;
                        console.log('driver', $scope.driver)
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }
                );
            };

            /****************************** Vehicle Data Get*******************************************************/

            $scope.vehicleDetailsTracking = function (vehicleData) {
                trackingServices.vehicleDataTracking(vehicleData.id).then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.vehicle = response.data.data_list;
                        console.log('vehicle', $scope.vehicle)
                    }
                    else {
                        swal("Cancelled", response.code.message, "error")
                    }
                }, function (error) {
                    alert('Error Occurred')
                })
            };

            $scope.completedTripsHistoryExportData = function (completedTripsHistory) {
                $scope.csvloading = true;
                $scope.downloadCompletedHistoryCsv = [];
                angular.forEach(completedTripsHistory, function (value, key) {
                    $scope.lr_no = "";
                    angular.forEach(value.lr.lr_data, function (value, key) {
                        $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                    });
                    $scope.lr_no = $scope.lr_no.slice(0, -1);

                    if (value.trip_end_date === null) {
                        $scope.trip_end_date = ''
                    } else {
                        $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time
                        //console.log('$scope.trip_end_date', $scope.trip_end_date)
                    }
                    if (value.is_market === true) {
                        $scope.market = 'yes'
                    } else {
                        $scope.market = 'no'
                    }
                    if (value.lane.lane_code === undefined) {
                        $scope.route = ''
                    } else {
                        $scope.route = value.lane.lane_code
                    }
                    $scope.completedHistoryExportValue = {
                        "trip_code": value.trip_code,
                        "market": $scope.market,
                        "lr_no": $scope.lr_no,
                        "customer": value.company_name + '<' + value.order_data.customer_code + '>',
                        "origin": value.order_data.origin.location_name,
                        "destination": value.order_data.destination.location_name,
                        "driver_name": value.order_data.driver_name + '/' + value.order_data.driver_no,
                        "vehicle_no": value.order_data.vehicle_no,
                        "route": $scope.route,
                        "trip_status": value.trip_status.status_name,
                        "placement_date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
                        "start_date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
                        "reporting_date": $scope.trip_end_date,
                        "buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
                    };
                    $scope.downloadCompletedHistoryCsv.push($scope.completedHistoryExportValue);
                });
                var mystyle = {
                    headers: true,
                    column: { style: { Font: { Bold: "1" } } }
                };
                alasql('SELECT * INTO XLS("Completed Trip History.xls",?) FROM ?', [mystyle, $scope.downloadCompletedHistoryCsv = $filter('orderBy')($scope.downloadCompletedHistoryCsv, 'placement_date')]);
                $scope.csvloading = false;
            };

            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.completedTripsHistory, query).length;
                });
            };
        }])

    //Ashish
    .controller('detour.alerts', ['$scope', '$state', '$rootScope', '$sce', 'trackingServices', '$filter', '$cookies', 'NgMap',
        function ($scope, $state, $rootScope, $sce, trackingServices, $filter, $cookies, NgMap) {

            $scope.alertStatus = [{ 'statusName': "ON" }, { "statusName": "SNOOZE" }, { "statusName": "OFF" }];



            $scope.asyncRequestForFilter = function () {
                //Write here function for async request
                async.parallel([

                    function (callback) {
                        trackingServices.getOwnDriverLists().then(function (response) {
                            console.log('response', response)
                            if (response.code.status === 200) {
                                $scope.driverList = response.driver_list;
                                callback();
                            } else {
                                alert(response.code.message);
                            }
                        }, function (err) {
                            swal("Oops", 'Something bad happened', "error")
                        });
                    },


                    function (callback) {
                        //request no 2
                        trackingServices.getAllCustomerLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.customerName = response.data.customer;
                                console.log('Customer response+++', response)
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'Something bad happened.', "error")
                        });
                    },

                    function (callback) {
                        //request no 5
                        trackingServices.getAllVehicleLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.vehicleNo = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }

                ])
            };


            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            $scope.alertsListPage = {};
            $scope.pagination = {};
            $scope.detourAlerts = function (count) {
                $scope.loading = true;
                $scope.searchText = '';
                trackingServices.getAlerts(count, 'detour').then(function (response) {
                    $scope.loading = false;
                    if (response.data.results.code.status == 200) {
                        $scope.alerts = response.data.results.alert_data;
                        $scope.alertsListPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    }
                    else if (response.data.code.status == 203) {
                        swal("Oops", response.data.code.message, "error")
                    }
                    else if (response.data.code.status == 300) {
                        swal("Oops", response.data.code.message, "error")
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });
            };

            $scope.getDetourHistory = function (alert) {
                console.log('here_alert', alert);

                $scope.detour_id = alert.id;
                $scope.type = 'detour';
                $scope.alert = alert;

                trackingServices.getDetourAlertHistory($scope.detour_id, $scope.type).then(function (response) {
                    console.log('detourHistoryResponse', response);
                    if (response.data.code.status === 200) {
                        $scope.actionHistory = response.data.actions;
                    }
                    else {
                        swal("oops!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.toTrusted = function (html_code) {
                return $sce.trustAsHtml(html_code);
            };

            $scope.source_name = $cookies.get('username');
            $scope.txtcomment = { name: '' };

            $scope.postAction = function (txtcomment) {
                var date = new Date();
                $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');
                $scope.actionHistory = $scope.actionHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';
                $scope.alert.last_action = txtcomment;
                $scope.txtcomment = { name: '' };
                $scope.actionHistorys = {
                    detour_id: $scope.detour_id,
                    actionHistory: $scope.actionHistory,
                    action: $scope.alert.last_action,
                    type: $scope.type
                };
                trackingServices.postDetourHistory($scope.actionHistorys).then(function (response) {
                    console.log(response);
                    if (response.code.status == 200) {

                    }
                    else {
                        swal("Cancelled", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.sortColumn = '';
            $scope.reverseSort = false;
            $scope.sortData = function (column) {
                $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
                $scope.sortColumn = column
            };

            $scope.getSortClass = function (column) {
                if ($scope.sortColumn == column) {
                    return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
                }
                else {
                    return 'fa fa-sort'
                }
            };

            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.alerts, query).length;
                });
            };

            $scope.custRoute = {
                trip_code: '',
                cust_code: ''
            };
            $scope.customerRouteMap = function (customerRoute) {
                $scope.custRoute.trip_code = customerRoute.trip_code;
                $scope.custRoute.cust_code = customerRoute.customer.code;
                trackingServices.customerRouteMapPoints($scope.custRoute).then(function (response) {
                    console.log('response', response.data.data);
                    $scope.tripWayPoints = [];
                    $scope.centerLatlong = customerRoute.latlng;
                    $scope.centerLatlong1 = {
                        lat: parseFloat($scope.centerLatlong.split(',')[0]),
                        lng: parseFloat($scope.centerLatlong.split(',')[1]),
                        name: customerRoute.location
                    };
                    $scope.customIcon = {
                        "scaledSize": [40, 40],
                        "url": "../static/apps/common/images/vehicle.png"
                    };

                    if (response.status === 200) {
                        for (var i = 0; i < response.data.data.length; i++) {
                            $scope.tripWayPoints.push({
                                location: {
                                    lat: parseFloat(response.data.data[i].split(',')[0]),
                                    lng: parseFloat(response.data.data[i].split(',')[1])
                                }, stopover: true
                            })
                        }
                        $scope.tripOrigin = $scope.tripWayPoints[0].location.lat + ',' + $scope.tripWayPoints[0].location.lng;
                        $scope.tripDestination = $scope.tripWayPoints[i - 1].location.lat + ',' + $scope.tripWayPoints[i - 1].location.lng;
                    } else {
                        swal("oops!", response.data.code.message)
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.showDetails = function (event, detail) {
                $scope.selected = detail;
                $scope.map.showInfoWindow('myInfoWindow', this);
            };

            $scope.clearRouteDeviationListing = function () {
                $scope.filterData = { 'customerCode': '', 'vehicleCode': '', 'driverCode': '', 'status': '' }
                filter_data = { 'customerCode': "", 'vehicleCode': "", 'driverCode': "", page: 1, 'status': '' };
                // trackingServices.getStoppageAlert(filter_data).then(function (response) {
                //                 console.log('stoppage lists', response)
                //                 $scope.routeDeviationAlert = response.data.details.alertData
                //                 $scope.routeDeviationAlertPagination = response.data.details.pagination


                //             }, function (err) {
                //             console.log("Error", err)
                //                 swal("Oops", err.statusText, "error")
                //             });
                trackingServices.getRouteDeviationAlert(filter_data).then(function (response) {
                    console.log('route deviation lists', response)
                    $scope.routeDeviationAlert = response.data.details.alertData
                    $scope.routeDeviationAlertPagination = response.data.details.pagination

                }, function (err) {
                    console.log("Error", err);
                    swal("Oops", 'Something bad happened.', "error")
                });
            }

            $scope.filterData = { 'customerCode': '', 'vehicleCode': '', 'driverCode': '', 'status': '' }

            $scope.getRouteDeviationListing = function (page, filterData) {
                $scope.defineData = {}

                if (filterData.vehicleCode === undefined || filterData.vehicleCode == "") {
                    $scope.defineData.vehicleCode = { 'code': "" };
                }
                else {
                    $scope.defineData.vehicleCode = filterData.vehicleCode
                }
                if (filterData.customerCode === undefined || filterData.customerCode == "") {
                    $scope.defineData.customerCode = { 'customer_code': "" };
                }
                else {
                    $scope.defineData.customerCode = filterData.customerCode
                }
                if (filterData.driverCode === undefined || filterData.driverCode == "") {
                    $scope.defineData.driverCode = { 'driver_code': "" };
                }
                else {
                    $scope.defineData.driverCode = filterData.driverCode
                }
                if (filterData.status === undefined || filterData.status == "") {
                    $scope.defineData.status = { 'statusName': "" };
                }
                else {
                    $scope.defineData.status = filterData.status
                }
                if (filterData.fromDateTime === undefined || filterData.fromDateTime == "") {
                    $scope.defineData.fromDateTime = "";
                }
                else {
                    $scope.defineData.fromDateTime = filterData.fromDateTime
                }
                if (filterData.toDateTime === undefined || filterData.toDateTime == "") {
                    $scope.defineData.toDateTime = "";
                }
                else {
                    $scope.defineData.toDateTime = filterData.toDateTime;
                }
                filter_data = { 'customerCode': $scope.defineData.customerCode.customer_code, 'vehicleCode': $scope.defineData.vehicleCode.code, 'driverCode': $scope.defineData.driverCode.driver_code, 'page': page, 'status': $scope.defineData.status.statusName, 'fromDateTime': $scope.defineData.fromDateTime, 'toDateTime': $scope.defineData.toDateTime }
                $scope.loading = true;
                trackingServices.getRouteDeviationAlert(filter_data).then(function (response) {
                    $scope.loading = false;
                    console.log('route deviation lists', response)
                    $scope.routeDeviationAlert = response.data.details.alertData
                    $scope.routeDeviationAlertPagination = response.data.details.pagination

                }, function (err) {
                    $scope.loading = false;
                    console.log("Error", err);
                    swal("Oops", err.data.message, "error")
                });
                // trackingServices.getStoppageAlert(filter_data).then(function (response) {
                //     $scope.routeDeviationAlert = response.data.details.alertData
                //     $scope.routeDeviationAlertPagination = response.data.details.pagination


                // }, function (err) {
                // console.log("Error", err)
                //     swal("Oops", response.data.message, "error")
                // });
            }


            $scope.getAlertReasonDetails = function () {
                trackingServices.getRouteDeviationReason().then(function (response) {
                    $scope.detourReasons = response.data.details
                }, function (err) {
                    console.log("Error", err)
                    swal("Oops", response.data.message, "error")
                });

            }

            $scope.detourAckCount = {}
            $scope.getReasonsCount = function (rowData) {
                $scope.rowData = rowData
                trackingServices.getAcknowledgementCount($scope.rowData.id).then(function (response) {
                    $scope.detourAckCount = response.data.details
                }, function (err) {
                    console.log("Error", err)
                    swal("Oops", 'Something bad happened.', "error")
                });
            }

            $scope.reasonData = {
                reason: ""
            }
            $scope.onReasonModalClose = function () {
                $scope.reasonData = {
                    reason: ""
                };
                $scope.detourAckCount = {}
            }
            $scope.onReasonSubmit = function (alertData, reasonData) {
                $scope.index = $scope.routeDeviationAlert.indexOf(alertData);
                if (!reasonData.reason) {
                    swal("Oops", "Please Select reason", "error")
                    return;
                }
                queryParams = {
                    alertReasonId: reasonData.reason.id,
                    alertsId: alertData.id,
                }
                trackingServices.submitAcknowledgement(queryParams).then(function (response) {
                    swal("Good job!", "Done", "success")
                    $scope.routeDeviationAlert[$scope.index].status = 'Snooze'
                    $scope.onReasonModalClose();
                    angular.element('#detourHistory').modal('hide');
                    $scope.stoppageReason = response.data.details
                }, function (err) {
                    console.log("Error", err)
                    swal("Oops", response.data.message, "error")
                });
            }

            $scope.asyncRequestForFilter();

        }])


    .controller('pilferage.alerts', ['$scope', '$state', '$rootScope', 'trackingServices', '$filter', '$cookies', '$sce',
        function ($scope, $state, $rootScope, trackingServices, $filter, $cookies, $sce) {

            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            $scope.alertsListPage = {};
            $scope.pilferageAlerts = function (count) {
                $scope.loading = true;
                $scope.searchText = '';
                trackingServices.getAlerts(count, 'pilferage').then(function (response) {
                    $scope.loading = false;
                    $scope.asyncRequestForFilter();
                    if (response.data.results.code.status == 200) {
                        $scope.alerts = response.data.results.alert_data;
                        $scope.alertsListPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    }
                    else if (response.data.code.status == 203) {
                        swal("Oops", response.data.code.message, "error")
                    }
                    else if (response.data.code.status == 300) {
                        swal("Oops", response.data.code.message, "error")
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });
            };
            $scope.sortColumn = '';
            $scope.reverseSort = false;
            $scope.sortData = function (column) {
                $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
                $scope.sortColumn = column
            };

            $scope.getSortClass = function (column) {
                if ($scope.sortColumn == column) {
                    return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
                }
                else {
                    return 'fa fa-sort'
                }
            };

            $scope.getPilferageHistory = function (alert) {
                console.log('here_alert', alert);

                $scope.detour_id = alert.id;
                $scope.type = 'pilferage';
                $scope.alert = alert;

                trackingServices.getDetourAlertHistory($scope.detour_id, $scope.type).then(function (response) {
                    console.log('detourHistoryResponse', response);
                    if (response.data.code.status === 200) {
                        $scope.actionHistory = response.data.actions;
                    }
                    else {
                        swal("oops!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.toTrusted = function (html_code) {
                return $sce.trustAsHtml(html_code);
            };

            $scope.source_name = $cookies.get('username');
            $scope.txtcomment = { name: '' };

            $scope.postAction = function (txtcomment) {
                var date = new Date();
                $scope.ddMMyyyy = $filter('date')(new Date(), 'dd-MM-yy');
                $scope.HHmmss = $filter('date')(new Date(), 'HH:mm');
                $scope.actionHistory = $scope.actionHistory + angular.copy($scope.source_name + " (" + $scope.ddMMyyyy + ' ' + $scope.HHmmss + ")" + ": " + txtcomment) + '<br />';
                $scope.alert.last_action = txtcomment;
                $scope.txtcomment = { name: '' };
                $scope.actionHistorys = {
                    detour_id: $scope.detour_id,
                    actionHistory: $scope.actionHistory,
                    action: $scope.alert.last_action,
                    type: $scope.type
                };
                trackingServices.postDetourHistory($scope.actionHistorys).then(function (response) {
                    console.log(response);
                    if (response.code.status == 200) {

                    }
                    else {
                        swal("Cancelled", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                })
            };

            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.alerts, query).length;
                });
            };

            $scope.asyncRequestForFilter = function () {
                async.parallel([
                    function (callback) {
                        trackingServices.getAllCustomerLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.customerName = response.data.customer;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                    function (callback) {
                        trackingServices.getAllVehicleLists().then(function (response) {
                            if (response.data.code.status === 200) {
                                $scope.vehicleNo = response.data.data_list;
                                callback();
                            } else {
                                alert(response.data.code.message)
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    },
                    function (callback) {
                        trackingServices.getOwnDriverLists().then(function (response) {
                            console.log('response', response)
                            if (response.code.status === 200) {
                                $scope.driverList = response.driver_list;
                                callback();
                            } else {
                                alert(response.code.message);
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                    }
                ])
            };

            $scope.filterData = {
                customer: '',
                vehicle_no: '',
                driver_id: ''
            };

            $scope.pilferageAlertFilter = function (count, filterData) {
                console.log('filterdata', filterData)
                $scope.loading = true;

                $scope.vehicle_no = '';
                $scope.customer_id = '';
                $scope.driver_id = '';

                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.customer)
                    $scope.customer_id = filterData.customer.id;

                if (filterData.driver)
                    $scope.driver_id = filterData.driver.id;

                $scope.filterDataObject = {
                    customer: $scope.customer_id,
                    vehicle_no: $scope.vehicle_no,
                    driver_id: $scope.driver_id,
                };
                console.log('$scope.filterDataObject', $scope.filterDataObject)
                trackingServices.pilferageAlertFilterService(count, $scope.filterDataObject).then(function (response) {
                    $scope.loading = false;
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    if (response.data.code.status === 200) {
                        $scope.waitingCwh = response.data.trip_data;
                    } else {
                        alert(response.data.code.message)
                    }
                })
            };

            $scope.clearPilferageAlert = function () {
                $scope.pagination.current = 1;
                $scope.searchText = '';
                $scope.pilferageAlerts();
                $scope.filterData = {
                    customer: '',
                    vehicle_no: '',
                    driver_id: ''
                };
            };


        }])

    .controller('realtime.tracking', ['$scope', '$state', 'trackingServices', '$filter',
        function ($scope, $state, trackingServices, $filter) {
            $scope.$watch('online', function (newStatus) {
                if (newStatus === false) {
                    swal("Data Connection Lost")
                }
            });

            $scope.vehileNoList = function () {
                trackingServices.getAllVehicleLists().then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.vehicleNo = response.data.data_list;
                    } else {
                        alert(response.data.code.message)
                    }
                }, function (err) {
                    swal("Oops", 'No internet connection.', "error")
                });
            };
            $scope.vehileNoList();

            $scope.trackinDataPage = { 'next': '', 'previous': '', 'count': '' };
            $scope.pagination = {};
            $scope.realtimeTrackingData = function (count) {
                $scope.loading = true;
                trackingServices.realtimeTrackingService(count).then(function (response) {
                    $scope.loading = false;
                    if (response.data.results.code.status === 200) {
                        $scope.tracking_list = response.data.results.tracking_list
                        $scope.trackinDataPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal("oops!", response.data.results.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });
            };

            $scope.VehGpsDataPage = { 'next': '', 'previous': '', 'count': '' };
            $scope.paginationGps = {};
            $scope.gpsCrossoverData = function (count) {
                $scope.loading = true;
                trackingServices.vehGpsService(count).then(function (response) {
                    if (response.data.results.code.status === 200) {
                        $scope.loading = false;
                        $scope.veh_api_list = response.data.results.data_list;
                        $scope.VehGpsDataPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal("oops!", response.data.results.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });
            };

            $scope.fetchApiName = function () {
                trackingServices.fetchApiNameService().then(function (response) {
                    if (response.data.code.status === 200) {
                        $scope.api_list = response.data.api_name;
                    } else {
                        swal("oops!", response.data.results.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });
            };

            $scope.vehApiEdit = function (cross, vehApiEditForm) {
                $scope.loading = true;
                trackingServices.vehApiEditService(cross).then(function (response) {
                    console.log('response_here__', response);
                    $scope.loading = false;
                    if (response.data.code.status === 200) {
                        $scope.veh_api_list[$scope.index_api].api_type = cross.api;
                        swal("Good job!", response.data.code.message, "success")
                    } else {
                        swal("oops!", response.data.results.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });
            };

            $scope.vehApiAdd = function (cross, vehApiEditForm) {
                console.log('cross', cross);
                $scope.loading = true;
                trackingServices.vehApiAddService(cross).then(function (response) {
                    console.log('response_here__', response);
                    $scope.loading = false;
                    if (response.data.code.status === 200) {
                        swal("Good job!", response.data.code.message, "success")
                    } else {
                        swal("oops!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });
            };

            $scope.filterData = {
                vehicle_no: '',
                from_date: '',
                from_time: '',
                to_date: '',
                to_time: ''
            };

            $scope.realtimeTrackingFilter = function (count, filterData) {
                $scope.loading = true;
                var vehicle_no = '';
                var from_date = '';
                var from_time = '';
                var to_date = '';
                var to_time = '';

                if (filterData.vehicle_no) {
                    vehicle_no = filterData.vehicle_no.vehicle_registration_number;
                }
                if (filterData.from_date) {
                    from_date = filterData.from_date;
                }
                if (filterData.from_time) {
                    from_time = filterData.from_time;
                }
                if (filterData.to_date) {
                    to_date = filterData.to_date;
                }
                if (filterData.to_time) {
                    to_time = filterData.to_time;
                }
                var from_datetime_combined = from_date + " " + from_time;
                var from_datetime = new Date(from_datetime_combined);
                var to_datetime_combined = to_date + " " + to_time;
                var to_datetime = new Date(to_datetime_combined);

                var filterDataObject = {
                    vehicle_no: vehicle_no,
                    from_date_time: from_datetime,
                    to_date_time: to_datetime
                };
                $scope.trackinDataPage = { 'next': '', 'previous': '', 'count': '' };
                trackingServices.realtimeTrackingFilterService(count, filterDataObject).then(function (response) {
                    console.log('filter_data_response', response);
                    $scope.loading = false;
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    try {
                        if (response.data.code.status === 300) {
                            swal("oops!", response.data.code.message, "error");
                            return;
                        }
                    }
                    catch (err) {
                    }
                    if (response.data.results.code.status === 200) {
                        $scope.tracking_list = response.data.results.tracking_list;
                        $scope.trackinDataPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal("oops!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });

            };

            $scope.gpsCrossoverDataFilter = function (count, filterData) {
                console.log('in_filter');
                $scope.loading = true;
                var vehicle_no = '';

                if (filterData.vehicle_no) {
                    vehicle_no = filterData.vehicle_no.vehicle_registration_number;
                }
                var filterDataObject = {
                    vehicle_no: vehicle_no
                };
                $scope.VehGpsDataPage = { 'next': '', 'previous': '', 'count': '' };
                trackingServices.gpsCrossoverDataFilterService(count, filterDataObject).then(function (response) {
                    console.log('filter_data_response', response);
                    $scope.loading = false;
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    try {
                        if (response.data.code.status === 300) {
                            swal("oops!", response.data.code.message, "error");
                            return;
                        }
                    }
                    catch (err) {
                    }
                    if (response.data.results.code.status === 200) {
                        $scope.veh_api_list = response.data.results.data_list;
                        $scope.VehGpsDataPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal("oops!", response.data.code.message, "error")
                    }
                }, function (err) {
                    alert('Error Occurred')
                });

            };

            $scope.clearFilter = function () {
                $scope.filterData = {
                    vehicle_no: '',
                    from_date: '',
                    to_date: ''
                };
                $scope.realtimeTrackingData();
            };

            $scope.clearApiFilter = function () {
                $scope.filterData = {
                    vehicle_no: ''
                };
                $scope.gpsCrossoverData();
            };

            $scope.veh_api_edit = function (t, index) {
                console.log('index', index);
                $scope.index_api = index;
                $scope.cross = { 'veh_no': t.vehicle_no, 'api': t.api_type };
            };

            $scope.veh_api_add = function (t) {
                $scope.cross = { 'veh_no': '', 'api': '' };
            };

            $scope.sortColumn = '';
            $scope.reverseSort = false;
            $scope.sortData = function (column) {
                $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
                $scope.sortColumn = column
            };

            $scope.getSortClass = function (column) {
                if ($scope.sortColumn == column) {
                    return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
                } else {
                    return 'fa fa-sort'
                }
            };

            $scope.counted = 1;
            $scope.setCountedValue = function () {
                $scope.$watch("searchText", function (query) {
                    $scope.counted = $filter("filter")($scope.alerts, query).length;
                });
            };

            $scope.realTimeExportData = function () {
                $scope.csvloading = true;
                trackingServices.realTimeExportDataService().then(function (response) {
                    $scope.csvloading = false;
                    console.log('response', response);
                    if (response.data.code.status == 200) {
                        $scope.realTimeExport = response.data.data_list;
                        $scope.downloadOnGoingCsv = [];

                        angular.forEach($scope.realTimeExport, function (value, key) {

                            if (value.is_tr) {
                                var api_type = 'Trimble Data'
                            }

                            if (value.is_tci) {
                                api_type = 'TCI Data'
                            }
                            if (value.is_gtrac) {
                                api_type = 'G-Trac'
                            }
                            if (value.is_airtel) {
                                api_type = 'Airtel'
                            }
                            if (value.is_voda) {
                                api_type = 'Vodafone'
                            }
                            if (value.is_mani) {
                                api_type = 'Mani Data'
                            }
                            if (value.is_mobilF) {
                                api_type = 'Mobile F Data'
                            }
                            if (value.is_loconav) {
                                api_type = 'Loconav Data'
                            }
                            if (value.is_wheelseye) {
                                api_type = 'Wheels Eye Data'
                            }
                            if (value.is_thinkbiz) {
                                api_type = 'Think Biz Data'
                            }
                            if (value.is_gpsvts) {
                                api_type = 'GpsVts Data'
                            }
                            if (value.is_tigps) {
                                api_type = 'TIGps Data'
                            }
                            if (value.is_fleetx) {
                                api_type = 'Fleetx Data'
                            }
                            var fuel_value = value.fuelValue;
                            if (fuel_value === null) {
                                fuel_value = 0
                            }
                            $scope.realTimeExportValue = {
                                "Vehicle Number": value.vehicle_number,
                                "Date": value.date,
                                "Stop": value.stop,
                                "Speed": value.speed,
                                "Location": value.location,
                                "Fuel Value": fuel_value,
                                "Distance Travelled": value.distance_travelled,
                                "IgnStatus": value.ignStatus,
                                "Api Type": api_type,
                                "Attached Broker": value.broker_name
                            };
                            $scope.downloadOnGoingCsv.push($scope.realTimeExportValue);
                        });
                        var mystyle = {
                            headers: true,
                            column: { style: { Font: { Bold: "1" } } }
                        };
                        alasql('SELECT * INTO CSV("Real_Time_Tracking_Data.csv",?) FROM ?', [mystyle, $scope.downloadOnGoingCsv = $filter('orderBy')($scope.downloadOnGoingCsv, '-placement_date')]);
                        $scope.csvloading = false;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                });
            };

        }]);
