<style>
    .ui-select-choices-row .location-tag {
        display: inline-block;
        background-color: #428BCA;
        color: white;
        padding: 2px 8px;
        margin: 2px;
        border-radius: 3px;
        font-size: 12px;
    }

    .ui-select-container {
        width: 100%;
    }

    .ui-select-choices-row.active .location-tag {
        background-color: #fff;
        color: #333;
    }

    .locations {
        overflow-x: auto;
        white-space: nowrap;
    }
</style>
<!-- Modal -->
<div class="modal fade" data-backdrop="static" id="change_contract_id_modal" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="margin-top: 4px; margin-left: 24px;"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Change Contract ID</h4>
            </div>
            <div class="modal-body">
                <form name="changeContractIdForm" class="form-horizontal" novalidate="">
                    <!-- <div class="form-group row">
                        <label class="col-sm-3 text-right">Contract ID</label>
                        <div class="col-md-8">
                            <select 
                                class="form-control"
                                ng-model="contract_id"
                                ng-options="contract.id as contract.id for contract in contracts">
                                <option value="">Select Contract ID</option>
                            </select>
                        </div>
                    </div> -->

                    <div class="form-group row">
                        <label for="contract_id" class="col-sm-3 control-label text-right">Contract ID</label>
                    
                        <div class="col-sm-7"
                            ng-class="{ 'has-error' : changeContractIdForm.contract_id.$invalid && (submitted || changeContractIdForm.contract_id.$dirty) }">
                    
                            <ui-select name="contract_id" ng-model="data.contract_id"
                                theme="bootstrap" ng-required="true">
                                <ui-select-match placeholder="Select Contract ID">
                                    {{ $select.selected.contract_id ? $select.selected.contract_id : 'No Contract Found' }}
                                </ui-select-match>
                                <ui-select-choices repeat="contract in contract_ids | filter: $select.search">
                                    <div class="contract-option">
                                        <div
                                            ng-bind-html="(contract.contract_id ? contract.contract_id : 'No Contract Found') | highlight: $select.search">
                                        </div>
                                        <div class="locations thin-scrollbar-2px" ng-if="contract.waypoints && contract.waypoints.length > 0">
                                            <span ng-repeat="location in contract.waypoints" class="location-tag">
                                                {{location}}
                                            </span>
                                        </div>
                                    </div>
                                </ui-select-choices>
                            </ui-select>
                    
                            <p ng-show="changeContractIdForm.contract_id.$error.required && (submitted || changeContractIdForm.contract_id.$dirty)" class="help-block">
                                Contract ID is required.</p>
                        </div>
                    </div>
                    
                    <div class="form-group row" ng-if="data.contract_id">
                        <label class="col-sm-3 text-right">Waypoints</label>
                        <div class="col-md-8">
                            <span class="chip-badge" ng-repeat="waypoint in data.contract_id.waypoints">{{waypoint}}</span>
                        </div>
                    </div>
                
                    <div class="form-group row" ng-if="data.contract_id">
                        <label class="col-sm-3 text-right">Tat</label>
                        <div class="col-md-8">
                            {{data.contract_id.tat}}
                        </div>
                    </div>
                
                    <div class="form-group row" ng-if="data.contract_id">
                        <label class="col-sm-3 text-right">Vehicle Type</label>
                        <div class="col-md-8">
                            {{data.contract_id.vehicle_type}}
                        </div>
                    </div>

                    <div class="form-group row" ng-if="data.contract_id">
                        <label class="col-sm-3 text-right">Broker Rate</label>
                        <div class="col-md-8">
                            {{data.contract_id.broker_rate}}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="clear-button btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary apply-button" ng-disabled="currentContractId === data.contract_id.contract_id || changeContractIdForm.$invalid"
                        ng-click="changeContractId(data.contract_id)">
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>