<section class="content" ng-init="vehicleTrackingData();" xmlns="http://www.w3.org/1999/html"
         xmlns="http://www.w3.org/1999/html">
    <div class="row">
        <div class="col-xs-12">
            <div class="box">
                <div class="box-header">
                    <h3 class="box-title">Vehicle Tracking</h3>

                    <!-- <div class="box-tools">
                        <div class="input-group input-group-sm" style="width: 150px;">
                            <div class="input-group-btn">
                                <button type="submit" ng-click="clearFilterTracking()" class="btn btn-default"><i
                                        class="fa fa-refresh"></i></button>
                            </div>
                            <input type="text" name="table_search" class="form-control pull-right" placeholder="Search"
                                   ng-model="searchText" ng-change="setCountedValue()">

                            <div class="input-group-btn">
                                <button type="submit" class="btn btn-default"><i class="fa fa-search"></i></button>
                            </div>
                        </div>
                    </div> -->
                </div>

                <div class="row">
                    <div class="col-md-3 ">
                        <ui-select name="customer" ng-model="filterData.lr"
                                   theme="bootstrap"
                                   ng-required="true" ng-change="getLrNumbers(filterData.lr)">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select LR">
                                {{ $select.selected.lr_reciept }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in lrName | filter: $select.search track by $index"
                                    refresh="getLrNumbers($select.search,$select)" refresh-delay="50">
                                <div ng-bind-template="{{ item.lr_reciept }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.customer"
                                   ng-init="getCustomerListNewTrack()"
                                   ng-change="getLocationNewTrack(filterData.customer);getVehNoNewTrack(filterData.customer)"
                                   theme="bootstrap"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select customer">
                                {{ $select.selected.company_name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in customerName | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.company_name }}<{{ item.customer_code }}> "></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3">
                        <div class="typahead_container">
                            <input type="text" ng-model="filterData.origin" placeholder="Type to search Origin"
                                uib-typeahead="item for item in fetchOriginLocation($viewValue)" typeahead-loading="originLoading"
                                typeahead-editable="false" class="form-control typeaheadInput" />
                            
                            <!-- Loading spinner -->
                            <span ng-show="originLoading" class="spinner-icon">
                                <i class="fa fa-spinner fa-spin"></i>
                            </span>
                            <!-- Clear button -->
                            <span ng-show="filterData.myorigin && !originLoading"
                                class="clear_icon"
                                ng-click="filterData.origin = ''">
                                <i class="glyphicon glyphicon-remove"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="typahead_container">
                            <input type="text" ng-model="filterData.destination" placeholder="Type to search Destination"
                                uib-typeahead="item as item for item in fetchDestinationLocation($viewValue)" typeahead-editable="false"
                                typeahead-loading="destinationLoading" class="form-control typeaheadInput" />
                            
                            <!-- Loading spinner -->
                            <span ng-show="destinationLoading" class="spinner-icon">
                                <i class="fa fa-spinner fa-spin"></i>
                            </span>
                            <!-- Clear button -->
                            <span ng-show="filterData.destination && !destinationLoading"
                                class="clear_icon"
                                ng-click="filterData.destination = ''">
                                <i class="glyphicon glyphicon-remove"></i>
                            </span>
                        </div>
                    </div>

                    <!-- <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.origin"
                                   theme="bootstrap"
                                   ng-change="destinationBasedOriginTrack(filterData.origin);oDBasedVehNo(filterData.customer)"
                                   ng-click="checkIfCustTrack(filterData.customer)"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Origin">
                                {{ $select.selected.location_name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in origin | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.location_name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.destination"
                                   theme="bootstrap"
                                   ng-click="checkIfCustTrack(filterData.customer)"
                                   ng-change="destinationLocationTrack(filterData.origin, filterData.destination);oDBasedVehNo(filterData.customer)"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Destination">
                                {{ $select.selected.location_name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in destination | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.location_name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div> -->
                </div>
                <br/>

                <div class="row">
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date_from"
                               ng-model="filterData.date_vehicle_required" placeholder="From 'Trip Start Date'"
                               jqdatepicker=""
                               required/>
                    </div>
                    <div class="col-md-3">
                        <input class="form-control" type="text" name="date_to"
                               ng-model="filterData.to_date_vehicle_required" placeholder="To 'Trip Start Date'"
                               jqdatepicker="" required/>
                    </div>
                    <div class="col-md-3">

                        <ui-select name="customer" ng-model="filterData.vehicle_number"
                                   theme="bootstrap"
                                   ng-init="getVehNoNewAcc(undefined)"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Vehicle No">
                                {{ $select.selected._source.vehicle_data.vehicle_registration_number }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in vehicleNo | filter: $select.search | limitTo: 20 track by $index">
                                <div ng-bind-template="{{ item._source.vehicle_data.vehicle_registration_number }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>

                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.lane_type"
                                   theme="bootstrap"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Lane Type">
                                {{ $select.selected }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in ['Express', 'Non-Express'] | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>




                </div>
                <br/>

                <div class="row">

                         <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.source_name"
                                   theme="bootstrap"
                                   ng-required="true" ng-init="getSourceList()">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Trip Source">
                                {{ $select.selected.name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in trip_source_type | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="col-md-3">
                        <ui-select name="customer" ng-model="filterData.delay"
                                   theme="bootstrap"
                                   ng-required="true">
                            <ui-select-match allow-clear="true"
                                             placeholder="Select Delay">
                                {{ $select.selected.name }}
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="item in hour | filter: $select.search track by $index">
                                <div ng-bind-template="{{ item.name }}"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="col-md-4 pull-right text-right">
                        <span type="button" class="btn btn-info apply-button pointer"
                              ng-click="vehicleTrackingFilter(count,filterData)">Apply Filter</span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <span type="button" class="btn btn-info clear-button pointer"
                              ng-click="clearFilterTracking()">Clear</span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <button type="button" class="btn btn-info pointer clear-button" ng-disabled="csvloading==true"
                                ng-click="exportTrackingData(filterData)">
                            <span ng-show="csvloading === true"><i
                                    class="glyphicon glyphicon-refresh spinning"></i></span>
                            Export CSV
                        </button>
                    </div>
                </div>
                <br/>
                <!-- /.box-header -->
                <loading></loading>
                <div id="example2_wrapper" class="dataTables_wrapper form-inline dt-bootstrap">
                    <div class="row">
                        <div class="col-sm-6"></div>
                        <div class="col-sm-6"></div>
                    </div>
                    <div class="row" ng-if="vehicleTrackingReceived.length && counted !=0">
                        <div class="col-sm-12">
                            <scrollable-table>
                                <table id="example2" class="table table-bordered table-hover dataTable" role="grid"
                                       aria-describedby="example2_info">
                                    <thead>
                                    <tr role="row">
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Browser: activate to sort column ascending">Sr. No
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Platform(s): activate to sort column ascending">
                                            Trip Code
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Platform(s): activate to sort column ascending">
                                            Order Code
                                        </th>
                                        <th>
                                            Vehicle Ref </br>ID
                                        </th>
                                        <th>
                                            Trip </br>Source
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Platform(s): activate to sort column ascending">
                                            LR Number
                                        </th>

                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Engine version: activate to sort column ascending">
                                            <br>Driver
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Browser: activate to sort column ascending">Vehicle
                                            <br>Tracking
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Engine version: activate to sort column ascending">
                                            Customer
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Route
                                        </th>

                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Start <br>Date&Time
                                        </th>

                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Previous hub
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Previous hub <br>out time
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Next
                                            Hub ETA
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="Browser: activate to sort column ascending">Vehicle
                                            <br>Number
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Start
                                            Point
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Destination
                                            <br>Point
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Start - End
                                            <br>Points
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Current <br>Location
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Stopped <br>For
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Stopped<br>Since
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">TAT/
                                            Distance <br/>Remaining
                                        </th>

                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Status
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">ETA
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending"
                                            ng-click="sortDataTracking('delay_status')">
                                            Delay <br>Status
                                            <div ng-class="getSortClassTracking('delay_status')"></div>
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Trip
                                            <br>Complete (%)
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Time
                                            <br>Complete (%)
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">
                                            Reporting <br>Date&Time
                                        </th>
                                        <th class="sorting" tabindex="0" aria-controls="example2" rowspan="1"
                                            colspan="1" aria-label="CSS grade: activate to sort column ascending">Action
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr dir-paginate="vehicleTracking in vehicleTrackingReceived | itemsPerPage: 100 | filter : searchText |orderBy:predicate:true"
                                        total-items="completedTripPage.count" current-page="pagination.current">
                                        <td>{{ ($index + 1) + ((pagination.current ? pagination.current : 1) - 1) * 100
                                            }}
                                        </td>
                                        <td ng-bind="vehicleTracking.trip_code"></td>
                                        <td ng-bind="vehicleTracking.order_code"></td>
                                        <td ng-bind="vehicleTracking.demand_reference_id"></td>
                                        <td ng-bind="vehicleTracking.source_name"></td>
                                        <td>
                                            <a href="#" ng-repeat="lr_data in vehicleTracking.lr " data-toggle="modal"
                                               data-target="#lr_num_pic"
                                               ng-click="showLrNumberPicture(lr_data.pic_url)">{{ lr_data.lr_reciept
                                                }}<br/>
                                            </a>
                                            <span ng-if="vehicleTracking.invoice_data.front_invoice">
                                    <a class="pointer" data-toggle="modal" data-target="#frontInvoice"
                                       ng-click="showFrontInvoicePicture(vehicleTracking.invoice_data.front_invoice)">Front-Invoice</a><br/>
                                </span>
                                  <span ng-if="vehicleTracking.invoice_data.back_invoice">
                                    <a class="pointer" data-toggle="modal" data-target="#backInvoice"
                                       ng-click="showBackInvoicePicture(vehicleTracking.invoice_data.back_invoice)">Back-Invoice</a>
                                </span>
                                        </td>

                                        <td><span class="pointer"
                                                  ng-class="{'red': vehicleTracking.is_market === true, 'green':vehicleTracking.is_market === false }"
                                                  data-toggle="modal" data-target="#trackingDriverDetailsPlacement"
                                                  ng-bind-template="{{ vehicleTracking.order_data.driver_name }} {{ vehicleTracking.order_data.driver_no }}"
                                                  ng-click="driverDetailsPlacement(vehicleTracking)"></span>
                                            <span class="pointer"
                                                  ng-class="{'red': vehicleTracking.is_market === true, 'green':vehicleTracking.is_market === false }"
                                                  data-toggle="modal" data-target="#trackingDriverDetailsPlacement"
                                                  ng-click="coDriverDetailsPlanning(vehicleTracking)"
                                                  ng-bind-template="{{ vehicleTracking.co_driver.driver_name }} {{ vehicleTracking.co_driver.mobile_number }}">
                                            </span>
                                        </td>
                                        <td class="pointer" ng-class="{'red': vehicleTracking.is_market === true}">
                                            <div ng-show="vehicleTracking.is_market==true">
                                                <p class="pointer" data-toggle="modal"
                                                   data-target="#tripStatus"
                                                   ng-bind="vehicleTracking.vehicle_no"
                                                   ng-click="trackingTripvehicle(vehicleTracking.id, vehicleTracking.is_market)"></p>
                                                <br/>
                                                <!--Ashish-->
                                                <a href="javascript:void(0)" data-toggle="modal"
                                                   data-target="#vehicleNoHistoryMarket"><img
                                                        src="../static/apps/common/images/trimblehistory.png"
                                                        ng-click="getVehicleLocationHistory(vehicleTracking.vehicle_no, vehicleTracking.id)"/></a>
                                            </div>

                                            <div ng-show="vehicleTracking.is_market==false">
                                                <a href="javascript:void(0)" data-toggle="modal"
                                                   data-target="#tripStatus"
                                                   ng-bind="vehicleTracking.vehicle_no"
                                                   ng-click="trackingTripvehicle(vehicleTracking.id, vehicleTracking.is_market)"></a><br/>
                                                <a href="javascript:void(0)" data-toggle="modal"
                                                   data-target="#vehicleNoHistory"><img
                                                        src="../static/apps/common/images/trimblehistory.png"
                                                        ng-click="getVehicleLocationHistory(vehicleTracking.vehicle_no, vehicleTracking.id)"/></a>
                                                <a href="javascript:void(0)" data-toggle="modal"
                                                   data-target="#lineChar"><img
                                                        src="../static/apps/common/images/graph.png"
                                                        ng-click="vehicleGraph(vehicleTracking.vehicle_no, vehicleTracking.id)"/></a>
                                            </div>
                                        </td>
                                        <td ng-bind-template="{{ vehicleTracking.company }} <{{ vehicleTracking.customer_code }}>"></td>
                                        <td ng-bind="vehicleTracking.lane.lane_code"></td>

                                        <td><span
                                                ng-bind="vehicleTracking.trip_start_date_time | date:'yyyy-MM-dd '"></span><br>
                                            <span ng-bind="vehicleTracking.trip_start_date_time | date:'HH:mm'"></span>
                                        </td>
                                        <td>
                                            <div ng-show="vehicleTracking.is_market==true">
                                                Not applicable.
                                            </div>
                                            <div ng-show="vehicleTracking.is_market==false">
                                                <p ng-bind="vehicleTracking.previous_hub.hub_name"></p>
                                            </div>
                                        </td>
                                        <td>
                                            <div ng-show="vehicleTracking.is_market==true">
                                                Not applicable.
                                            </div>
                                            <div ng-show="vehicleTracking.is_market==false">
                                                <p ng-bind="vehicleTracking.previous_hub.out_time"></p>
                                            </div>
                                        </td>
                                        <td>
                                            <div ng-show="vehicleTracking.is_market==true">
                                                Not applicable.
                                            </div>
                                            <div ng-show="vehicleTracking.is_market==false">
                                                <p ng-bind="vehicleTracking.eta_next"></p>
                                            </div>
                                        </td>
                                        <td><span class="pointer"
                                                  ng-class="{'red': vehicleTracking.is_market === true, 'green':vehicleTracking.is_market === false }"
                                                  data-toggle="modal"
                                                  data-target="#biltyVehicleDetails"
                                                  ng-bind="vehicleTracking.vehicle_no"
                                                  ng-click="vehicleDetailsPlacement(vehicleTracking)">
                                        </span>
                                        </td>
                                        <td ng-bind="vehicleTracking.start_point.location_name"></td>
                                        <td ng-bind="vehicleTracking.destination_point.location_name"></td>
                                        <td>
                                            <a ng-if="vehicleTracking.order_data.multiple_stopage" class="pointer" data-toggle="modal" data-target="#touchPoints" ng-click="getMultiplePoint(vehicleTracking)">
                                                {{dataBindD(vehicleTracking.start_point.location_name, vehicleTracking.start_point.location_code, vehicleTracking.order_data.multiple_stopage, vehicleTracking.destination_point.location_name,  vehicleTracking.destination_point.location_code)}}
                                            </a>
                                            <span ng-hide="vehicleTracking.order_data.multiple_stopage">
                                                {{dataBindD(vehicleTracking.start_point.location_name, vehicleTracking.start_point.location_code, vehicleTracking.order_data.multiple_stopage, vehicleTracking.destination_point.location_name,  vehicleTracking.destination_point.location_code)}}
                                            </span>
                                        </td>
                                        <td>
                                            <span
                                                ng-bind="vehicleTracking.current_location.location"></span>
                                            <br>
                                            <span ng-bind="vehicleTracking.current_date_stamp"></span>
                                        </td>

                                        <td>
                                            <div ng-if="vehicleTracking.stop_data.duration < 900 && vehicleTracking.is_gps_lost == false ">
                                                NA
                                            </div>
                                            <div ng-if="vehicleTracking.stop_data.duration >= 900 && vehicleTracking.is_gps_lost == false">
                                                <p ng-bind-template="{{ vehicleTracking.stop_data.duration | secondsToHM }}"
                                                   ng-style='{"color" : "red"}'></p>
                                            </div>

                                            <div ng-if="vehicleTracking.is_gps_lost == true">
                                                <p ng-style='{"color" : "red"}'>GPS Signal Lost</p>
                                            </div>

                                        </td>
                                        <td>
                                            <div ng-if="vehicleTracking.stop_data.duration < 900  && vehicleTracking.is_gps_lost == false ">
                                                NA
                                            </div>
                                            <div ng-if="vehicleTracking.stop_data.duration >= 900 && vehicleTracking.is_gps_lost == false ">
                                                <p ng-bind="vehicleTracking.stop_data.date_time | date :  'yyyy-MM-dd h:mm:ss'"
                                                   ng-style='{"color" : "red"}'></p> To <p ng-bind="vehicleTracking.stop_data.to_d | date :  'yyyy-MM-dd h:mm:ss'"  ng-style='{"color" : "red"}'>

                                            </p>
                                            </div>
                                            <div ng-if="vehicleTracking.is_gps_lost == true ">
                                                 <p ng-bind="vehicleTracking.stop_data.date_time | date :  'yyyy-MM-dd h:mm:ss'"
                                                   ng-style='{"color" : "red"}'></p>To <p ng-bind="vehicleTracking.stop_data.to_d | date :  'yyyy-MM-dd h:mm:ss'"  ng-style='{"color" : "red"}'>
                                            </div>

                                        </td>
                                        <td><span ng-bind="vehicleTracking.tat"></span>
                                        /<br>
                                            <span ng-bind-template="{{ vehicleTracking.eta_data.current_distance * 0.001  }}km"></span>
                                        </td>

                                        <td ng-bind="vehicleTracking.trip_status.status_name"></td>
                                        <td>{{ vehicleTracking.final_eta_backend }}
                                        </td>
                                        <td><span ng-style="{ 'color' : (vehicleTracking.delay_status.charAt(0) >=1) ? 'red' : '' }"
                                                  ng-bind="vehicleTracking.delay_status_backend"></span>

                                                  <div>
                                                    <a href="javascript:void(0)" data-toggle="modal"
                                                       data-target="#Unscheduledstatus"
                                                       ng-click="unscheduleStoppagedeatils(vehicleTracking.id,vehicleTracking.is_market, vehicleTracking.trip_code, vehicleTracking)">Unscheduled <br> Stoppages</a><br>
                                            <p ng-if="vehicleTracking.route_not_updated == true" ng-style='{"color" : "brown"}'> --> Route not found. Old data shown.</p>
                                        </td>

                                        <!--Ashish-->
                                        <td ng-if="vehicleTracking.trip_complete != 'Location Not Available'">
                                            <div class="progress progress-xs"
                                                 style="height: 15px; background: lightgrey">
                                                <div class="progress-bar progress-bar-green"
                                                     style="width: {{ vehicleTracking.trip_complete }}%">
                                                </div>
                                            </div>
                                            {{ vehicleTracking.trip_complete }}%
                                        </td>
                                        <td ng-if="vehicleTracking.trip_complete == 'Location Not Available'">
                                            {{ vehicleTracking.trip_complete }}
                                        </td>
                                        <td>
                                            <div class="progress progress-xs"
                                                 style="height: 15px; background: lightgrey">
                                                <div class="progress-bar progress-bar-green"
                                                     style="width: {{ vehicleTracking.time_complete }}%">
                                                </div>
                                            </div>
                                            {{ vehicleTracking.time_complete }}%
                                        </td>

                                        <td ng-bind-template="{{ vehicleTracking.trip_end_date | date:'yyyy-MM-dd ' }}
                                                {{ vehicleTracking.trip_end_time | date:'HH:mm' }}"></td>
                                        <td>
                                            <div class="divd_wrap"><span class="h_text">Vehicle</span></div>
                                            <div class="grid_items_group">
                                                <a href="" class="pointer"
                                                    data-toggle="modal" data-target="#addLocation"
                                                    ng-click="addLoc(vehicleTracking, $index)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Grab Vehicle Location">
                                                    <img src="../static/apps/common/images/grab-location.svg"/>
                                                    </span>
                                                </a>
                                                <a href="" class="pointer "
                                                data-toggle="modal" data-target="#tracking_vehicle_location_modal"
                                                ng-click="trackTrip(vehicleTracking); getRoutePlacesDetails(vehicleTracking.route_assign.routeCode);
                                                getRouteDetails(vehicleTracking.route_assign.routeCode); getTrackingDetails(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Show Vehicle Location">
                                                    <img src="../static/apps/common/images/vehicle-location.svg"/>
                                                    </span>
                                                </a>
                                                <a href="" class="pointer "
                                                data-toggle="modal" data-target="#trackingDateTime"
                                                ng-click="dateTime(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Vehicle Reported">
                                                    <img src="../static/apps/common/images/vehicle-placed.svg"/>
                                                    </span>
                                                </a>
                                                <a href="" class="pointer"
                                                data-toggle="modal" data-target="#trackingUnloading"
                                                ng-click="unlodingDateTime(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Vehicle Unloaded">
                                                    <img src="../static/apps/common/images/unloading.svg"/>
                                                    </span>
                                                </a>
                                                <!-- <a href="" class="pointer"
                                                    data-toggle="modal" data-target="#loading_points"
                                                    ng-click="loading_points(vehicleTracking.loading_points, vehicleTracking.contract_id)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Loading Points">
                                                    <img src="../static/apps/common/web-font/checklist.png"/>
                                                    </span>
                                                </a>  -->
                                                <a ng-if="vehicleTracking.source_name=='OWNED' && vehicleTracking.is_maintenance_created && vehicleTracking.trip_status.status_name!=='Vehicle Reported'" href="" class="pointer"
                                                    data-toggle="modal" data-target="#breakdownModal"
                                                    ng-click="breakdownDetailsModal(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Breakdown">
                                                        <img src="../static/apps/common/web-font/MaintenanceActive.svg"/>
                                                    </span>
                                                </a>
                                                <a ng-if="vehicleTracking.is_market==true && vehicleTracking.trip_status.status_name!=='Vehicle Reported'" href="javascript:void(0)" class="pointer"  
                                                    data-toggle="modal" data-target="#updateDriverPhone" ng-click="setModalData(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"  title="Update Phone No.">
                                                        <img src="../static/apps/common/web-font/mobile.svg" />
                                                    </span>
                                                </a>
                                                <a ng-if="vehicleTracking.source_name=='OWNED' && !vehicleTracking.is_maintenance_created && vehicleTracking.trip_status.status_name!=='Vehicle Reported'" href="" class="pointer"
                                                    ng-click="redirectToMaintenance(vehicleTracking.vehicle_no)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Breakdown">
                                                    <img src="../static/apps/common/images/breakdown_icon.svg"/>
                                                    </span>
                                                </a>
                                                <a href="" class="pointer"
                                                    data-toggle="modal" data-target="#add_driver"
                                                    ng-click="addDriver(vehicleTracking.id)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Add Driver">
                                                    <img src="../static/apps/common/images/driver.svg"/>
                                                    </span>
                                                </a>
                                                <a href="javascript:void(0)" class="pointer"  
                                                    data-toggle="modal" data-target="#lock_status_modal" ng-click="getDhanukaLockInfo(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"  title="Lock Status">
                                                        <img src="../static/apps/common/web-font/lock.png"  />
                                                    </span>
                                                </a>
                                            </div>
                                            <div class="divd_wrap"><span class="h_text">Finance</span></div>
                                            <div class="grid_items_group">
                                                <a href="" class="pointer"
                                                data-toggle="modal" data-target="#fin_atr"
                                                ng-click="finGetTracking(vehicleTracking)">
                                                    <span data-toggle="tooltip" data-placement="top"
                                                        title="Finance Attributes">
                                                    <img src="../static/apps/common/images/finance_attributes.svg"/>
                                                    </span>
                                                </a>
                                            </div>    
                                            <div class="divd_wrap"><span class="h_text">Contract</span></div>
                                            <div class="grid_items_group">
                                                <a href="javascript:void(0)" ng-class="{'img-disabled': vehicleTracking.trip_status.status_name !== 'In Transit'}"
                                                    class="pointer" data-toggle="modal" data-target="#change_contract_id_modal"
                                                    ng-click="fetchContractIds(vehicleTracking)"
                                                    ng-disabled="vehicleTracking.trip_status.status_name !== 'In Transit'">
                                                    <span data-toggle="tooltip" data-placement="top" title="Change Contract ID">
                                                        <img src="../static/apps/common/images/contract_id.svg" />
                                                    </span>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </scrollable-table>
                            <div ng-if='wf ==0'>
                                <dir-pagination-controls max-size="8" direction-links="true" boundary-links="true"
                                                         on-page-change="vehicleTrackingData(newPageNumber)"
                                                         class="ng-isolate-scope"></dir-pagination-controls>
                            </div>
                            <div ng-if='wf == 1'>
                                <dir-pagination-controls max-size="8" direction-links="true" boundary-links="true"
                                                         on-page-change="vehicleTrackingFilter(newPageNumber, filterData)"
                                                         class="ng-isolate-scope"></dir-pagination-controls>
                            </div>
                        </div>

                    </div>
                    <div ng-if="(!vehicleTrackingReceived.length || counted ==0) && !loading">
                        <div class="col-md-4"></div>
                        <div class="col-md-4">
                            <img style="margin-top: 50px" src="../static/apps/common/images/noData.png">
                        </div>
                        <div class="col-md-4"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section>

<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/vehicle_number_history.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/breakdown-modal.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/vehicle_graph.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/vehicle_number_history_market.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/trip_status.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/date_time.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/unloading.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/add_driver.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/touch_point.html'"></div>

<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/contract_loading_points.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/finance_attrib.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/lock_status_modal.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/change_contract_id_modal.html'"></div>


<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/trip/placement_driver_details.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/bilty/template/trip/vehicle_details.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/bilty/template/trip/update_mobile_modal.html'"></div>

<div class="modal fade" id="lr_num_pic" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <header class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close Modal</span></button>
                <h4 class="modal-title">LR</h4>
            </header>
            <div class="modal-body">
                <!-- Show iframe for PDFs -->
                <iframe ng-if="isPdf(showLrNumPic)" ng-src="{{ trustSrc(showLrNumPic) }}" width="100%" height="500px"
                    frameborder="0" allowtransparency="true"></iframe>
            
                <!-- Show image for images -->
                <img ng-if="!isPdf(showLrNumPic)" ng-src="{{ showLrNumPic }}" height="auto" width="100%" />
            </div>
            <!-- form start -->
            <!--<form class="form-horizontal" novalidate="">-->
                <!--<div align="center">-->
                    <!--<img ng-src="{{ showLrNumPic }}" height=auto width=100%/>-->
                <!--</div>-->
            <!--</form>-->
        </div>
    </div>
</div>

<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/v2/tracking/tracking_vehicle_location_modal.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/v2/tracking/tracking_send_message_modal.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/v2/tracking/tracking_driver_sim_location.html'"></div>

<div ng-include="'../static/apps/gobolt_opc/tracking_team/template/tracking/history_ping_view.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/bilty/template/trip/front-invoice.html'"></div>
<div ng-include="'../static/apps/gobolt_opc/bilty/template/trip/back-invoice.html'"></div>
<script>
    $('[data-toggle="tooltip"]').tooltip({
        trigger: 'hover'
    });
</script>





<div class="modal" id="addLocation" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Grab Co-ordinates </h4>
            </div>
            <div class="modal-body">
                <form name="routeform" class="form-horizontal">
                    <div class="form-group">

                        <label for="inputPassword3" class="col-sm-2 control-label">Search Address</label>

                        <div class="col-sm-4">
                            <input type="text" name="search_add" class="form-control" id="GLSearch"
                                   placeholder="Search Address"
                                   ng-autocomplete-add-locations="location.location_address"
                                   ng-model="location.location_address" ng-change="showMarker(location)" required/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label">Latitude</label>

                        <div class="col-sm-4"
                             ng-class="{ 'has-error' : routeform.con_route.$invalid && !routeform.con_route.$pristine }">
                            <input type="text" name="con_route" class="form-control" id="inputPassword3"
                                   placeholder="Latitude"
                                   ng-model="location.lat" required/>

                            <p ng-show="routeform.con_route.$dirty && routeform.con_route.$error.required"
                               class="help-block">Latitude is required.</p>
                        </div>

                        <label for="inputPassword3" class="col-sm-2 control-label">Longitude</label>

                        <div class="col-sm-4"
                             ng-class="{ 'has-error' : routeform.alt_route.$invalid && !routeform.alt_route.$pristine }">
                            <input type="text" name="alt_route" class="form-control" id="inputPassword3"
                                   placeholder="Longitude"
                                   ng-model="location.long" required/>

                            <p ng-show="routeform.alt_route.$dirty && routeform.alt_route.$error.required"
                               class="help-block">Longitude is required.</p>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary apply-button" ng-disabled="!location.long "
                                data-dismiss="modal" ng-click="add_loc(location)">
                            Grab
                        </button>
                    </div>
                </form>

                <div class="box-body">
                    <ng-map zoom="10" id="locTrackingMap" center="20.5937,78.9629" style="height:500px">
                        <marker
                                icon="{{ customIcon.url }}"
                                position="{{ markerLatLong }}">

                        </marker>
                        <directions ng-if="locRefresh"
                                    draggable="false"
                                    panel="directions-panel"
                                    waypoints="{{ loc_wayPoints }}"
                                    origin="{{ loc_origin }}"
                                    destination="{{ loc_destination }}">
                        </directions>
                    </ng-map>
                </div>
            </div>
        </div>
    </div>
</div>


