/**
 * Created by som on 4/9/16.
 */
/**
 * Created by dell on 19-08-2016.
 */




angular.module('myApp')

    .service('trackingServices', ['$http', '$q', function ($http, $q) {

        this.picLoadServerT = picLoadServerT;
        this.addLocation = addLocation;
        this.sendRouteAlert = sendRouteAlert;
        this.tracking_trip_listing = tracking_trip_listing;
        this.onGoingTrip = onGoingTrip;
        this.getTripListPlaning = getTripListPlaning;
        this.completedTrip = completedTrip;
        this.getCompletedTripHistory = getCompletedTripHistory;
        this.getAssignOrderList = getAssignOrderList;
        this.postReportingDateTimeTrip = postReportingDateTimeTrip;
        this.unloadingDateTime = unloadingDateTime;
        this.vehicle_tracking = vehicle_tracking;
        this.assignOrderAlertHistory = assignOrderAlertHistory;
        this.postCheckHistory = postCheckHistory;
        this.getVehicleLocationHistoryService = getVehicleLocationHistoryService;
        this.getVehicleStatus = getVehicleStatus;
        this.getHubInfo = getHubInfo;
        this.getStartEndPoint = getStartEndPoint;
        this.getHubWaiting = getHubWaiting;
        this.hubWaitingAlertHistory = hubWaitingAlertHistory;
        this.postHubWaitingHistory = postHubWaitingHistory;
        this.getUnScheduledStopage = getUnScheduledStopage;
        this.getUnScheduleStopage = getUnScheduleStopage;
        this.getStoppageAlertNew= getStoppageAlertNew;
        this.postUnScheduledHistory = postUnScheduledHistory;
        this.waitingAtCWH = waitingAtCWH;
        this.waitingCwhAlertHistory = waitingCwhAlertHistory;
        this.postWaitingCwhAlertHistory = postWaitingCwhAlertHistory;
        this.unScheduledAlertHistory = unScheduledAlertHistory;
        this.driverDataTracking = driverDataTracking;
        this.vehicleDataTracking = vehicleDataTracking;
        this.postVehicleGraph = postVehicleGraph;
        /*************************Filter services************************/
        this.getAllLrLists = getAllLrLists;
        this.getAllCustomerLists = getAllCustomerLists;
        this.getAllLocationLists = getAllLocationLists;
        this.getAllBrokerList = getAllBrokerList;
        this.getAllVehicleLists = getAllVehicleLists;
        this.completedTripFilterService = completedTripFilterService;
        this.completedHistoryFilterData = completedHistoryFilterData;
        this.ongoingTripFilterService = ongoingTripFilterService;
        this.getAllLrOngoingLists = getAllLrOngoingLists;
        this.getAllLrHistoryLists = getAllLrHistoryLists;

        //Ashish
        this.get_chart_data = get_chart_data;
        this.driverOnlyDataList = driverOnlyDataList;
        this.vehicleDetailsList = vehicleDetailsList;
        this.advancePayDetails = advancePayDetails;
        this.ownAdvancePayDetails = ownAdvancePayDetails;
        this.latLngInRoute = latLngInRoute;
        this.coDriverDataPlanning = coDriverDataPlanning;
        this.getAlerts = getAlerts;
        this.postDetourHistory = postDetourHistory;
        this.customerRouteMapPoints = customerRouteMapPoints;
        this.getDetourAlertHistory = getDetourAlertHistory;
        this.vehicleTrackingFilterService = vehicleTrackingFilterService;
        this.postExportData = postExportData;
        this.postCompleteExportData = postCompleteExportData;
        this.markIdealTrip = markIdealTrip;
        this.send_w_message = send_w_message;
        this.getAllCustomerListsNew = getAllCustomerListsNew;
        this.getLocationListsNew = getLocationListsNew;
        this.getAllVehNoListNew = getAllVehNoListNew;
        this.vehGpsService = vehGpsService;
        this.fetchApiNameService = fetchApiNameService;
        this.vehApiEditService = vehApiEditService;
        this.vehApiAddService = vehApiAddService;
        this.gpsCrossoverDataFilterService = gpsCrossoverDataFilterService;

        // Mukesh Gautam
        this.realtimeTrackingService = realtimeTrackingService;
        this.realtimeTrackingFilterService = realtimeTrackingFilterService;
        this.realTimeExportDataService = realTimeExportDataService;

        this.getAllOrderIdsLists = getAllOrderIdsLists;
        this.getAllCustomerLists = getAllCustomerLists;
        this.getAllLocationLists = getAllLocationLists;
        this.getLocationLists = getLocationLists;
        this.placementAssuranceService = placementAssuranceService;
        this.cwhFilterService = cwhFilterService;
        this.getOwnDriverLists = getOwnDriverLists;
        this.getTripLists = getTripLists;
        this.pilferageAlertFilterService = pilferageAlertFilterService;
        this.last_x_dist = last_x_dist;
        this.updateMultiPoints = updateMultiPoints;
        this.updateLoadPoints = updateLoadPoints;
        this.removeLoadPoints = removeLoadPoints;
        this.unscheduleFilter = unscheduleFilter;
        this.updateDriver = updateDriver;
        this.getNewDriver = getNewDriver;
        this.getLrNumSerTrc = getLrNumSerTrc;
        this.getSimBasedTrackingData = getSimBasedTrackingData;

        this.getStoppageAlert = getStoppageAlert;
        this.getRouteDeviationAlert = getRouteDeviationAlert;
        this.getRouteDeviationReason = getRouteDeviationReason;
        this.getStoppageReason = getStoppageReason;
        this.getAcknowledgementCount = getAcknowledgementCount;
        this.submitAcknowledgement = submitAcknowledgement;
        this.getLatestAlert=getLatestAlert


        // ##############################################################

        this.getRoutePlacesDetails = getRoutePlacesDetails;
        this.getRouteDetails = getRouteDetails;
        this.getSourceList = getSourceList
        this.getVehicleMaintenanceData = getVehicleMaintenanceData;

        // to get tracking data for sim or gps
        this.getVehicleTrackingDetails = getVehicleTrackingDetails;
        // to get driver's consent for sim based tracking
        this.getDriverConsentForSimTracking = getDriverConsentForSimTracking;
        // to get history of pings for particular vehicle
        this.getHistoryPings = getHistoryPings;
        //to change the market driver's phone number
        this.changeDriverMobileNumber = changeDriverMobileNumber;

        this.getDhanukaLockInfo = getDhanukaLockInfo;
        this.fetchContractIds = fetchContractIds;
        this.changeContractId = changeContractId;
        this.fetchCustomerLocation = fetchCustomerLocation;

        function getVehicleMaintenanceData(maintenance_code){
            url = '/gobolt/trip/get-maintenance-details/';
            var deferred = $q.defer();
            $http.get(url, {params: { maintenance_code } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getSourceList(){
            url = '/gobolt/vehicle/source-list/';
            var deferred = $q.defer();
            $http.get(url).then(function (data) {
                deferred.resolve(data);

            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;

        }


        function getStoppageAlert(count,data) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/alert/stoppages/'
            }
            else {
                url = '/gobolt/trip/alert/stoppages/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url, { params: data }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function getRouteDeviationAlert(data) {
            url = '/gobolt/trip/alert/route/deviation/'
            var deferred = $q.defer();
            $http.get(url, { params: data }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function getRouteDeviationReason() {
            url = '/gobolt/trip/alert/route/deviation/reason/'
            var deferred = $q.defer();
            $http.get(url).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getStoppageReason() {
            url = '/gobolt/trip/alert/stoppage/reason/'
            var deferred = $q.defer();
            $http.get(url).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getAcknowledgementCount(alertId) {
            url = '/gobolt/trip/alert/acknowledgement/count/'
            console.log('alert id ', alertId)

            var deferred = $q.defer();
            $http.get(url, { params: { 'alertId': alertId } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getLatestAlert(alertsId) {
            url = '/gobolt/trip/alert/latest/'
            console.log('alert id ', alertsId)

            var deferred = $q.defer();
            $http.get(url, { params: { 'alertsId': alertsId } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function submitAcknowledgement(data) {
            url = '/gobolt/trip/alert/acknowledge/'
            console.log(data)
            var deferred = $q.defer();
            $http.post(url, data).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        /**
         * Get Pic From Server
         * @param d
         */
        function picLoadServerT(d) {
            url = '/gobolt/pic/load/';
            var deferred = $q.defer();
            $http.get(url, { params: { 'path': d } }).then(function (data) {
                deferred.resolve(data);

            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;

        }


        /**
         *
         * @returns {Promise}
         */


        function tracking_trip_listing() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/data/gobolt-tracking/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getTripListPlaning() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/data/planning/team/').then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getAssignOrderList() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/order-assign/').then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function onGoingTrip(urlData) {
            if (urlData != null && urlData != '') {
                url = urlData
            }
            else {
                url = '/gobolt/trip/ongoing-trip/'
            }
            var deferred = $q.defer();
            $http.get(url).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
         * functionName:completedTrip
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        function completedTrip(count) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/completed-trip/vehicle/tracking/';
            }
            else {
                url = '/gobolt/trip/completed-trip/vehicle/tracking/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        //Ashish
        function getCompletedTripHistory(count) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/complete/trip/history/';
            }
            else {
                url = '/gobolt/trip/complete/trip/history/?page_no=' + count;
            }

            var deferred = $q.defer();
            $http.get(url).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function addLocation(lat, long, trip_id, vehicle_no) {
            var location = { 'lat': lat };
            location.long = long;
            location.trip_id = trip_id;
            location.vehicle_no = vehicle_no;
            var deferred = $q.defer();
            $http.post('/gobolt/trip/add-location/', location).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function sendRouteAlert(route, number) {
            route.number = number;
            var deferred = $q.defer();
            $http.post('/gobolt/trip/send-alert/', route).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function postReportingDateTimeTrip(reportingDateTimeTrip) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/reporting/date/time/', reportingDateTimeTrip).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function unloadingDateTime(unloadingDateTimeTrip) {
            var deferred = $q.defer();
            $http.put('/gobolt/trip/unloading/date/time/', unloadingDateTimeTrip).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
         * functionName:vehicle_tracking
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        function vehicle_tracking(count) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/tracking/own-vehicle/';
            }
            else {
                url = '/gobolt/trip/tracking/own-vehicle/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function assignOrderAlertHistory(order_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/order/comment/alert/', { 'params': { 'order_id': order_id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function postCheckHistory(alertHistory) {
            var deferred = $q.defer();
            $http.put('/gobolt/trip/order/comment/alert/', alertHistory).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
         * By - Vishnu
         * @param vehicleNo
         * @param data_id
         */

        function getVehicleLocationHistoryService(vehicleNo, data_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/current/dump/', {
                'params': {
                    'vehicle_no': vehicleNo,
                    'id': data_id
                }
            }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function getVehicleStatus(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/previous/next/hub/', { 'params': { 'trip_id': id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getHubInfo(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/hub/previous/', { 'params': { 'trip_id': id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getStartEndPoint(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/start/end/eta/', { 'params': { 'trip_id': id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getHubWaiting() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/hub/waiting/list/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function hubWaitingAlertHistory(trip_id, hub_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/set/alert/hub/', {
                'params': {
                    'trip_id': trip_id,
                    'hub_id': hub_id
                }
            }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function postHubWaitingHistory(alertHistorys) {
            var deferred = $q.defer();
            $http.put('/gobolt/trip/tracking/get/set/alert/hub/', alertHistorys).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getUnScheduledStopage() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/data/un/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function unScheduledAlertHistory(unschedule_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/set/un/alert/', {
                'params': {
                    'unschedule_id': unschedule_id
                }
            }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getUnScheduleStopage(trip_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/unschedule/stoppage/', { 'params': { 'trip_id': trip_id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function getStoppageAlertNew(tripCode) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/alert/with/reason/', { 'params': { 'tripCode': tripCode } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function postUnScheduledHistory(alertHistorys) {
            var deferred = $q.defer();
            $http.put('/gobolt/trip/tracking/get/set/un/alert/', alertHistorys).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function waitingAtCWH() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/waiting/cwh/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function waitingCwhAlertHistory(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/tracking/get/set/alert/cwh/', { 'params': { 'trip_id': id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function postWaitingCwhAlertHistory(alertHistorys) {
            var deferred = $q.defer();
            $http.put('/gobolt/trip/tracking/get/set/alert/cwh/', alertHistorys).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
         * Prafull
         */

        function driverDataTracking(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/assign/driver/onlydata/get/', { params: { 'id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }


        function vehicleDataTracking(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/assign/vehicle/data/get/', { params: { 'id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        /*****************************************************/
        //Filter
        /******************************************************/

        function getAllLrLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/lr-complete-trip/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllCustomerLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/create-indent/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllLocationLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/finance/customer/locations/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllBrokerList() {
            var deferred = $q.defer();
            $http.get('/gobolt/finance/broker/list/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllVehicleLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/finance/vehicle/all/list/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        /**
         * functionName:vehicle_tracking
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        function completedTripFilterService(count, completedTripFilter) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/completed-trip/vehicle/tracking/filter/';
            }
            else {
                url = '/gobolt/trip/completed-trip/vehicle/tracking/filter/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, completedTripFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        //Ashish
        function completedHistoryFilterData(count, BPfilterData) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/filter/completed-history/';
            }
            else {
                url = '/gobolt/trip/filter/completed-history/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, BPfilterData).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function ongoingTripFilterService(urlData, ongoingTripFilter) {
            if (urlData != null && urlData != '') {
                url = urlData
            }
            else {
                url = '/gobolt/trip/filter/ongoing/'
            }
            var deferred = $q.defer();
            $http.post(url, ongoingTripFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function postVehicleGraph(vehicleNo, data_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/fetching/fuel/vehicle-average/data/', {
                'params': {
                    'vehicle_no': vehicleNo,
                    'id': data_id
                }
            }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getAllLrOngoingLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/lr-ongoing-trip').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllLrHistoryLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/lr-history-trip').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        //Ashish
        function get_chart_data(vehicleNo, data_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/fetching/fuel/vehicle-average/data/', {
                'params': {
                    'vehicle_no': vehicleNo,
                    'id': data_id
                }
            }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getAlerts(count, type) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/alerts/';
            }
            else {
                url = '/gobolt/trip/alerts/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url, { params: { 'type': type } }).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function postDetourHistory(actionHistorys) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/detour/history/', actionHistorys).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function customerRouteMapPoints(customerRoute) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/plot/latlng/', customerRoute).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function customerRouteMapPoints(customerRoute) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/plot/latlng/', {
                params: {
                    'trip_code': customerRoute.trip_code,
                    'cust_code': customerRoute.cust_code
                }
            }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }


        function getDetourAlertHistory(id, type) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/detour/history/', { params: { 'id': id, 'type': type } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function driverOnlyDataList(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/assign/driver/onlydata/get/', { params: { 'id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function vehicleDetailsList(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/assign/vehicle/data/get/', { params: { 'id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function advancePayDetails(code) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/advance/pay/details', { params: { 'order_code': code } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function ownAdvancePayDetails(code) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/advance/pay/details/own', { params: { 'order_code': code } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function latLngInRoute(ordinates) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/lat/lng/route/', ordinates).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function coDriverDataPlanning(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/assign/co-driver/data/get/', { params: { 'id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function postExportData(filterData) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/filter/tracking-without-pagination/', filterData).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        /**
         * Created By: Prafull
         * @param count
         * @param vehicleTrackingFilter
         * @returns {*}
         */
        function vehicleTrackingFilterService(count, vehicleTrackingFilter) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/filter/vehicle-tracking-filter/';
            }
            else {
                url = '/gobolt/trip/filter/vehicle-tracking-filter/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, vehicleTrackingFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function postCompleteExportData(filterData) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/filter/completed-without-pagination/', filterData).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function vehApiEditService(cross) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/veh/api/edit/', cross).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function vehApiAddService(cross) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/veh/api/add/', cross).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function send_w_message(data) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/send/w/message/', data).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function getAllCustomerListsNew() {
            var deferred = $q.defer();
            $http.get('/gobolt/get/customer/list/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getLocationListsNew(customerName) {
            var deferred = $q.defer();
            $http.post('/gobolt/getting-location/new/customer/', customerName).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }


        function getAllVehNoListNew(id) {
            var deferred = $q.defer();
            $http.get('/gobolt/finance/vehicle/all/list/new/', { 'params': { 'customer_id': id } }).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function markIdealTrip(trip_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/mark/ideal/', { params: { 'trip_id': trip_id } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function fetchApiNameService() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/get/apiname/').then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        // Mukesh Gautam
        function realtimeTrackingService(count) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/vehicle/realtime-tracking-data/';
            } else {
                url = '/gobolt/vehicle/realtime-tracking-data/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function vehGpsService(count) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/veh-gps-api/';
            } else {
                url = '/gobolt/trip/veh-gps-api/?page=' + count;
            }
            var deferred = $q.defer();
            $http.get(url).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        //Mukesh Gautam
        function realtimeTrackingFilterService(count, filterData) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/vehicle/filter/realtime-tracking-data/';
            } else {
                url = '/gobolt/vehicle/filter/realtime-tracking-data/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, filterData).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function gpsCrossoverDataFilterService(count, filterData) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/filter/veh-gps-api/';
            } else {
                url = '/gobolt/trip/filter/veh-gps-api/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, filterData).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function realTimeExportDataService() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/real-time-export-csv/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllOrderIdsLists(search) {
            var deferred = $q.defer();
            $http.get('/gobolt/customer/care/get-order-ids/', { params: { 'search': search } }).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllCustomerLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/create-indent/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getAllLocationLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/finance/customer/locations/').then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function getLocationLists(customerName) {
            var deferred = $q.defer();
            $http.post('/gobolt/getting-location/customer/', customerName).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function last_x_dist(trip) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/fetch/last_x_dist/', {
                'params': {
                    'trip_code': trip.trip_code,
                    'cust_id': trip.cust_id, 'origin': trip.origin, 'destination': trip.destination
                }
            }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function placementAssuranceService(count, orderAssignFilter) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/filter/order-assign-filter/';
            }
            else {
                url = '/gobolt/trip/filter/order-assign-filter/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, orderAssignFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }

        function cwhFilterService(count, vehicleTrackingFilter) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/tracking/get/waiting/cwh/filter/';
            } else {
                url = '/gobolt/trip/tracking/get/waiting/cwh/filter/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, vehicleTrackingFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        };

        function getOwnDriverLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/driver/own-listing/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        };

        function getTripLists() {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/alert/stoppages/').success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        };

        function pilferageAlertFilterService(count, vehicleTrackingFilter) {
            if (count === 1 || count === undefined) {
                url = '/gobolt/trip/tracking/get/waiting/cwh/filter/';
            } else {
                url = '/gobolt/trip/tracking/get/waiting/cwh/filter/?page=' + count;
            }
            var deferred = $q.defer();
            $http.post(url, vehicleTrackingFilter).then(function (data) {
                deferred.resolve(data);
            }, function (error) {
                deferred.reject(error);
            });
            return deferred.promise;
        }



        function updateMultiPoints(data) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/update-multi-points/', data).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function updateLoadPoints(data) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/update-load-points/', data).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function removeLoadPoints(data) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/remove-load-points/', data).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function unscheduleFilter(actionHistorys) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/filter/unschedule-stoppage-filter/', actionHistorys).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function updateDriver(data) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/add/driver/', data).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getNewDriver(trip_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/get/new/driver/', { 'params': { 'trip_id': trip_id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getLrNumSerTrc(filterData) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/lr-search/', filterData).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }


        function getSimBasedTrackingData(trip_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/get-gps-data/', { 'params': { 'trip_id': trip_id } }).success(function (data) {
                deferred.resolve(data);
            }).error(function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
       * @param routeCode
       * @returns {Promise}
       */
        function getRoutePlacesDetails(routeCode) {
            var deferred = $q.defer();
            $http.get('/gobolt/routes/place/list/', { params: { routeCode: routeCode } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        /**
       * @param routeCode
       * @returns {Promise}
       */
        function getRouteDetails(routeCode) {
            var deferred = $q.defer();
            $http.get('/gobolt/routes/details/', { params: { routeCode: routeCode } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getVehicleTrackingDetails(params){
            var deferred = $q.defer();
            $http.get('/gobolt/trip/vehicle-tracking-details/', { params: params }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getDriverConsentForSimTracking(params){
            const url = '/gobolt/trip/get-driver-sim-tracking-consent/';
            var deferred = $q.defer();
            $http.get(url, { params: params }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function getHistoryPings(params){
            const url = '/gobolt/trip/vehicle-gps-history/';
            var deferred = $q.defer();
            $http.get(url, { params: params }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.reject(err);
            });
            return deferred.promise;
        }

        function changeDriverMobileNumber(payload) {
            var deferred = $q.defer();
            $http.post('/gobolt/trip/driver-no-change-for-sim-tracking/', payload).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function getDhanukaLockInfo(vehicle_number, order_code) {
            var deferred = $q.defer();
            $http.get('/gobolt/trip/own/dhanuka-lock/', { params: { 'vehicle_number': vehicle_number, 'order_code': order_code } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function fetchContractIds(contract_id) {
            var deferred = $q.defer();
            $http.get('/gobolt/fetch-contract-ids-v3/', {
                'params': {
                    'contract_id': contract_id,
                }
            }).then(function (data) {
                deferred.resolve(data);
            });
            return deferred.promise;
        }

        function changeContractId(payload) {
            var deferred = $q.defer();
            $http.put('/gobolt/change-contract/', payload).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }

        function fetchCustomerLocation(customer_id, originQueryStr = '', destinationQueryStr = '') {
            const queryParams = {
                'customer_id': customer_id,
                'origin': originQueryStr ?? '',
                'destination': destinationQueryStr ?? ''
            };

            var deferred = $q.defer();
            $http.get('/gobolt/get-location/cust-based-location/', { params: { ...queryParams } }).then(function (data) {
                deferred.resolve(data);
            }, function (err) {
                deferred.resolve(err);
            });
            return deferred.promise;
        }
        
    }]);