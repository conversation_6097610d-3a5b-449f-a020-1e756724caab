/**
 * Created by som on 3/9/16.
 */

var app = angular.module('myApp');

/*********************************************************************************
 * ControllerName:trip.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('trip.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$filter', '$sce',
    function ($scope, $state, $rootScope, AccountManagerServices, $filter, $sce) {

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Internet Connection Lost!")
            }
        });

        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };


        $scope.accountList = function () {
            $scope.loading = true;
            AccountManagerServices.account_trip_listing().then(function (data) {
                $scope.loading = false;
                if (data.code.status === 200) {
                    $scope.AccountTripData = data.trip_data;

                } else {
                    alert(data.code.message);
                }
            });
        };

    }
]);
/*********************************************************************************
 * ControllerName:customer.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('customer.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$filter', '$sce',
    function ($scope, $state, $rootScope, AccountManagerServices, $filter, $sce) {
        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };


        $scope.customerListing = function () {
            AccountManagerServices.customerListing().then(function (response) {
                $scope.customer_management = response.data.customer;
                if (response.data.code.status === 200) { } else {
                    alert(response.data.code.message)
                }

            }, function (error) {
                alert('Error Occurred')
            })
        };


        $scope.customerListEdit = function (customerListData) {
            $scope.customer_list = customerListData;
        };


        $scope.customerListUpdate = function (customer_list) {
            AccountManagerServices.customerUpdate(customer_list).then(function (response) {
                if (response.data.code.status == 200) {
                    swal("Good job!", response.data.code.message, "success")
                    $state.go('customer-list');
                } else {
                    swal("oops!", response.data.code.message, "error")
                }

            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.customerListing();


    }
]);
/*********************************************************************************
 * ControllerName:customer.creation
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('customer.creation', ['$scope', '$state', 'AccountManagerServices', '$filter', '$sce',
    function ($scope, $state, AccountManagerServices, $filter, $sce) {
        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };

        $scope.accBilty = function () {
            AccountManagerServices.getAccBilty().then(function (response) {
                $scope.accont_manager = response.data.account_manager_data;
                $scope.bilty = response.data.bilty_boy_data
                if (response.data.code.status === 200) { } else {
                    alert(response.data.code.message)
                }
            })
        }

        $scope.customerRegistration = function (customersReg) {
            AccountManagerServices.customerRegistrations(customersReg).then(function (response) {
                if (response.data.code.status === 200) {
                    swal("Good job!", response.data.code.message, "success")
                    $state.go('customer-list');
                } else if (response.data.code.status === 202) {
                    swal("oops!", response.data.code.message, "success")
                }
                //swal("Good job!", response.message, "success")
            }, function (error) {
                alert('Error Occurred')
            })
        }

        $scope.accBilty();

    }
]);
/*********************************************************************************
 * ControllerName:indent.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('indent.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$filter', '$sce',
    function ($scope, $state, $rootScope, AccountManagerServices, $filter, $sce) {
        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };
        $scope.isReadOnlyEditRefId = function (tripStatus) {
            var allowEdit = "";
            if (tripStatus in ["New", "Planned", "Vehicle Placed", "Placement Initiated"]) {
                allowEdit = "true";
            }
            return allowEdit;
        }

        $scope.indentRefIdUpdate = function indentRefIdUpdate(indentOrders) {

            $scope.loading = true;
            $scope.data = {
                "indent_code": indentOrders[0].indent.indent_code
            }
            $scope.orders = [];
            angular.forEach(indentOrders, function (value, key) {
                $scope.orders.push({
                    "order_id": value.order_id,
                    "demand_reference_id": value.demand_reference_id
                });
            });
            $scope.data["orders"] = $scope.orders;
            AccountManagerServices.indentRefIdUpdate($scope.data)
                .then(function (response) {
                    $scope.loading = false;
                    if (response.data.code === 200) {
                        swal("Good job!", response.data.message, "success");
                    } else {
                        swal("Error", response.data.message, "error");
                    }
                });
        }

        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.indent_list, query).length;
            });
        };
        $scope.setCancelCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.cancelled_indent_list, query).length;
            });
        };

        /**
         * functionName:indentList
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        $scope.indentListPage = {};
        $scope.pagination = {};
        $scope.indentList = function (count) {
            $scope.loading = true;
            AccountManagerServices.getIndentList(count).then(function (response) {
                $scope.loading = false;
                if (response.data.results.code.status === 200) {
                    $scope.indent_list = response.data.results.indent;
                    $scope.indentListPage.count = response.data.count;
                    $scope.wf = response.data.results.wf;
                } else {
                    alert(response.data.results.code.message)
                }
            })
        };

        $scope.repetitiveIndentList = function (count, repetitiveFilterData) {
            $scope.loading = true;
            $scope.filterDataObject = {
                customer: (repetitiveFilterData && repetitiveFilterData.customer) ? repetitiveFilterData.customer.id : '',
                vehicle_type: (repetitiveFilterData && repetitiveFilterData.vehicle_type) ? repetitiveFilterData.vehicle_type.id : '',
                origin: (repetitiveFilterData && repetitiveFilterData.origin) ? repetitiveFilterData.origin.location_name : '',
                destination: (repetitiveFilterData && repetitiveFilterData.destination) ? repetitiveFilterData.destination.location_name : '',
                from_date: (repetitiveFilterData && repetitiveFilterData.date_vehicle_required) ? repetitiveFilterData.date_vehicle_required : '',
                to_date: (repetitiveFilterData && repetitiveFilterData.to_date_vehicle_required) ? repetitiveFilterData.to_date_vehicle_required : ''
            };
            AccountManagerServices.getRepetitiveIndentList(count, $scope.filterDataObject).then(function (response) {
                $scope.loading = false;
                if (response.data.results.code.status === 200) {
                    $scope.indent_list = response.data.results.indent;
                    $scope.indentListPage.count = response.data.count;
                    $scope.wf = response.data.results.wf;
                } else {
                    alert(response.data.results.code.message)
                }
            })
        };

        $scope.clearFilterRepetitiveIndent = function () {
            $scope.filterData = {};
            $scope.pagination.current = 1;
            $scope.repetitiveIndentList();
        };

        $scope.deleteRepetitiveIndent = function (id, indentLists) {
            $scope.repeat_indent_cancel_idx = $scope.indent_list.indexOf(indentLists)
            console.log('$scope.repeat_indent_cancel_idx', $scope.repeat_indent_cancel_idx)
            var indent_details = {
                id: id
            };
            swal({
                    title: "Are you sure?",
                    text: "You want to disable repetitive indent!",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Yes, disable it!",
                    cancelButtonText: "No, cancel !",
                    closeOnConfirm: false,
                    closeOnCancel: false
                },
                function (isConfirm) {
                    if (isConfirm) {
                        AccountManagerServices.deleteRepetitiveIndent(indent_details).then(function (response) {
                            if (response.data.code.status === 200) {
                                swal("Success",response.data.code.message, "success");
                                $scope.indent_list.splice($scope.repeat_indent_cancel_idx, 1)                            
                            } else {
                                swal(response.data.code.message, "error");
                            }
                        })
                    } 
                    else {
                        swal.close();
                    }
                }
            );
        };

        $scope.dataBindD = function (data_0, data_1, data_2, data_3, data_4) {
            var add = ' ---> ';

            angular.forEach(data_2, function (value, key) {
                // console.log()
                add += value.destination_point.location_name + ' ( ' + value.destination_point.location_code + ' ) ' + ' ---> '

            });
            return data_0 + ' ( ' + data_1 + ' ) ' + add + data_3 + ' ( ' + data_4 + ' ) '

        }

        $scope.dataBindDWaypoints = function (data) {
            var add = '';

            angular.forEach(data, function (value, key) {
                add += value.destination_point.location_name + ' ( ' + value.destination_point.location_code + ' ) ' + ' ---> '
            });
            return add
        }
        /**
         * functionName:clearFilterIndent
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        $scope.clearFilterIndent = function () {
            $scope.filterData = {};
            $scope.pagination.current = 1;
            $scope.indentList();
        };
        /**
         * functionName:cancelledIndents
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        $scope.cancelledIndents = function (count) {
            $scope.loading = true;
            AccountManagerServices.getCancelledIndentList(count).then(function (response) {
                $scope.loading = false;
                if (response.data.results.code.status === 200) {
                    $scope.cancelled_indent_list = response.data.results.indent;
                    $scope.indentListPage.count = response.data.count;
                    $scope.wf = response.data.results.wf;
                } else {
                    alert(response.data.results.code.message)
                }
            })
        };

        /**
         * functionName:cancelledIndents
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */

        $scope.clearFilterIndentCancelled = function () {
            $scope.filterData = {};
            $scope.pagination.current = 1;
            $scope.searchText = '';
            $scope.cancelledIndents();
        };

        /**
         * functionName:adminIndentFilter
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        $scope.filterData = {
            customer: '',
            vehicle_type: '',
            origin: '',
            destination: '',
            date_vehicle_required: '',
            to_date_vehicle_required: '',
            manifest_number: ''
        };
        $scope.adminIndentFilter = function (count, filterData, type) {
            $scope.loading = true;
            $scope.filterDataObject = {
                customer: filterData.customer ? filterData.customer.id : '',
                vehicle_type: filterData.vehicle_type ? filterData.vehicle_type.id : '',
                origin: filterData.origin ? filterData.origin.location_name : '',
                destination: filterData.destination ? filterData.destination.location_name : '',
                from_date: filterData.date_vehicle_required ? filterData.date_vehicle_required : '',
                to_date: filterData.to_date_vehicle_required ? filterData.to_date_vehicle_required : '',
                order_status: filterData.order_status ? filterData.order_status.name : '',
                indent_type: type,
                manifest_number: filterData.manifest_number ? filterData.manifest_number : ''
            };

            AccountManagerServices.postAdminIndentFilter(count, $scope.filterDataObject).then(function (response) {
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (type === 'indent') {
                    if (response.data.results.code.status === 200) {
                        $scope.indent_list = response.data.results.indent_data;
                        $scope.indentListPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal(response.data.results.code.message)
                    }
                } else if (type === 'cancelled_indent') {
                    if (response.data.results.code.status === 200) {
                        $scope.cancelled_indent_list = response.data.results.indent;
                        $scope.indentListPage.count = response.data.count;
                        $scope.wf = response.data.results.wf;
                    } else {
                        swal(response.data.results.code.message)
                    }
                }

            })
        };


        //Ashish
        $scope.getOrder = function () {
            AccountManagerServices.getOrderData().then(function (response) {
                $scope.vehicleType = response.data.vehicle_types;

            }, function (error) {
                alert('Error Occurred')
            })
        };


        $scope.indentListEdit = function (indentListData) {
            indentListData.is_favorite = false;
            $scope.indentEdit = indentListData;
            $scope.indent = {};
            $scope.indent.index = $scope.indent_list.indexOf(indentListData);
            $scope.indent.vehicle_type = indentListData.vehicle_type;
            $scope.indent.start_point = indentListData.start_point;
            $scope.indent.destination_point = indentListData.destination_point;
            $scope.indent.date_vehicle_required = indentListData.date_vehicle_required;
            $scope.indent.time_vehicle_required = indentListData.time_vehicle_required;
            $scope.indent.number_of_vehicle = indentListData.number_of_vehicle;
            $scope.indent.contract = indentListData.contract;
            $scope.indent.customer = indentListData.customer;
            $scope.indent.date_indent = indentListData.date_indent;
            $scope.indent.id = indentListData.id;
            $scope.indent.indent_code = indentListData.indent_code;
            $scope.indent.is_favorite = false;
            $scope.indent.time_indent = indentListData.time_indent;
            $scope.getOrder();
            $scope.getLocation(indentListData.customer);
            // $scope.$watch('indent_edit.date_vehicle_required', function (newValue) {
            //     $scope.date_vehicle_required = $filter('date')(newValue, 'dd-MM-yyyy');
            // });
        };

        ////Ashish
        //$scope.getLocation = function (customerName) {
        //    AccountManagerServices.getLocationLists(customerName).then(function (response) {
        //
        //        if (response.data.code.status === 200) {
        //            $scope.locations = response.data.location;
        //        } else {
        //            alert(response.data.code.message)
        //        }
        //    }, function (error) {
        //        alert('Error Occurred')
        //    })
        //};


        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            AccountManagerServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    $scope.locations = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};


        $scope.indentListUpdate = function (indentEditData, indentedit) {
            $scope.indentedit = indentedit;
            if (!$scope.indentedit.$invalid) {
                AccountManagerServices.indentUpdate(indentEditData).then(function (response) {
                    //Ashish
                    if (response.data.code.status == 200) {
                        $scope.indentEdit.vehicle_type.name = $scope.indent.vehicle_type.name;
                        $scope.indentEdit.start_point.location_name = $scope.indent.start_point.location_name;
                        $scope.indentEdit.start_point.location_code = $scope.indent.start_point.location_code;
                        $scope.indentEdit.destination_point.location_name = $scope.indent.destination_point.location_name;
                        $scope.indentEdit.destination_point.location_code = $scope.indent.destination_point.location_code;
                        $scope.indentEdit.indent_code = response.data.indentCode;
                        $scope.indentEdit.date_vehicle_required = $scope.indent.date_vehicle_required;
                        $scope.indentEdit.time_vehicle_required = $scope.indent.time_vehicle_required;
                        $scope.indentEdit.number_of_vehicle = $scope.indent.number_of_vehicle;
                        $scope.indent_list[$scope.indent.index].id = response.data.indentId;
                        swal("Indent Updated", response.data.code.message, "success");
                    } else if (response.data.code.status == 204) {
                        swal("Oops!", response.data.code.message, "error");
                    } else if (response.data.code.status == 404) {
                        swal("Oops", response.data.code.message, "error");
                    } else {
                        swal("Can't edit!", response.data.code.message, "error");
                    }
                }, function (error) {
                    alert('Error Occurred')
                });
                $('#indentEditFrom').modal('hide')
            } else {
                swal("Please fill all required fields.", "", "error");
                $scope.submitted = true
            }
        };


        //Ashish
        $scope.removeIndentList = function (id, indentLists) {
            var order_details = {
                id: id
            };
            $scope.indent_cancle_idx = $scope.indent_list.indexOf(indentLists)
            swal({
                title: "Are you sure?",
                text: "You want to delete data !",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: false,
                closeOnCancel: false
            },
                function (isConfirm) {
                    if (isConfirm) {
                        //Ashish
                        swal({
                            title: "Choose!",
                            text: "Cancellation Type",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "Unfulfilled Indent",
                            cancelButtonText: "Wrong Indent",
                            closeOnConfirm: false,
                            closeOnCancel: false
                        },
                            function (isConfirm) {
                                if (isConfirm) {
                                    $scope.cancellation_type = 'Unfulfilled Indent'
                                } else {
                                    $scope.cancellation_type = 'Wrong Indent'
                                }
                                order_details.cancellation_type = $scope.cancellation_type;
                                AccountManagerServices.removeIndent(order_details).then(function (response) {
                                    if (response.data.status === 'Done') {
                                        swal("Indent Cancelled!", response.data.code.message, "success");
                                        $scope.indent_list.splice($scope.indent_cancle_idx, 1)
                                    } else if (response.data.status === 'Transit') {
                                        swal("Indent-order In Transit!", response.data.code.message, "error");
                                    } else {
                                        swal("Can't cancel!", 'Indent stage is beyond cancellation', "error");
                                    }
                                }, function (err) {
                                    alert('Error Occurred')
                                })
                            });
                    } else {
                        swal("Not cancelled!", "Your Indent is safe :)", "error");
                    }
                });
            //$scope.indent_list.splice(index, 1)
        };

        $scope.exportDataIndentAccount = function (filterData) {
            $scope.csvloading = true;
            console.log('filterdata_here', filterData);

            $scope.customer_id = '';
            $scope.vehicle_type_id = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.manifest_number = '';

            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;
            if (filterData.vehicle_type)
                $scope.vehicle_type_id = filterData.vehicle_type.id;
            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;
            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;
            if (filterData.order_status)
                $scope.order_status = filterData.order_status.name;
            if (filterData.date_vehicle_required)
                $scope.date_vehicle_required = filterData.date_vehicle_required;
            if (filterData.to_date_vehicle_required)
                $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;
            if (filterData.manifest_number)
                $scope.manifest_number = filterData.manifest_number


            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_type: $scope.vehicle_type_id,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                order_status: $scope.order_status,
                indent_type: 'indent',
                manifest_number: $scope.manifest_number
            };

            AccountManagerServices.postIndentExportData($scope.filterDataObject).then(function (response) {
                $scope.csvloading = false;
                if (response.data.code.status === 200) {
                    $scope.indentExport = response.data.indent_data;
                    $scope.downloadIndentCsv = [];
                    angular.forEach($scope.indentExport, function (value, key) {
                        $scope.indentExportData = {
                            "customer": value.customer.company_name + '<' + value.customer.customer_code + '>',
                            "vehicle_type": value.vehicle_type.name,
                            "start_point": value.start_point.location_name.replace('<', '').replace('>', ''),
                            "destination_point": value.destination_point.location_name.replace('<', '').replace('>', ''),
                            "indent_code": value.indent_code,
                            "date": value.date_vehicle_required,
                            "time": value.time_vehicle_required,
                            "number_of_vehicle": value.number_of_vehicle,
                            "manifest_number": value.manifest_number
                        };
                        $scope.downloadIndentCsv.push($scope.indentExportData);
                    });
                    var mystyle = {
                        headers: true,
                        column: {
                            style: {
                                Font: {
                                    Bold: "1"
                                }
                            }
                        }
                    };
                    alasql('SELECT * INTO XLS("Indent List.xls",?) FROM ?', [mystyle, $scope.downloadIndentCsv = $filter('orderBy')($scope.downloadIndentCsv, '-date')]);
                }
            })
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            console.log('column', column);
            console.log('$scope.sortColumn', $scope.sortColumn);
            $scope.reverseSort = ($scope.sortColumn === column ? !$scope.reverseSort : false)
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn === column) {
                //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
            //return '';
        };


        /**
         * getOrderDetails
         */
        $scope.getOrderDetailsAccount = function (data) {
            AccountManagerServices.getIndentOrdersAccount(data).then(function (response) {
                console.log('response', response)
                if (response.data.code.status == 200) {
                    $scope.indentOrders = response.data.data;
                } else {
                    swal("Oops!", response.data.code.message, "error");
                }

            })
        };

        //$scope.exportDataIndentAccount = function (indentList) {
        //    $scope.downloadIndentCsv = [];
        //    angular.forEach(indentList, function (value, key) {
        //
        //        $scope.indentExportData = {
        //            "customer": value.customer.company_name + '<' + value.customer.customer_code + '>',
        //            "vehicle_type": value.vehicle_type.name,
        //            "start_point": value.start_point.location_name,
        //            "destination_point": value.destination_point.location_name,
        //            "indent_code": value.indent_code,
        //            "date": value.date_vehicle_required,
        //            "time": value.time_vehicle_required,
        //            "number_of_vehicle": value.number_of_vehicle
        //
        //        };
        //        $scope.downloadIndentCsv.push($scope.indentExportData)
        //        //console.log('value', $scope.expenseExportData)
        //    });
        //    alasql('SELECT * INTO CSV("indent.csv",{headers:true, separator:","}) FROM ?', [$scope.downloadIndentCsv = $filter('orderBy')($scope.downloadIndentCsv, 'date')]);
        //
        //};

        /**
         * Indent filter
         */





        $scope.getOrder = function () {
            AccountManagerServices.getOrderData().then(function (response) {
                //Ashish
                //$scope.locations = response.data.locations;
                $scope.vehicleType = response.data.vehicle_types;

            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.getCustomerData = function () {
            AccountManagerServices.getAllCustomerLists().then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.customerName = response.data.customer;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.getLocationData = function () {
            AccountManagerServices.getAllLocationLists().then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.locationNmae = response.data.data_list;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.getVehicleTypeData = function () {
            AccountManagerServices.getVehicleType().then(function (data) {
                $scope.vehicle_type = data.data.vehicle_type;
            })
        };
    }
]);
/*********************************************************************************
 * ControllerName:indent.creation
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('indent.creation', ['$scope', '$state', 'AccountManagerServices', '$sce', 'MasterService', function ($scope, $state, AccountManagerServices, $sce, MasterService) {
    $scope.$watch('online', function (newStatus) {
        if (newStatus === false) {
            swal("Internet Connection Lost!")
        }
    });
    $scope.data = {
        is_favorite: false,
        date_vehicle_required: ""
    };

    $scope.isDisabledCheckBox = false;
    $scope.$watch('data.date_vehicle_required', function() {
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];

        if($scope.data.date_vehicle_required!=="" && $scope.data.date_vehicle_required<formattedToday){
            $scope.isDisabledCheckBox = true;
        }else{
            $scope.isDisabledCheckBox = false;
        }
    });

    $scope.indentType = MasterService.indentType;
    $scope.trustSrc = function (src) {
        return $sce.trustAsResourceUrl(src);
    };

    $scope.getIndentData = function indentPlacement() {
        AccountManagerServices.getAllLists().then(function (response) {
            $scope.customerName = response.data.customer;
            $scope.locations = response.data.location;
            $scope.vehicleType = response.data.vehicle_type;
            if (response.data.code.status === 200) { } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            alert('Error Occurred')
        })
    };

    $scope.disable = false;
    $scope.data = {
        'date_vehicle_required': '',
        'customer': '',
        'vehicle_type': '',
        'start_point': '',
        'destination_point': '',
        'time_vehicle_required': '',
        'placement_type': 'Scheduled',
        'no_of_vehicle': '',
        'is_favorite': false,
        'is_Ad_hoc': false,
        'separate_indent':false,
        'ad_hoc_contract': 0
    };

    $scope.customerIndent = function (indentData, data_1) {
        $scope.hit = true;
        $scope.lastData = { ...indentData };
        $scope.lastData.contract_id = indentData.contract_id.contract_id;
        $scope.lastData.destination_point = data_1;
        $scope.disable = true;

        var no_of_vehicle = indentData.no_of_vehicle;
        var demand_reference_ids = indentData.demand_reference_id;

        if (demand_reference_ids !== undefined) {
            demand_reference_ids = demand_reference_ids.split(",");
            demand_reference_ids.map(Function.prototype.call, String.prototype.trim)
            if (demand_reference_ids.length != no_of_vehicle) {
                // swal("Vehicle Reference ID Should be equal to number of vehicle required", "", "error");
                // $scope.submitted = true;
                // $scope.disable = false;
            }
            $scope.lastData.demand_reference_id = demand_reference_ids;
        }

        if (!$scope.indentForm.$invalid) {
            AccountManagerServices.postCustomerIndent($scope.lastData).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.hit = false;


                    swal({
                        title: "Good job!",
                        text: "Your indent is created.",
                        type: "success",
                        confirmButtonText: "Ok",
                        closeOnConfirm: true
                    },
                        function (isConfirm) {
                            if (isConfirm) {
                                location.reload()
                            }
                        })




                } else {
                    $scope.hit = false;
                    swal("oops!", response.data.code.message, "error")
                }
            }, function (error) {
                alert('Error Occurred')
            })
        } else {
            swal("Please fill all required fields.", "", "error");
            $scope.submitted = true;
            $scope.disable = false;
            $scope.hit = false;
        }
    };

    $scope.desMulti = [{
        'destination_point': ''
    }];
    var wrapper = $(".input_fields_wrap");
    $scope.addMoreDestination = function (data_1) {

        console.log("data_1", data_1);
        console.log("data_2", $scope.desMulti);

        if ($scope.desMulti.length >= 1) {
            $scope.active1 = true
        }
        var newItemNo = $scope.desMulti.length + 1;
        $scope.desMulti.push({
            'destination_point': ''
        });
    };


    $scope.removeAdditionalDestination = function (index) {
        $scope.idxx = 'remove_' + index;
        $scope.del_accc = 'remove_' + 1;
        if ($scope.idxx === $scope.del_accc) {
            $scope.active1 = false
        }
        //$scope.active = false
        var lastItem = $scope.desMulti.length - 1;
        $scope.desMulti.splice(lastItem);
    };
    $scope.stopageInfo = {
        'destination_point': ''
    };
    $scope.des_loc_mul = function (data, $select) {

        angular.forEach($scope.desMulti, function (value, key) {
            // console.log(value);
            if (data && data.id === value.destination_point.id) {
                swal("oops!", 'Destination points already selected for this location!', "warning");
                $select.selected = undefined;
                // //reset search query
                // $select.search = undefined;
                // //focus and open dropdown
                $select.activate();
            }
        });

        $scope.des_locations_mul = [];
        angular.forEach($scope.locations, function (value, key) {
            // console.log(value);
            if (data && data.id === value.id) {
                // $select.selected = undefined;
                // //reset search query
                // $select.search = undefined;
                // //focus and open dropdown
                // $select.activate();
            } else {
                $scope.des_locations_mul.push(value)
            }

        });
    };

    $scope.checkSourceDestination = function (customer) {
        if (!customer) {
            swal("Please select customer first!", "", "error");
            return false
        }
    };

    $scope.fetch_ids = function (cust, orig, dest) {
        console.log('The_data', cust, orig, dest);
        if (!cust || !orig || !dest) {

        } else {
            AccountManagerServices.fetch_contract_ids(cust, orig, dest).then(function (response) {
                console.log('THE_IDS_DATA', response);
                if (response.data.code.status === 200) {
                    $scope.contract_ids = response.data.c_ids;
                    $scope.data.contract = {};
                    //$scope.isDisabled = response.data.disable;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                alert('Error Occurred')
            })
        }
    };

    $scope.isDisabled = false;
    $scope.waypoint_len = 0;
    $scope.getFormDetails = function (selectedContract) {
        const contract_id = (selectedContract && selectedContract.contract_id) ? selectedContract.contract_id : '';

        if (contract_id == 'No Contract Found' || !contract_id) {
            //$scope.data = {
            //'date_vehicle_required': '',
            //'vehicle_type': ''
            //'start_point': '',
            //'customer': customer,
            //'placement_type': 'Scheduled',
            //'destination_point': ''
            //};
            //$scope.isDisabled = false;

            $scope.data.tat = '';
            return 0
        } else {
            AccountManagerServices.formDetails(contract_id).then(function (response) {
                console.log('THE_DATA', response);
                if (response.data.code.status === 200) {
                    console.log('desMulti', $scope.desMulti);
                    $scope.data.vehicle_type = response.data.veh_type;
                    $scope.data.tat = response.data.tat;
                    $scope.desMulti.splice(0, $scope.waypoint_len);
                    angular.forEach(response.data.waypoints, function (value, key) {
                        console.log('key', key);
                        $scope.desMulti.splice(key, 0, {
                            'destination_point': value.s_location,
                            'is_active': value.is_active
                        });
                    });
                    $scope.waypoint_len = $scope.desMulti.length - 1;
                    console.log('$scope.waypoint_len', $scope.waypoint_len);
                    $scope.isDisabled = response.data.disable;
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                alert('Error Occurred')
            })
        }
    };

    $scope.multiCheckAgain = function (data, $select) {
        // console.log('multiCheckAgain', data);
        var count = 0;
        angular.forEach($scope.desMulti, function (value, key) {
            // console.log(value);
            if (data && data.id === value.destination_point.id) {
                console.log('multiCheckAgain', data);
                // $select.selected = undefined;
                // //reset search query
                // $select.search = undefined;
                // //focus and open dropdown
                // $select.activate();
                if (count >= 1) {
                    $select.selected = undefined;
                    $select.activate();
                    swal("oops!", 'Points already selected for this location!', "warning");
                }
                count += 1
            } else {
                $scope.des_locations_mul.push(value)
            }
        });
    };

    $scope.favoriteIndents = function (customer) {
        if (!customer) {
            swal("Please select customer first!", "", "error");
            return false
        } else {
            AccountManagerServices.getFavoriteIndents(customer.id).then(function (response) {
                $scope.favoriteIndentList = response.data.favorite_indents
                if (response.data.code.status === 200) {
                    //  $state.go('indent-list')
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                alert('Error Occurred')
            })
        }
    };
    /**
     *
     * @param data_0
     * @param data_1
     * @param data_2
     * @param data_3
     * @param data_4
     * @returns {string}
     */

    $scope.dataBindD = function (data_0, data_1, data_2, data_3, data_4) {

        var add = ' ---> ';

        angular.forEach(data_2, function (value, key) {
            // console.log()
            add += value.destination_point.location_name + ' ( ' + value.destination_point.location_code + ' ) ' + ' ---> '

        });
        return data_0 + ' ( ' + data_1 + ' ) ' + add + data_3 + ' ( ' + data_4 + ' ) '

    };

    $scope.selectIndent = function (indent) {

        $scope.data.start_point = indent.start_point;
        $scope.desMulti = [];
        if (indent.multi_stopage) {
            angular.forEach(indent.multi_stopage, function (value, key) {
                // console.log()
                $scope.desMulti.push(value)

            });
        }

        $scope.desMulti.push({
            'destination_point': indent.destination_point
        });
        console.log("Vishnu", $scope.desMulti);
        // $scope.data.destination_point = indent.destination_point
        $scope.data.vehicle_type = indent.vehicle_type
    }


    $scope.getLocation = function (customerName) {
        AccountManagerServices.getLocationLists_v2(customerName).then(function (response) {
            $scope.locations = response.data.location;
            if (response.data.code.status === 200) { } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            alert('Error Occurred')
        })
    };

    //$(".timepicker").timepicker({
    //    showInputs: false
    //});
    $scope.getIndentData();
}])
/*********************************************************************************
 * ControllerName:order.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('order.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$location', '$filter', '$sce', '$interval', function ($scope, $state, $rootScope, AccountManagerServices, $location, $filter, $sce, $interval) {

    $scope.$watch('online', function (newStatus) {
        if (newStatus === false) {
            swal("Internet Connection Lost!")
        }
    });
    $scope.trustSrc = function (src) {
        return $sce.trustAsResourceUrl(src);
    };

    //Badal
    $scope.getVehicleList = function () {
        AccountManagerServices.getAllVehicleLists().then(function (response) {
            if (response.data.code.status === 200) {
                $scope.vehicle_number = response.data.data_list;
            } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            alert('Error Occurred')
        })
    }

    //Ashish
    $scope.getCustomerData = function () {
        AccountManagerServices.getAllCustomerLists().then(function (response) {
            if (response.data.code.status === 200) {
                $scope.customerName = response.data.customer;
            } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            alert('Error Occurred')
        })
    };

    //Ashish
    $scope.getLocationData = function () {
        AccountManagerServices.getAllLocationLists().then(function (response) {
            console.log('location', response);
            if (response.data.code.status === 200) {
                $scope.locationNmae = response.data.data_list;
            } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            alert('Error Occurred')
        })
    };


    /**
     * functionName:getLocation
     * inputType:
     * outputType:
     * ownerName: Sushil
     * developedDate: 07/02/2018
     * testerName:
     * testDate:
     */
    $scope.getLocation = function (customerName) {
        //$scope.billModel.origin_location = '';
        //$scope.billModel.destination_location = '';
        AccountManagerServices.getLocationLists(customerName).then(function (response) {
            if (response.data.code.status === 200) {
                //$scope.locations = response.data.location;
                $scope.locationNmae = response.data.location;
                $scope.locations = response.data.location;
                //angular.forEach($scope.locationNmae, function (value, key) {
                //    $scope.locationNmae[key].location_id = value.id;
                //});
                //console.log('new',$scope.locationNmae)
            } else {
                alert(response.data.code.message)
            }
        }, function (error) {
            swal("Oops", 'No internet connection.', error)
        })
    };


    //$scope.distLocation = function (location) {
    //    angular.forEach($scope.locationNmae, function (value, key) {
    //        if (value.location_name == location.location_name)
    //            $scope.locationNmae.splice(key, 1);
    //    });
    //};


    //Ashish
    $scope.getVehicleTypeData = function () {
        AccountManagerServices.getVehicleType().then(function (data) {
            $scope.vehicle_type = data.data.vehicle_type;
        })
    };

    //Ashish
    $scope.orderStatus = function () {
        $scope.orderStatusFilterList = [{
            id: '1',
            name: 'New'
        }, {
            id: '2',
            name: 'Order Planned'
        }, {
            id: '3',
            name: 'Vehicle Placed'
        }, {
            id: '4',
            name: 'Trip Started'
        }, {
            id: '5',
            name: 'Vehicle Reported'
        }, {
            id: '6',
            name: 'Trip Completed'
        }];
    };

    //Ashish
    $scope.pagination = {};
    $scope.filterData = {
        customer: '',
        vehicle_type: '',
        vehicle_number: '',
        order_ids: '',
        trip_codes: '',
        origin: '',
        destination: '',
        order_status: '',
        date_vehicle_required: '',
        to_date_vehicle_required: ''
    };

    /**
     * functionName:accountOrderListFilter
     * inputType:
     * outputType:
     * ownerName: Sushil
     * developedDate: 31/07/2017
     * testerName:
     * testDate:
     */
    $scope.accountOrderListFilter = function (count, filterData) {
        //TODO
        $scope.loading = true;
        $scope.customer_id = '';
        $scope.vehicle_type_id = '';
        $scope.vehicle = '';
        $scope.origin_name = '';
        $scope.destination_name = '';
        $scope.order_status = '';
        $scope.date_vehicle_required = '';
        $scope.to_date_vehicle_required = '';
        $scope.trip_order_code = '';
        console.log('filterData', filterData);


        if (filterData.customer)
            $scope.customer_id = filterData.customer.id;
        if (filterData.vehicle_type)
            $scope.vehicle_type_id = filterData.vehicle_type.id;
        if (filterData.vehicle_number)
            $scope.vehicle = filterData.vehicle_number.vehicle_registration_number;
        if (filterData.trip_order_code)
            $scope.trip_order_code = filterData.trip_order_code;
        if (filterData.origin)
            $scope.origin_name = filterData.origin.location_name;
        if (filterData.destination)
            $scope.destination_name = filterData.destination.location_name;
        if (filterData.order_status)
            $scope.order_status = filterData.order_status.name;
        if (filterData.date_vehicle_required)
            $scope.date_vehicle_required = filterData.date_vehicle_required;
        if (filterData.to_date_vehicle_required)
            $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

        $scope.filterDataObject = {
            customer: $scope.customer_id,
            vehicle_type: $scope.vehicle_type_id,
            vehicle_number: $scope.vehicle,
            origin: $scope.origin_name,
            destination: $scope.destination_name,
            from_date: $scope.date_vehicle_required,
            to_date: $scope.to_date_vehicle_required,
            order_status: $scope.order_status,
            trip_order_code: $scope.trip_order_code
        };
        console.log('$scope.filterDataObject', $scope.filterDataObject);
        $scope.orderFilterPage = {
            'next': '',
            'previous': '',
            'count': ''
        };
        // $scope.orderListingFilter = function () {
        AccountManagerServices.postAccountOrderListFilter(count, $scope.filterDataObject).then(function (response) {
            console.log('response', response);
            $scope.loading = false;
            if (count === undefined) {
                $scope.pagination.current = 1;
            }
            if (response.data.results.code.status === 200) {
                $scope.orderListPage.count = response.data.count;
                $scope.orderList = response.data.results.order_data;
                $scope.wf = response.data.results.wf;


                // if ($scope.orderFilterPage.next) {
                //     $scope.orderList = $scope.orderList.concat(response.data.results.order_data)
                // }
                // else {
                //     $scope.orderList = angular.extend(response.data.results.order_data)
                // }
                // $scope.orderFilterPage.next = response.data.next;
                // $scope.orderFilterPage.previous = response.data.previous;
                // $scope.orderFilterPage.count = response.data.count;
                // if ($scope.orderFilterPage.next != null) {
                //     $scope.orderListingFilter()
                // }
            } else {
                swal(response.data.results.code.message)
            }

        }, function (error) {
            alert('Error Occurred')
        })
    };
    //     $scope.orderListingFilter();
    // };

    //Ashish
    $scope.clearFilter = function () {
        $scope.pagination.current = 1;
        $scope.searchText = '';
        $scope.filterData = {
            customer: '',
            vehicle_type: '',
            vehicle_number: '',
            order_ids: '',
            trip_codes: '',
            origin: '',
            destination: '',
            order_status: '',
            date_vehicle_required: '',
            to_date_vehicle_required: ''
        };
        $scope.orderManagement();
    };


    /**
     * functionName:orderManagement
     * inputType:
     * outputType:
     * ownerName: Sushil
     * developedDate: 31/07/2017
     * testerName:
     * testDate:
     */
    $scope.orderListPage_v_2 = {
        'next': '',
        'previous': '',
        'count': ''
    };
    $scope.orderManagement_v_2 = function (count) {
        $scope.loading = true;
        AccountManagerServices.getAllOrders(count).then(function (response) {
            $scope.loading = false;
            // console.log(response);
            if (response.data.results.code.status === 200) {
                $scope.orderList_v_2 = response.data.results.order_data;
                $scope.orderListPage_v_2.count = response.data.count;
                $scope.wf_v_2 = response.data.results.wf;
                // try {
                //     if ($scope.orderList.length) {
                //         $scope.orderList = $scope.orderList.concat(response.data.results.order_data)
                //     }
                //     else {
                //         $scope.searchText = '';
                //         $scope.orderList = response.data.results.order_data;
                //     }
                // }
                // catch (err) {
                //     $scope.orderList = response.data.results.order_data;
                // }
                //
                // $scope.orderListPage.next = response.data.next;
                // $scope.orderListPage.previous = response.data.previous;
                // $scope.orderListPage.count = response.data.count;
                // if ($scope.orderListPage.next != null) {
                //     $scope.orderManagement()
                // }
            }
        }, function (err) {
            alert("Error Occurred!")
        });


        $scope.showDLPicture = function (dl_pic) {
            alert("vishnu")
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url
                });
            } else {
                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
            }

        };
        $scope.showRCPicture = function (rc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url
                });
            } else {
                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
            }

        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url
                });
            } else {
                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
            }

        };
        $scope.showLRPicture = function (lr_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url
                });
            } else {
                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };
        $scope.showDriverWithVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url
                });
            } else {


                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
            }
        };
        $scope.showPermitPictureAcc = function (permit_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPermitPic = response.data.url
                });
            } else {

                $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
            }
        };

    };


    /**
     *
     * @type {{}}
     */
    $scope.orderListPage = {
        'next': '',
        'previous': '',
        'count': ''
    };
    $scope.orderManagement = function (count) {
        $scope.loading = true;
        AccountManagerServices.getAllOrders(count).then(function (response) {
            $scope.loading = false;
            console.log(response);
            if (response.data.results.code.status === 200) {
                $scope.orderList = response.data.results.order_data;
                $scope.orderListPage.count = response.data.count;
                $scope.wf = response.data.results.wf;
                // try {
                //     if ($scope.orderList.length) {
                //         $scope.orderList = $scope.orderList.concat(response.data.results.order_data)
                //     }
                //     else {
                //         $scope.searchText = '';
                //         $scope.orderList = response.data.results.order_data;
                //     }
                // }
                // catch (err) {
                //     $scope.orderList = response.data.results.order_data;
                // }
                //
                // $scope.orderListPage.next = response.data.next;
                // $scope.orderListPage.previous = response.data.previous;
                // $scope.orderListPage.count = response.data.count;
                // if ($scope.orderListPage.next != null) {
                //     $scope.orderManagement()
                // }
            }
        }, function (err) {
            alert("Error Occurred!")
        });


        $scope.showDLPicture = function (dl_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url
                });
            } else {
                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
            }

        };
        $scope.showRCPicture = function (rc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url
                });
            } else {
                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
            }

        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url
                });
            } else {
                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
            }

        };
        $scope.showLRPicture = function (lr_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url
                });
            } else {
                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };
        $scope.showDriverWithVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url
                });
            } else {


                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
            }
        };
        $scope.showPermitPictureAcc = function (permit_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPermitPic = response.data.url
                });
            } else {

                $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
            }
        };

    };
    //Ashish
    //$scope.orderManagement();
    $scope.cancelledOrdersListPage = {};
    $scope.cancelledOrders = function (count) {
        //$scope.searchText = '';
        $scope.loading = true;
        AccountManagerServices.cancelled_orders(count).then(function (response) {
            $scope.loading = false;
            if (response.data.results.code.status === 200) {
                $scope.cancelledOrdersListPage.count = response.data.count;
                $scope.cancelledOrderList = response.data.results.order_data;
                $scope.wf = response.data.results.wf;
            }
        }, function (err) {
            alert("Error Occurred!")
        })
    };

    /**
     *
     * @param item
     */
    $scope.cancelledOrdersListPage_v_2 = {};
    $scope.cancelledOrders_v_2 = function (count) {
        //$scope.searchText = '';
        $scope.loading = true;
        AccountManagerServices.cancelled_orders(count).then(function (response) {
            $scope.loading = false;
            console.log(response);
            if (response.data.results.code.status === 200) {
                $scope.cancelledOrdersListPage_v_2.count = response.data.count;
                $scope.cancelledOrderList_v_2 = response.data.results.order_data;
                $scope.wf_v_2 = response.data.results.wf;
            }
        }, function (err) {
            alert("Error Occurred!")
        })
    };
    //Ashish
    $scope.cancelOrder = function (item) {
        //var id = item.id;
        var order_details = {
            id: item.id
        };
        var index = $scope.orderList.indexOf(item);
        swal({
            title: "Are you sure?",
            text: "You want to delete data !",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel !",
            closeOnConfirm: false,
            closeOnCancel: false,
        },
            function (isConfirm) {
                if (isConfirm) {
                    //Ashish
                    swal({
                        title: "Choose!",
                        text: "Cancellation Type",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: "Unfulfilled Order",
                        cancelButtonText: "Wrong order",
                        closeOnConfirm: false,
                        closeOnCancel: false
                    },
                        function (isConfirm) {
                            if (isConfirm) {
                                $scope.cancellation_type = 'Unfulfilled Order'
                            } else {
                                $scope.cancellation_type = 'Wrong Order'
                            }
                            order_details.cancellation_type = $scope.cancellation_type;

                            AccountManagerServices.removeOrder(order_details).then(function (response) {
                                if (response.data.status == 'Done') {
                                    swal("Order Cancelled!", response.data.code.message, "success");
                                    $scope.orderList.splice(index, 1)
                                } else if (response.data.status == 'Transit') {
                                    swal("Order In Transit!", response.data.code.message, "error");
                                } else {
                                    swal("Can't cancel!", 'Order stage is beyond cancellation', "error");
                                }
                            }, function (err) {
                                alert('Error Occurred')
                            })
                        });
                } else {
                    //Ashish
                    swal("Not cancelled!", "Your order is safe :)", "error");
                }
            })
    };


    $scope.markInvalidPayment = function (item) {
        $scope.pollLoader = true
        var order_details = {
            orderCode: item.order_id,
            vehicleNumber: item.vehicle_data.vehicle_registration_number,
            tripStatus: item.current_status
        };
        if(item.trip){
            order_details['tripCode'] = item.trip;
        }
        swal({
            title: "Are you sure?",
            text: "You want mark this trip invalid ?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes",
            showLoaderOnConfirm: $scope.pollLoader,
            cancelButtonText: "No",
            closeOnConfirm: false,
            closeOnCancel: false
        },
            function (isConfirm) {
                if (isConfirm) {
                    //Badal
                    AccountManagerServices.markInvalidPayment(order_details).then(function (response) {
                        if (response.data.code.status === 200) {
                            var orchestrationId = response.data.data.details.orchestrationId;
                            $scope.orchestrationId = orchestrationId

                            var getApiStatus = function () {
                                AccountManagerServices.markInvalidPaymentStatus($scope.orchestrationId).then(function (response) {
                                    if (response.data.data.details) {
                                        var runtimeStatus = response.data.data.details.runtimeStatus;
                                        if (runtimeStatus === 'Completed') {
                                            $scope.pollLoader = false
                                            swal("Good job!", response.data.code.message, "success");
                                            $interval.cancel(promise);
                                        } else if (runtimeStatus === 'Failed') {
                                            $scope.pollLoader = false
                                            swal("Error Occured!", response.data.data.details.status, "error");
                                            $interval.cancel(promise);
                                        }
                                    }
                                }, function (err) {
                                    $scope.pollLoader = false
                                    swal("Error Occured!", response.message, "error");
                                })
                            };

                            var promise = $interval(getApiStatus, 1000);

                            $scope.cancel = function () {
                                $interval.cancel(promise);
                                $scope.orchestrationId = undefined
                            };

                            // swal("Mark Invalid!", response.data.code.message, "success");
                        } else if (response.data.code === 300 || response.data.code === 400) {
                            swal("Error Occured!", response.data.message, "error");
                        }
                    }, function (err) {
                        swal("Error Occured!", response.message, "error");
                        // alert('Error Occurred')
                    })
                } else {
                    swal("Not marked invalid!", "Your order is safe :)", "error");
                }
            })
    };


    $scope.filterData = {
        customer: '',
        vehicle_type: '',
        vehicle_number: '',
        order_ids: '',
        trip_codes: '',
        origin: '',
        destination: '',
        order_status: '',
        date_vehicle_required: '',
        to_date_vehicle_required: ''
    };

    /**
     * functionName:accountCancelledOrderListFilter
     * inputType:
     * outputType:
     * ownerName: Prafull
     * developedDate: 21/08/2017
     * testerName:
     * testDate:
     */

    $scope.cancelledOrdersListPage = {};
    $scope.accountCancelledOrderListFilter = function (count, filterData) {

        $scope.loading = true;
        $scope.customer_id = '';
        $scope.vehicle_type_id = '';
        $scope.origin_name = '';
        $scope.destination_name = '';
        $scope.date_vehicle_required = '';
        $scope.to_date_vehicle_required = '';
        console.log('filterData', filterData);


        if (filterData.customer)
            $scope.customer_id = filterData.customer.id;
        if (filterData.vehicle_type)
            $scope.vehicle_type_id = filterData.vehicle_type.id;
        if (filterData.origin)
            $scope.origin_name = filterData.origin.location_name;
        if (filterData.destination)
            $scope.destination_name = filterData.destination.location_name;
        if (filterData.date_vehicle_required)
            $scope.date_vehicle_required = filterData.date_vehicle_required;
        if (filterData.to_date_vehicle_required)
            $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

        $scope.filterDataObject = {
            customer: $scope.customer_id,
            vehicle_type: $scope.vehicle_type_id,
            origin: $scope.origin_name,
            destination: $scope.destination_name,
            from_date: $scope.date_vehicle_required,
            to_date: $scope.to_date_vehicle_required,
            order_status: $scope.order_status
        };
        console.log('$scope.filterDataObject', $scope.filterDataObject);
        $scope.orderFilterPage = {
            'next': '',
            'previous': '',
            'count': ''
        };
        // $scope.orderListingFilter = function () {
        AccountManagerServices.postCancelledOrderListFilter(count, $scope.filterDataObject).then(function (response) {
            if (count == undefined) {
                $scope.pagination.current = 1;
            }
            $scope.loading = false;
            if (response.data.results.code.status === 200) {
                $scope.cancelledOrdersListPage.count = response.data.count;
                $scope.cancelledOrderList = response.data.results.order_data;
                $scope.wf = response.data.results.wf;

            } else {
                swal(response.data.results.code.message)
            }

        }, function (error) {
            alert('Error Occurred')
        })
    };
    $scope.clearFilterCancelled = function () {
        $scope.pagination.current = 1;
        $scope.searchText = '';
        $scope.filterData = {
            customer: '',
            vehicle_type: '',
            vehicle_number: '',
            order_ids: '',
            trip_codes: '',
            origin: '',
            destination: '',
            date_vehicle_required: '',
            to_date_vehicle_required: ''
        };
        $scope.cancelledOrders();
    };

    $scope.counted = 1;
    $scope.setCountedValue = function () {
        $scope.$watch("searchText", function (query) {
            if ($scope.orderList) {
                $scope.counted = $filter("filter")($scope.orderList, query).length;
            }
            if ($scope.cancelledOrderList) {
                $scope.counted = $filter("filter")($scope.cancelledOrderList, query).length;
            }

        });
    };


    //Ashish
    $scope.customerStatus = function (id, item) {
        console.log('here');
        AccountManagerServices.getCustomerStatus(id).then(function (response) {
            if (response.data.code.status == 200) {
                $scope.status = response.data.order_log;
            } else {
                swal("Oops", response.data.code.message, "error");
            }
        }, function (err) {
            alert('Error Occurred')
        });
    }

    $scope.sortColumn = '';
    $scope.reverseSort = false;
    $scope.sortData = function (column) {
        console.log('column', column)
        console.log('$scope.sortColumn', $scope.sortColumn)
        $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
        $scope.sortColumn = column
    };

    $scope.getSortClass = function (column) {
        if ($scope.sortColumn == column) {
            //return $scope.reverseSort ? 'arrow-down':'arrow-up'
            return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
        } else {
            return 'fa fa-sort'
        }
    };

    $scope.accountOrderExportData = function (filterData) {
        $scope.csvloading = true;
        $scope.customer_id = '';
        $scope.vehicle_type_id = '';
        $scope.origin_name = '';
        $scope.destination_name = '';
        $scope.order_status = '';
        $scope.date_vehicle_required = '';
        $scope.to_date_vehicle_required = '';
        $scope.trip_order_code = '';
        $scope.vehicle = '';
        console.log("x->export", filterData)
        if (filterData.customer)
            $scope.customer_id = filterData.customer.id;
        if (filterData.vehicle_type)
            $scope.vehicle_type_id = filterData.vehicle_type.id;
        if (filterData.origin)
            $scope.origin_name = filterData.origin.location_name;
        if (filterData.destination)
            $scope.destination_name = filterData.destination.location_name;
        if (filterData.order_status)
            $scope.order_status = filterData.order_status.name;
        if (filterData.date_vehicle_required)
            $scope.date_vehicle_required = filterData.date_vehicle_required;
        if (filterData.to_date_vehicle_required)
            $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;
        if (filterData.trip_order_code)
            $scope.trip_order_code = filterData.trip_order_code;
        if (filterData.vehicle_number)
            $scope.vehicle = filterData.vehicle_number.vehicle_registration_number;

        $scope.filterDataObject = {
            customer: $scope.customer_id,
            vehicle_type: $scope.vehicle_type_id,
            origin: $scope.origin_name,
            destination: $scope.destination_name,
            from_date: $scope.date_vehicle_required,
            to_date: $scope.to_date_vehicle_required,
            order_status: $scope.order_status,
            vehicle_number: $scope.vehicle,
            trip_order_code: $scope.trip_order_code

        };
        AccountManagerServices.postOrderExportDataAcc($scope.filterDataObject).then(function (response) {
            console.log('response', response);
            if (response.data.code.status === 200) {
                $scope.orderExport = response.data.order_data;
                $scope.downloadOrderCsv = [];
                angular.forEach($scope.orderExport, function (value, key) {
                    console.log("x->value", value);
                    $scope.orderExportData = {
                        "order_id": value.order_id,
                        "indent_code": value.indent_data.indent_code,
                        "trip_order_code": value.trip,
                        "customer": value.indent_data.customer.company_name + '<' + value.indent_data.customer.customer_code + '>',
                        "vehicle_type": value.indent_data.vehicle_type.name,
                        "vehicle_number": value.vehicle_data.vehicle_registration_number,
                        "start_point": value.indent_data.start_point.location_name,
                        "destination_point": value.indent_data.destination_point.location_name,
                        "date": value.indent_data.date_vehicle_required,
                        "time": value.indent_data.time_vehicle_required,
                        "order_status": value.current_status,
                    };
                    $scope.downloadOrderCsv.push($scope.orderExportData);
                });
                var mystyle = {
                    headers: true,
                    column: {
                        style: {
                            Font: {
                                Bold: "1"
                            }
                        }
                    }
                };
                alasql('SELECT * INTO XLS("Order List.xls",?) FROM ?', [mystyle, $scope.downloadOrderCsv = $filter('orderBy')($scope.downloadOrderCsv, '-date')]);
                $scope.csvloading = false;
            } else {
                swal("Cancelled", response.code.message, "error")
            }
        })
    };

    $scope.accountCancelledOrderExportData = function (filterData) {
        $scope.csvloading = true;
        $scope.customer_id = '';
        $scope.vehicle_type_id = '';
        $scope.origin_name = '';
        $scope.destination_name = '';
        $scope.order_status = '';
        $scope.date_vehicle_required = '';
        $scope.to_date_vehicle_required = '';
        if (filterData.customer)
            $scope.customer_id = filterData.customer.id;
        if (filterData.vehicle_type)
            $scope.vehicle_type_id = filterData.vehicle_type.id;
        if (filterData.origin)
            $scope.origin_name = filterData.origin.location_name;
        if (filterData.destination)
            $scope.destination_name = filterData.destination.location_name;
        if (filterData.order_status)
            $scope.order_status = filterData.order_status.name;
        if (filterData.date_vehicle_required)
            $scope.date_vehicle_required = filterData.date_vehicle_required;
        if (filterData.to_date_vehicle_required)
            $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

        $scope.filterDataObject = {
            customer: $scope.customer_id,
            vehicle_type: $scope.vehicle_type_id,
            origin: $scope.origin_name,
            destination: $scope.destination_name,
            from_date: $scope.date_vehicle_required,
            to_date: $scope.to_date_vehicle_required,
            order_status: $scope.order_status
        };
        AccountManagerServices.postCancelledOrderExportData($scope.filterDataObject).then(function (response) {
            console.log('response', response);
            if (response.data.code.status === 200) {
                $scope.cancelledOrderExport = response.data.order_data;
                $scope.downloadOrderCsv = [];
                angular.forEach($scope.cancelledOrderExport, function (value, key) {

                    if (value.cancellation_date === null) {
                        $scope.cancellation_date = ''
                    } else {
                        $scope.cancellation_date = value.cancellation_date.split('T')[0];
                    }

                    $scope.orderExportData = {
                        "order_id": value.order_id,
                        "indent_code": value.indent_data.indent_code,
                        "customer": value.indent_data.customer.company_name + '<' + value.indent_data.customer.customer_code + '>',
                        "vehicle_type": value.indent_data.vehicle_type.name,
                        "start_point": value.indent_data.start_point.location_name,
                        "destination_point": value.indent_data.destination_point.location_name,
                        "date": value.indent_data.date_vehicle_required,
                        "time": value.indent_data.time_vehicle_required,
                        "cancellation_type": value.cancellation_type,
                        "cancellation_date": $scope.cancellation_date
                    };
                    $scope.downloadOrderCsv.push($scope.orderExportData);
                });
                var mystyle = {
                    headers: true,
                    column: {
                        style: {
                            Font: {
                                Bold: "1"
                            }
                        }
                    }
                };
                alasql('SELECT * INTO XLS("Cancelled Orders.xls",?) FROM ?', [mystyle, $scope.downloadOrderCsv = $filter('orderBy')($scope.downloadOrderCsv, '-date')]);
                $scope.csvloading = false;
            } else {
                swal("Cancelled", response.code.message, "error")
            }
        })
    };
}]);

/*********************************************************************************
 * ControllerName:assign.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('assign.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$location', '$filter', '$sce', function ($scope, $state, $rootScope, AccountManagerServices, $location, $filter, $sce) {

    $scope.$watch('online', function (newStatus) {
        if (newStatus === false) {
            swal("Internet Connection Lost!")
        }
    });

    $scope.trustSrc = function (src) {
        return $sce.trustAsResourceUrl(src);
    };

    /**
     *  v_2
     * @type {{}}
     */

    $scope.pod_v_2 = {};
    $scope.assignOrderList_v_2 = function () {
        $scope.loading = true;
        $scope.searchText = '';
        AccountManagerServices.getAssignOrderList().then(function (response) {
            $scope.loading = false;
            if (response.data.results.code.status == 200) {
                $scope.orderAssigns_v_2 = response.data.results.trip_data;
            } else {
                alert("Something bad happened, Please try again.")
            }

        }, function (err) {
            alert("Error Occurred!")
        })
    }


    $scope.pod = {};
    $scope.assignOrderList = function () {
        $scope.loading = true;
        $scope.searchText = '';
        AccountManagerServices.getAssignOrderList().then(function (response) {
            $scope.loading = false;
            if (response.data.results.code.status == 200) {
                $scope.orderAssigns = response.data.results.trip_data;
            } else {
                alert("Something bad happened, Please try again.")
            }

        }, function (err) {
            alert("Error Occurred!")
        })
    }


    $scope.counted = 1;
    $scope.setCountedValue = function () {
        $scope.$watch("searchText", function (query) {
            $scope.counted = $filter("filter")($scope.orderAssigns, query).length;
        });
    };


    $scope.driverDetailsAccount = function (orderAssign) {
        AccountManagerServices.driverDataAccount(orderAssign.id).then(function (response) {
            if (response.data.code.status == 200) {
                $scope.driver = response.data.data_list;
            } else {
                swal("Cancelled", response.code.message, "error")
            }
        });
    };

    $scope.vehicleDetailAccount = function (vehicleData) {
        AccountManagerServices.vehicleDataAccount(vehicleData.id).then(function (response) {
            if (response.data.code.status === 200) {
                $scope.vehicle = response.data.data_list;
            } else {
                swal("Cancelled", response.code.message, "error")
            }
        }, function (error) {
            alert('Error Occurred')
        })
    };

    $scope.showDlPicture = function (dl_pic) {

        alert("vishnu")
        if (pic) {
            AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                // console.log(response)
                $scope.showDlPic = response.data.url
            });
        } else {
            $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
        }

    };

    $scope.showDlPicture_v2 = function (dl_pic, d) {
        $scope.name = d;

        // alert("vishnu")
        if (pic) {
            AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                // console.log(response)
                $scope.showDlPic = response.data.url
            });
        } else {
            $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
        }

    };
    $scope.showInsuPicture = function (insurance_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                // console.log(response)
                $scope.showInsurancePic = response.data.url
            });
        } else {
            $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
        }

    };
    $scope.showRcPicture = function (rc_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                // console.log(response)
                $scope.showRcPic = response.data.url
            });
        } else {
            $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
        }

    };
    $scope.showFitPicture = function (fitness_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                // console.log(response)
                $scope.showFitnessPic = response.data.url
            });
        } else {
            $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
        }

    };
    $scope.showLrPicture = function (lr_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                // console.log(response)
                $scope.showLrPic = response.data.url
            });
        } else {
            $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
        }

    };
    $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                // console.log(response)
                $scope.showDriverVehiclePic = response.data.url
            });
        } else {
            $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
        }

    };
    $scope.showLrNumPicture = function (lr_num_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                // console.log(response)
                $scope.showLrNumPic = response.data.url
            });
        } else {
            $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
        }


    };
    $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(driver_vehi_pic).then(function (response) {

                // console.log(response)
                $scope.showDriverAndVehiclePic = response.data.url
            });
        } else {
            $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic
        }

    };
    $scope.showLrNumberPicture = function (lr_num_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                // console.log(response)
                $scope.showLrNumPic = response.data.url
            });
        } else {
            $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
        }


    };
    $scope.showPermitPicture = function (permit_pic) {
        if (pic) {
            AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                // console.log(response)
                $scope.showPermitPic = response.data.url
            });
        } else {
            $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
        }


    };

    $scope.sortColumn = '';
    $scope.reverseSort = false;
    $scope.sortData = function (column) {
        console.log('column', column)
        console.log('$scope.sortColumn', $scope.sortColumn)
        $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
        $scope.sortColumn = column
    };

    $scope.getSortClass = function (column) {
        if ($scope.sortColumn == column) {
            //return $scope.reverseSort ? 'arrow-down':'arrow-up'
            return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
        } else {
            return 'fa fa-sort'
        }
        //return '';
    };

    $scope.assignOrderList();
}]);
/*********************************************************************************
 * ControllerName:completed.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('completed.listing', ['$scope', '$state', '$rootScope', 'AccountManagerServices', '$location', '$filter', '$modal', 'spinnerService', '$timeout', 'MasterService', '$sce',
    function ($scope, $state, $rootScope, AccountManagerServices, $location, $filter, $modal, spinnerService, $timeout, MasterService, $sce) {

        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("Internet Connection Lost!")
            }
        });

        $scope.monthRangeList = [
            {
                monthIntervalLabel: 'Last One Month',
                monthIntervalValue: 1,
            },
            {
                monthIntervalLabel: 'Last Two Months',
                monthIntervalValue: 2,
            },
            {
                monthIntervalLabel: 'Last Three Months',
                monthIntervalValue: 3,
            },
            {
                monthIntervalLabel: 'Last Four Months',
                monthIntervalValue: 4,
            }
        ]

        $scope.checkMonthRange = function (changedDate) {
            if ($scope.filterData.date_vehicle_required && $scope.filterData.to_date_vehicle_required) {
                const fromDate = moment($scope.filterData.date_vehicle_required);
                const toDate = moment($scope.filterData.to_date_vehicle_required);

                let diffInDays = toDate.diff(fromDate, 'days');
                let isValidDateRange = true;

                if (diffInDays > 120) {
                    isValidDateRange = false;
                    swal("Cancelled", "Please select dates within 120 days range", "error");
                } else if (diffInDays < 0) {
                    isValidDateRange = false;
                    swal("Cancelled", "Start Date should be less than End Date", "error");
                }
                if (isValidDateRange) {
                    return;
                }
                if (changedDate.toUpperCase() === 'STARTDATE') {
                    $scope.filterData.date_vehicle_required = undefined;
                } else {
                    $scope.filterData.to_date_vehicle_required = undefined;
                }
            }
        }

        $scope.markTripInvalid = function (tripCode) {
            var trip_code = {
                "trip_code": tripCode
            }
            swal({
                title: "Are you sure?",
                text: "You Want mark this trip Invalid?",
                type: "warning",
                showCancelButton: true,
                showLoaderOnConfirm: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $scope.loading = true;

                    AccountManagerServices.markTripInvalid(trip_code).then(function (response) {
                        console.log(response)
                        $scope.loading = false;
                        if (response.code == 200) {
                            swal("Good job!", response.message, "success");
                        } else {
                            swal("Cancelled", response.message, "error")
                        }
                    }, function (err) {
                        $scope.loading = false;
                        swal("Unauthorized!", err.detail, "error")
                    })
                }

            })
        };

        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };
        /******************************* Master Data *****************************************/
        $scope.entityType = MasterService.entityType;
        /******************************* Master Data *****************************************/


        $('#ownedCustomerNext').click(function () {
            if (!$scope.podDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });
        $('#marketCustomerNext').click(function () {
            if (!$scope.podDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });
        $('#brokerNext').click(function () {
            if (!$scope.customerDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });


        $scope.counted = 1;
        $scope.isPodActive = 'active';
        $scope.isCustActive = '';
        $scope.isCompActive = '';
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.completedTrips, query).length;
            });
        };

        $scope.can_approve_broker_charges = $rootScope.can_approve_broker_charges;

        $scope.podEditBrokerChargesState = {
            showFieldDisabled: false,
            showWarningMessage: false
        }

        $scope.podStatus = function () {
            $scope.podStatusFilterList = [{
                id: '1',
                name: 'Completed',
                status: true
            }, {
                id: '2',
                name: 'Pending',
                status: false
            }, {
                id: '3',
                name: 'Uploaded',
                status: false
            }, {
                id: '4',
                name: 'Rejected',
                status: false
            }];
        };

        $scope.initScrollSync = function () {
            $timeout(function () {
                const scrollContainers = document.querySelectorAll('.multi-column-scroll-container');
                let isScrolling = false;

                scrollContainers.forEach(container => {
                    // 🔧 Set container height to match parent <td>
                    const td = container.closest('td');
                    if (td) {
                        const tdHeight = td.offsetHeight;
                        container.style.height = tdHeight + 'px';
                    }

                    container.addEventListener('scroll', function () {
                        // console.log('Scroll event:', event.target, 'scrollLeft:', event.target.scrollLeft);
                        if (!isScrolling) {
                            isScrolling = true;
                            const scrollLeft = this.scrollLeft;

                            scrollContainers.forEach(otherContainer => {
                                if (otherContainer !== this) {
                                    otherContainer.scrollLeft = scrollLeft;
                                }
                            });

                            setTimeout(() => {
                                isScrolling = false;
                            }, 10);
                        }
                    });
                });
            }, 100); // Small delay to ensure DOM is ready
        };
        

        $scope.brokerChargesStatusList = [
            {
                label: 'Pending for Approval',
                value: 1,
            },
            {
                label: 'Approved',
                value: 2,
            },
            {
                label: 'NA',
                value: 3,
            }
        ]

        $scope.brokerChargePendingForApprovalTrips = [];

        $scope.tripsSelected = {
            allTripsSelected: false
        };
        $scope.selectedTripsCount = 0;
        $scope.selectedTripsData = [];

        $scope.$watch('completedTrips', function () {
            $scope.brokerChargePendingForApprovalTrips = [];
            angular.forEach($scope.completedTrips, function (item) {
                if ($scope.isTripBrokerChargePendingForApproval(item)) {
                    $scope.brokerChargePendingForApprovalTrips.push(item);
                }
            })
        }, true);

        $scope.isTripBrokerChargePendingForApproval = function(trip) {
            return !!trip.is_market && trip?.pod_data?.pod_broker_data?.broker_charge_status == 'Pending for Approval'
        }

        $scope.handleSelectAllTripsForApproval = function (isAllChecked) {
            if (!isAllChecked) {
                $scope.selectedTripsData = [];
                $scope.selectedTripsCount = 0;
                $scope.tripsSelected.allTripsSelected = false;
                $scope.allTripsSelected = false;
                angular.forEach($scope.completedTrips, function (trip) {
                    if ($scope.isTripBrokerChargePendingForApproval(trip)) {
                        trip.selected = false;
                    }
                });
                return;
            }

            $scope.tripsSelected.allTripsSelected = isAllChecked;
            $scope.selectedTripsCount = 0;
            angular.forEach($scope.completedTrips, function (trip) {
                if ($scope.isTripBrokerChargePendingForApproval(trip)) {
                    trip.selected = isAllChecked;
                    $scope.selectedTripsCount = $scope.selectedTripsCount + 1;
                    $scope.selectedTripsData.push(trip);
                }
            });
        };

        $scope.handleSelectTripForApproval = function (selectedTrip, isChecked) {
            if (isChecked) {
                selectedTrip.selected = true;
                $scope.selectedTripsCount = $scope.selectedTripsCount + 1;
                $scope.selectedTripsData.push(selectedTrip);

                if ($scope.selectedTripsCount == $scope.brokerChargePendingForApprovalTrips.length) {
                    $scope.tripsSelected.allTripsSelected = true;
                }
            } else {
                selectedTrip.selected = false;
                $scope.tripsSelected.allTripsSelected = false;
                $scope.selectedTripsCount = $scope.selectedTripsCount - 1;
                $scope.selectedTripsData.splice($scope.selectedTripsData.indexOf(selectedTrip), 1);
            }
        };

        $scope.bulkApproveBrokerCharges = function() {
            if($scope.selectedTripsCount == 0) {
                swal("Error", 'Select Items To Approve', "error");
                return;
            }

            const selectedTripsOrderCodes = $scope.selectedTripsData.map(trip => ({ "order_code": trip.order_code }));
            $scope.bulkApproveLoading = true;

            AccountManagerServices.bulkApproveBrokerCharges(selectedTripsOrderCodes).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.bulkApproveLoading = false;
                    swal("Good Job!", response.data.code.message)
                    $scope.selectedTripsData = [];
                    $scope.selectedTripsCount = 0;
                    $scope.allTripsSelected = false;
                    $scope.completedTripFilter($scope.pagination.current, $scope.filterData);
                } else {
                    $scope.bulkApproveLoading = false;
                    swal("oops!", response.data.code.message, "error")
                }
            })
        }

        $scope.enablePodEditBrokerChargesFields = function () {
            $scope.podEditBrokerChargesState.showFieldDisabled = false;
        }

        /**
         * functionName:tripcomplete_listing
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 20/07/2017
         * testerName:
         * testDate:
         */

        $scope.completedTripPage = {};
        $scope.tripcomplete_listing = function (count) {
            $scope.loading = true;
            const currentDate = new Date();
            $scope.filterData.to_date_vehicle_required = currentDate.toISOString().split('T')[0];
            currentDate.setMonth(currentDate.getMonth() - 1);
            $scope.filterData.date_vehicle_required = currentDate.toISOString().split('T')[0];
            const filterDateObject = {
                'from_date': $scope.filterData.date_vehicle_required,
                'to_date': $scope.filterData.to_date_vehicle_required
            }
            
            AccountManagerServices.completedTripFilterService(count, filterDateObject).then(function (response) {
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {
                    $scope.completedTrips = response.data.results.trip_data;
                    $scope.wf = response.data.results.wf;
                    $scope.completedTripPage.count = response.data.count;
                    //Reset Bulk approve related data
                    $scope.selectedTripsCount = 0;
                    $scope.selectedTripsData = [];
                    $scope.tripsSelected.allTripsSelected = false;

                    $scope.initScrollSync();
                } else {
                    swal("oops!", response.data.results.code.message, "error")
                }
            })
        };

        // v_2
        $scope.completedTripPage_v_2 = {};
        $scope.tripcomplete_listing_v_2 = function (count) {
            $scope.loading = true;
            AccountManagerServices.completedTrip(count).then(function (response) {
                console.log('s', response)
                $scope.is_superuser = response.results.is_superuser;
                $scope.loading = false;
                if (response.results.code.status === 200) {
                    $scope.completedTrips_v_2 = response.results.trip_data;
                    $scope.wf_v_2 = response.results.wf;
                    $scope.completedTripPage_v_2.count = response.count;
                    // $scope.asyncRequestForFilter();
                } else {
                    alert(response.results.code.message)
                }
            })
        };

        $scope.getLrNumbers = function (data) {
            console.log('NUM', data)
            if (data.length > 3) {
                var lr = {
                    'lr_num': data
                };
                AccountManagerServices.getLrNumSer(lr).then(function (response) {
                    console.log('response', response)
                    if (response.data.code.status === 200) {
                        $scope.lrName = response.data.lr;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })
            }
        };

        $scope.showDlPicture_v2c = function (dl_pic, d) {
            $scope.name = d;

            // alert("vishnu")
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };

        //Ashish
        $scope.receivedPOD = function (completedTrip) {
            $scope.trip_id = completedTrip.id;
            $scope.indexForUpdatePod = $scope.completedTrips.indexOf(completedTrip);
            AccountManagerServices.receivedPODUpdate($scope.trip_id).then(function (response) {
                if (response.code.status == 200) {
                    $scope.completedTrips[$scope.indexForUpdatePod].pod_status = response.PODstatus;
                    swal("Good job!", response.code.message, "success");
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        //Ashish
        $scope.unReceivedPOD = function (completedTrip) {
            $scope.trip_id = completedTrip.id;
            $scope.indexForUpdatePod = $scope.completedTrips.indexOf(completedTrip);
            AccountManagerServices.unReceivedPODUpdate($scope.trip_id).then(function (response) {
                if (response.code.status == 200) {
                    $scope.completedTrips[$scope.indexForUpdatePod].pod_status = response.PODstatus;
                    swal("Good job!", response.code.message, "success");
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        //Ashish
        $scope.getAdvancePayDetails = function (order_code) {
            AccountManagerServices.advancePayDetails(order_code).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.broker_rate = response.data.details.broker_rate;
                    $scope.cash_adv = response.data.details.cash_adv;
                    $scope.broker_advance = response.data.details.broker_advance;
                    $scope.balance_paid = response.data.details.payment_due;
                } else { }
            }, function (err) {
                alert('Error Occurred')

            });
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false);
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
        };

        $scope.accountOrderExportData = function (orderList) {
            $scope.csvloading = true;
            $scope.downloadAccountOrderCsv = [];
            angular.forEach(orderList, function (value, key) {
                $scope.accountOrderExportValue = {
                    "order_id": value.order_id,
                    "indent_code": value.indent_data.indent_code,
                    "customer": value.indent_data.customer.company_name + '<' + value.indent_data.customer.customer_code + '>',
                    "vehicle_type": value.indent_data.vehicle_type.name,
                    "start_point": value.indent_data.start_point.location_name,
                    "destination_point": value.indent_data.destination_point.location_name,
                    "date": value.indent_data.date_vehicle_required,
                    "time": value.indent_data.time_vehicle_required,
                    "order_status": value.current_status
                };
                $scope.downloadAccountOrderCsv.push($scope.accountOrderExportValue);
            });
            var mystyle = {
                headers: true,
                column: {
                    style: {
                        Font: {
                            Bold: "1"
                        }
                    }
                }
            };
            alasql('SELECT * INTO XLS("account-order.xls",?) FROM ?', [mystyle, $scope.downloadAccountOrderCsv = $filter('orderBy')($scope.downloadAccountOrderCsv, 'date')]);
            $scope.csvloading = false;

        };


        $scope.getOwnPayDetails = function (order_code) {
            AccountManagerServices.ownAdvancePayDetails(order_code).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.ownAdvanceData = response.data.details;
                    $scope.ownAdvanceData.totalEntry = 0;
                    $scope.ownAdvanceData.totalFuel = 0;
                    $scope.ownAdvanceData.totalMeals = 0;
                    $scope.ownAdvanceData.totalPolice = 0;
                    $scope.ownAdvanceData.totalToll = 0;
                    $scope.ownAdvanceData.totalAdvance = 0;
                    $scope.ownAdvanceData.totalExpenses = 0;
                    angular.forEach($scope.ownAdvanceData, function (value) {
                        if (value.advance_data.advance_for_entry != '')
                            $scope.ownAdvanceData.totalEntry += parseInt(value.advance_data.advance_for_entry);
                        if (value.advance_data.advance_for_fuel != '')
                            $scope.ownAdvanceData.totalFuel += parseInt(value.advance_data.advance_for_fuel);
                        if (value.advance_data.advance_for_meals != '')
                            $scope.ownAdvanceData.totalMeals += parseInt(value.advance_data.advance_for_meals);
                        if (value.advance_data.advance_for_police != '')
                            $scope.ownAdvanceData.totalPolice += parseInt(value.advance_data.advance_for_police);
                        if (value.advance_data.total_advance != '')
                            $scope.ownAdvanceData.totalAdvance += parseInt(value.advance_data.total_advance);
                        if (value.actual_data.total_actual != '')
                            $scope.ownAdvanceData.totalExpenses += parseInt(value.actual_data.total_actual);
                        if (value.advance_data.advance_for_toll != '')
                            $scope.ownAdvanceData.totalToll += parseInt(value.advance_data.advance_for_toll);
                    });
                } else { }
            }, function (err) {
                alert('Error Occurred')
            })
        };


        $scope.clearFilter = function () {
            $scope.pagination.current = 1;
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                vehicle_no: ''
            };
            $scope.tripcomplete_listing();
        };

        $scope.showDlPicture = function (dl_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url
                });
            } else {
                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
            }

        };
        $scope.showRcPicture = function (rc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url
                });
            } else {
                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
            }

        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url
                });
            } else {
                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
            }

        };
        $scope.showLrPicture = function (lr_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url
                });
            } else {
                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };
        $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url
                });
            } else {
                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
            }

        };
        $scope.showLrNumPicture = function (lr_num_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }

        };
        $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehi_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverAndVehiclePic = response.data.url
                });
            } else {
                $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic
            }


        };
        $scope.showLrNumberPicture = function (lr_num_pic) {
            $scope.showLrNumPic = null;
            if (!lr_num_pic) {
                setTimeout(function() {
                    $('#lr_number_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(lr_num_pic)) {
                $scope.showLrNumPic = lr_num_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showLrNumPic = response.data.url;
                    } else {
                        $('#lr_number_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }

        };
        $scope.showPermitPicture = function (permit_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPermitPic = response.data.url
                });
            } else {
                $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
            }

        };

        $scope.showFrontInvoicePicture = function (front_invoice) {
            $scope.$watch('front_invoice', function () {
                if (!front_invoice) {
                    setTimeout(function() {
                        $('#frontInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(front_invoice)) {
                    $rootScope.invoiceFront = front_invoice;
                    $scope.invoiceFront = front_invoice;
                    return;
                }

                if (pic) {
                    AccountManagerServices.picLoadServerA(front_invoice).then(function (response) {

                        // console.log(response)
                        if (response.data.code.status === 200) {
                            $scope.invoiceFront = response.data.url;
                            $rootScope.invoiceFront = response.data.url;
                        } else {
                            $('#frontInvoice').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                } else {
                    $rootScope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                    $scope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                }

            })
        };

        $scope.showFrontInvoicePic = function (front_invoice) {
            if (!front_invoice) {
                setTimeout(function() {
                    $('#frontInvoicePOD').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(front_invoice)) {
                $scope.invoiceFrontPic = front_invoice;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(front_invoice).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.invoiceFrontPic = response.data.url;
                    } else {
                        $('#frontInvoicePOD').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.invoiceFrontPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
            }

        };

        $scope.showBackInvoicePicture = function (back_invoice) {
            if (!back_invoice) {
                setTimeout(function() {
                    $('#backInvoice').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(back_invoice)) {
                $rootScope.invoiceBack = back_invoice;
                $scope.invoiceBack = back_invoice;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(back_invoice).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.invoiceBack = response.data.url;
                        $rootScope.invoiceBack = response.data.url;
                    } else {
                        $('#backInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $rootScope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
                $scope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
            }

        };

        $scope.showBackInvoicePic = function (back_invoice) {
            if (!back_invoice) {
                setTimeout(function() {
                    $('#backInvoicePOD').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(back_invoice)) {
                $scope.invoiceBackPic = back_invoice;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(back_invoice).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.invoiceBackPic = response.data.url;
                    } else {
                        $('#backInvoicePOD').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.invoiceBackPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
            }

        };

        $scope.isPdf = function (url) {
            return url && url.toLowerCase().includes(".pdf");
        };

        $scope.isFileFromGoogleStorage = function (file) {
            return file.includes('https://storage.googleapis.com');
        };

        $scope.showEwayBillPicture = function (e_way_bill_pic) {
            if (!e_way_bill_pic) {
                setTimeout(function() {
                    $('#e_way_bill_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(e_way_bill_pic)) {
                $rootScope.eWayBillPic = e_way_bill_pic;
                $scope.eWayBillPic = e_way_bill_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(e_way_bill_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.eWayBillPic = response.data.url;
                        $rootScope.eWayBillPic = response.data.url;
                    } else {
                        $('#e_way_bill_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $rootScope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
                $scope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
            }

        };

        $scope.showEwayBillPic = function (e_way_bill_pic) {
            if (!e_way_bill_pic) {
                setTimeout(function() {
                    $('#e_way_bill_pic_pod').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(e_way_bill_pic)) {
                $scope.eWayBillPic = e_way_bill_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(e_way_bill_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.eWayBillPic = response.data.url;
                    } else {
                        $('#e_way_bill_pic_pod').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
            }
        }

        $scope.generateOTPEmployee = function (data) {
            $scope.txn = '';
            console.log('dataaaa', data);
            console.log('data.pod_data.pod_broker_data.detention', data.pod_data.pod_broker_data.detention);


            total_charges = parseFloat(data.pod_data.pod_broker_data.loading_charge) + parseFloat(data.pod_data.pod_broker_data.unloading_charge) + parseFloat(data.pod_data.pod_broker_data.deduction_value) + parseFloat(data.pod_data.pod_broker_data.detention) + parseFloat(data.pod_data.pod_broker_data.b_surcharge) + parseFloat(data.pod_data.pod_broker_data.taxes)
            console.log('$scope.response', $scope.response);
            $scope.deten_otp = true;
            $scope.data = 'BROKER_CHARGES_APR';
            AccountManagerServices.getOtpCode($scope.data, total_charges, $scope.response.data.data.broker_company).then(function (response) {
                $scope.OtpCode = response;
                $scope.txn = $scope.OtpCode.data.txn;
            }, function (err) {
                alert("Error Occurred!")
            })
        };

        $scope.HideModel = function () {
            $scope.isCustActive = '';
            $scope.isCompActive = '';
            $scope.isPodActive = 'active';

            $scope.podEditBrokerChargesState.showWarningMessage = false;
            $scope.podEditBrokerChargesState.showFieldDisabled = false;

            angular.element('#pod_Edit').modal('hide');
        };


        $scope.checkApproval = function () {
            console.log("Check approval", $scope.tripCompleted)
            if ($scope.tripCompleted == undefined) {
                total_charges = 0.0

            }
            else {
                if ($scope.tripCompleted.pod_data == undefined) {
                    total_charges = 0.0

                }
                else {
                    if ($scope.tripCompleted.pod_data.pod_broker_data == undefined) {
                        total_charges = 0.0

                    }
                    else {
                        total_charges = parseFloat($scope.tripCompleted.pod_data.pod_broker_data.loading_charge) + parseFloat($scope.tripCompleted.pod_data.pod_broker_data.unloading_charge) + parseFloat($scope.tripCompleted.pod_data.pod_broker_data.deduction_value) + parseFloat($scope.tripCompleted.pod_data.pod_broker_data.detention) + parseFloat($scope.tripCompleted.pod_data.pod_broker_data.b_surcharge) + parseFloat($scope.tripCompleted.pod_data.pod_broker_data.taxes) + parseFloat($scope.tripCompleted.broker_advance_cash ?? 0) + parseFloat($scope.tripCompleted.broker_advance ?? 0)

                    }
                }

            }

            console.log("Total Charges", total_charges)

            return total_charges

        }

        $scope.isNullValue = function(value) {
            return value === null || value === undefined
                || (((typeof value) === "string") ? value.trim() : value) === "";
        }

        $scope.isObjectEmpty = function(obj) {
            return (obj === null || obj === undefined 
                || (Object.keys(obj).length === 0 && obj.constructor === Object));
        }

        $scope.lrInfo = [{lr_reciept: '', pic_url: ''}];
        $scope.lrReceiptNoList = [];

        $scope.lrAdditionalInfo = function () {
            if ($scope.lrInfo.length >= 1) {
                $scope.active = true
            }
            var newItemNo = $scope.lrInfo.length + 1;
            $scope.lrInfo.push({lr_reciept: '', pic_url: ''});
        }

        $scope.removeAddInfo = function (index) {
            $scope.idx = 'remove_' + index
            $scope.del_lr = 'remove_' + 1
            if ($scope.idx === $scope.del_lr) {
                $scope.active = false
            }
            //$scope.active = false
            var lastItem = $scope.lrInfo.length - 1;
            $scope.lrInfo.splice(lastItem);
            $scope.lrReceiptNoList.splice(lastItem);
        };

        $scope.extractLRNumbers = function(lrData) {
            lrData && angular.forEach(lrData, function(item, index) {
                if (!$scope.isObjectEmpty(item)) {
                    $scope.updateLrReceiptsList(item.lr_reciept, index);
                }
            });
        }
        $scope.updateLrReceiptsList = function(lrNo, index) {
            if (!$scope.isNullValue(lrNo)) {
                $scope.lrReceiptNoList[index] = lrNo;
            }
            if (index >= 1) {
                $scope.active = true
            } else {
                $scope.active = false;
            }
        }

        $scope.$watch('lrReceiptNoList', function() {
            const lrNoSet = new Set();
            $scope.lrReceiptNoList && angular.forEach($scope.lrReceiptNoList, function(item, index) {
                let isLRDuplicate = 'lr_reciept_duplicate_' + index;
                $scope[isLRDuplicate] = false;
                if (!$scope.isNullValue(item)) {
                    if (lrNoSet.has(item)) {
                        $scope[isLRDuplicate] = true;
                    } else {
                        lrNoSet.add(item);
                    }
                }
            })
        }, true);

        $scope.showInvoiceError = false;
        $scope.showInvoiceFileError = false;
        $scope.showEWayBillNumberError = false;
        $scope.showEWayBillNumberFileError = false;

        $scope.validateInvoice = function () {
            const invoiceNo = $scope.tripCompleted.invoice_number;
            const fronInvoice = $scope.tripCompleted.front_invoice;
            const backInvoice = $scope.tripCompleted.back_invoice;
            const invoiceFile = fronInvoice || backInvoice;

            if (invoiceNo && !invoiceFile) {
                $scope.showInvoiceFileError = true;
                $scope.showInvoiceError = false;
            } else if (!invoiceNo && invoiceFile) {
                $scope.showInvoiceError = true;
                $scope.showInvoiceFileError = false;
            } else {
                $scope.showInvoiceError = false;
                $scope.showInvoiceFileError = false;
            }
        };

        $scope.validateEwayBill = function () {
            const eWayBillNo = $scope.tripCompleted.e_way_bill_number;
            const eWayBillFile = $scope.tripCompleted.e_way_bill_pic;

            if (eWayBillNo && !eWayBillFile) {
                $scope.showEWayBillNumberFileError = true;
                $scope.showEWayBillNumberError = false;
            } else if (!invoiceNo && invoiceFile) {
                $scope.showEWayBillNumberError = true;
                $scope.showEWayBillNumberFileError = false;
            } else {
                $scope.showEWayBillNumberError = false;
                $scope.showEWayBillNumberFileError = false;
            }
        };

        $scope.hasDuplicateLRNumbers = function() {
            return $scope.lrReceiptNoList.length != new Set($scope.lrReceiptNoList).size;
        }

        $scope.isMarketPODAlreadyUploaded = false;
        $scope.podDataEdit = function (completedTrip) {
            console.log('completedTrip', completedTrip);
            $scope.OtpCode = {};
            $scope.deten_otp = false;
            angular.element("input[type='file']").val(null);
            $('#myTab a[data-target="#podDetails"]').click();
            $scope.indexForUpdatePod = $scope.completedTrips.indexOf(completedTrip);
            $rootScope.indexForUpdatePodOwned = $scope.indexForUpdatePod;
            $rootScope.completedTripsOwned = $scope.completedTrips;
            $scope.v_type = completedTrip.order_data.v_type;
            $scope.tripCompleted = {};
            $scope.tripCompleted.trip_code = completedTrip.trip_code;
            $scope.lrInfo = [{lr_reciept: '', pic_url: ''}];
            AccountManagerServices.gettingPod(completedTrip.id).then(function (response) {
                console.log('response_here', response);
                var lr = '';
                if (response.data.code.status == 200) {
                    var oneDay = 24 * 60 * 60 * 1000;
                    $scope.lrInfo = response.data.data.lr.lr_data;
                    $scope.response = response;
                    $scope.tripCompleted.pod_uploaded_data = response.data.data.pod_uploaded_data;
                    $scope.tripCompleted.broker_rate = response.data.data.broker_rate
                    $scope.tripCompleted.broker_advance_cash = response.data.data.broker_advance_cash
                    $scope.tripCompleted.broker_advance = response.data.data.broker_advance
                    $scope.tripCompleted.is_market = response.data.data.is_market;
                    $scope.tripCompleted.can_upload = response.data.can_upload;
                    $scope.tripCompleted.tat = response.data.data.tat;
                    $scope.tripCompleted.id = completedTrip.id;
                    $scope.tripCompleted.pod_updated_by = response.data.data.pod_updated_by;
                    $scope.tripCompleted.cus_deten3 = response.data.data.cus_deten3;
                    $scope.tripCompleted.cus_deten10 = response.data.data.cus_deten10;
                    $scope.tripCompleted.cus_detenN = response.data.data.cus_detenN;
                    $scope.tripCompleted.cus_deten_first = response.data.data.cus_deten_first;
                    $scope.tripCompleted.cus_deten_second = response.data.data.cus_deten_second;
                    $scope.tripCompleted.cus_deten_third = response.data.data.cus_deten_third;
                    $scope.tripCompleted.entity_name = response.data.data.entity_name;
                    $scope.tripCompleted.load_charge = response.data.data.load_charge;
                    $scope.tripCompleted.customer_detention_rate = 'For ' + response.data.data.cus_deten_first + ' days' + ' ' + ':' + response.data.data.cus_deten3 + ',' + '  ' + 'For ' + response.data.data.cus_deten_second + ' days' + ' ' + ':' + response.data.data.cus_deten10 + ',' + '  ' + 'Beyond ' + response.data.data.cus_deten_third + ' days' + ': ' + response.data.data.cus_detenN;
                    $scope.tripCompleted.first_deten = response.data.data.first_deten;
                    $scope.tripCompleted.second_deten = response.data.data.second_deten;
                    $scope.tripCompleted.third_deten = response.data.data.third_deten;
                    $scope.tripCompleted.broker_detention_rate = 'For ' + response.data.data.first_deten + ' days' + ' ' + ':' + response.data.data.deten3 + ',' + '  ' + 'For ' + response.data.data.second_deten + ' days' + ' ' + ':' + response.data.data.deten10 + ',' + '  ' + 'Beyond ' + response.data.data.third_deten + ' days' + ': ' + response.data.data.detenN;
                    $scope.tripCompleted.deten3 = response.data.data.deten3;
                    $scope.tripCompleted.deten10 = response.data.data.deten10;
                    $scope.tripCompleted.detenN = response.data.data.detenN;
                    $scope.tripCompleted.lr = response.data.data.lr.lr_data;
                    $scope.tripCompleted.invoice_data = response.data.data.invoice_pic;
                    $scope.tripCompleted.e_way_bill_data = response.data.data.e_way_bill;


                    $scope.extractLRNumbers($scope.lrInfo);
                    if (response.data.data.pod_updated_by.mobile_no === undefined) {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username
                    } else {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username + ',' + response.data.data.pod_updated_by.mobile_no
                    }
                    if (!response.data.data.pod_trip_end_date)
                        $scope.tripCompleted.pod_trip_end_date = completedTrip.trip_end_date;
                    else
                        $scope.tripCompleted.pod_trip_end_date = $filter('date')(response.data.data.pod_trip_end_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_trip_end_time)
                        $scope.tripCompleted.pod_trip_end_time = completedTrip.trip_end_time;
                    else
                        $scope.tripCompleted.pod_trip_end_time = $filter('date')(response.data.data.pod_trip_end_time, 'HH:mm');
                    if (!response.data.data.pod_unloading_date)
                        $scope.tripCompleted.pod_unloading_date = completedTrip.unloading_date;
                    else
                        $scope.tripCompleted.pod_unloading_date = $filter('date')(response.data.data.pod_unloading_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_unloading_time)
                        $scope.tripCompleted.pod_unloading_time = completedTrip.unloading_time;
                    else
                        $scope.tripCompleted.pod_unloading_time = $filter('date')(response.data.data.pod_unloading_time, 'HH:mm');

                    var day1 = new Date($scope.tripCompleted.pod_trip_end_date + " " + $scope.tripCompleted.pod_trip_end_time)
                    var day2 = new Date($scope.tripCompleted.pod_unloading_date + " " + $scope.tripCompleted.pod_unloading_time)
                    var one_day = 1000 * 60 * 60 * 24;
                    var date1_ms = day1.getTime();
                    var date2_ms = day2.getTime();
                    var difference_ms = date2_ms - date1_ms;
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay) - 1;
                    if ($scope.tripCompleted.detention_days === -1) {
                        $scope.tripCompleted.detention_days = 0;
                    }
                    var total_deten = 0;
                    var first_deten_value = 0;
                    var second_deten_value = 0;
                    var third_remaining_value = 0;
                    var remaining_days_beyond = 0;
                    var days = $scope.tripCompleted.detention_days;
                    if (['CIPL', 'UNBR', 'FKRT'].indexOf(completedTrip.order_data.customer_code) >= 0) {
                        if (days > 0) {
                            var remaining_days_after_first = days - $scope.tripCompleted.first_deten;
                            if (remaining_days_after_first > 0) {
                                var first_detention_days = days - remaining_days_after_first;
                                first_deten_value = parseInt($scope.tripCompleted.deten3) * first_detention_days;

                            } else {
                                first_deten_value = parseInt($scope.tripCompleted.deten3) * days;
                            }

                            if (remaining_days_after_first > 0) {
                                var remaining_days_after_second = remaining_days_after_first - $scope.tripCompleted.second_deten
                                if (remaining_days_after_second > 0) {
                                    var second_deten_days = remaining_days_after_first - remaining_days_after_second;
                                    second_deten_value = parseInt($scope.tripCompleted.deten10) * second_deten_days;
                                } else {
                                    second_deten_value = parseInt($scope.tripCompleted.deten10) * remaining_days_after_first;
                                }
                            }


                            if (remaining_days_after_second > 0) {
                                var remaining_days_after_third = remaining_days_after_second - $scope.tripCompleted.third_deten
                                if (remaining_days_after_third > 0) {
                                    var third_remaining_days = remaining_days_after_second - remaining_days_after_third;
                                    third_remaining_value = ($scope.tripCompleted.detenN) * third_remaining_days
                                } else {
                                    third_remaining_value = ($scope.tripCompleted.detenN) * remaining_days_after_second
                                }

                            }
                            if (remaining_days_after_third > 0) {
                                remaining_days_beyond = remaining_days_after_third * ($scope.tripCompleted.detenN);
                            }
                        }
                        total_deten = first_deten_value + second_deten_value + third_remaining_value + remaining_days_beyond;
                    } else {
                        total_deten = 0;
                    }
                    var total_deten_cus = 0;
                    var first_deten_value_cus = 0;
                    var second_deten_value_cus = 0;
                    var third_remaining_value_cus = 0;
                    var remaining_days_beyond_cus = 0;
                    if (['CIPL', 'UNBR', 'FKRT'].indexOf(completedTrip.order_data.customer_code) >= 0) {
                        if (days > 0) {
                            var remaining_days_after_first_cus = days - $scope.tripCompleted.cus_deten_first;
                            if (remaining_days_after_first_cus > 0) {
                                var first_detention_days_cus = days - remaining_days_after_first_cus;
                                first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * first_detention_days_cus;

                            } else {
                                first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * days;
                            }

                            if (remaining_days_after_first_cus > 0) {
                                var remaining_days_after_second_cus = remaining_days_after_first_cus - $scope.tripCompleted.cus_deten_second
                                if (remaining_days_after_second_cus > 0) {
                                    var second_deten_days_cus = remaining_days_after_first_cus - remaining_days_after_second_cus;
                                    second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * second_deten_days_cus;
                                } else {
                                    second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * remaining_days_after_first_cus;
                                }
                            }


                            if (remaining_days_after_second_cus > 0) {
                                var remaining_days_after_third_cus = remaining_days_after_second_cus - $scope.tripCompleted.cus_deten_third
                                if (remaining_days_after_third_cus > 0) {
                                    var third_remaining_days_cus = remaining_days_after_second_cus - remaining_days_after_third_cus;
                                    third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * third_remaining_days_cus
                                } else {
                                    third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * remaining_days_after_second_cus
                                }

                            }
                            if (remaining_days_after_third_cus > 0) {
                                remaining_days_beyond_cus = remaining_days_after_third_cus * ($scope.tripCompleted.cus_detenN);
                            }
                        }

                        total_deten_cus = first_deten_value_cus + second_deten_value_cus + third_remaining_value_cus + remaining_days_beyond_cus;
                    } else {
                        total_deten_cus = 0;
                    }
                    $scope.tripCompleted.pod_data = response.data.data.pod_data;
                    if ($scope.tripCompleted.pod_data.pod_broker_data.detention === 0 || $scope.tripCompleted.pod_data.pod_broker_data.detention === '') {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = response.data.data.pod_data.pod_broker_data.detention;
                    }

                    if ($scope.tripCompleted.pod_data.pod_customer_data.detentions === 0 || $scope.tripCompleted.pod_data.pod_customer_data.detentions === '') {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = total_deten_cus;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = response.data.data.pod_data.pod_customer_data.detentions;
                    }
                    $scope.tripCompleted.tds = response.data.data.tds;

                    if ($scope.tripCompleted.tds != null) {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = $scope.tripCompleted.tds
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = '';
                    }
                    if (response.data.data.pod_data.pod_customer_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = response.data.data.pod_data.pod_customer_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.loading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = response.data.data.pod_data.pod_customer_data.loading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.surcharge === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.surcharge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.surcharge = response.data.data.pod_data.pod_customer_data.surcharge
                    }
                    if (response.data.data.pod_data.pod_customer_data.unloading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = response.data.data.pod_data.pod_customer_data.unloading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = response.data.data.pod_data.pod_customer_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = 'NONE';
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = response.data.data.pod_data.pod_customer_data.deduction_comment
                    }
                    if (response.data.data.pod_data.pod_broker_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = response.data.data.pod_data.pod_broker_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.tds === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = response.data.data.pod_data.pod_customer_data.tds
                    }
                    if (response.data.data.pod_data.pod_broker_data.loading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = response.data.data.pod_data.pod_broker_data.loading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.unloading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = response.data.data.pod_data.pod_broker_data.unloading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = response.data.data.pod_data.pod_broker_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_broker_data.b_surcharge === "" || response.data.data.pod_data.pod_broker_data.b_surcharge === undefined) {
                        $scope.tripCompleted.pod_data.pod_broker_data.b_surcharge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.b_surcharge = response.data.data.pod_data.pod_broker_data.b_surcharge
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = 'None';
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = response.data.data.pod_data.pod_broker_data.deduction_comment
                    }
                    if (response.data.data.unld_ent_pay_status === 1 || response.data.data.unld_ent_pay_status === 2) {
                        $scope.unloadReadonly = true;
                    } else {
                        $scope.unloadReadonly = false;
                    }
                    if (response.data.data.unld_ent_pay_status === 1 || response.data.data.unld_ent_pay_status === 2) {
                        $scope.loadReadonly = true;
                    } else {
                        $scope.loadReadonly = false;
                    }

                    if ($scope.tripCompleted.is_market &&
                        (!$scope.isNullValue($scope.tripCompleted.pod_data?.front_pod_pic)
                            || !$scope.isNullValue($scope.tripCompleted.pod_data?.back_pod_pic))) {
                        $scope.isMarketPODAlreadyUploaded = true;
                    } else {
                        $scope.isMarketPODAlreadyUploaded = false;
                    }

                    // If user has no permission to approve broker charges then 
                    // show message "Warning for Approval" and disable fields
                    if ($scope.isTripBrokerChargePendingForApproval($scope.tripCompleted) && !$rootScope.can_approve_broker_charges) {
                        $scope.podEditBrokerChargesState.showFieldDisabled = true;
                        $scope.podEditBrokerChargesState.showWarningMessage = true;
                    } else {
                        $scope.podEditBrokerChargesState.showFieldDisabled = false;
                        $scope.podEditBrokerChargesState.showWarningMessage = false;
                    }
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }

            }, function (err) {
                alert('Error Occurred')
            });

            $scope.changeDateTimeInPOD = function (data) {
                var oneDay = 24 * 60 * 60 * 1000;
                $scope.tripCompleted = data;
                var day1 = new Date($scope.tripCompleted.pod_trip_end_date + " " + $scope.tripCompleted.pod_trip_end_time);
                var day2 = new Date($scope.tripCompleted.pod_unloading_date + " " + $scope.tripCompleted.pod_unloading_time);
                var one_day = 1000 * 60 * 60 * 24;
                var date1_ms = day1.getTime();
                var date2_ms = day2.getTime();
                var difference_ms = date2_ms - date1_ms;
                if (difference_ms == 0) {
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay);
                } else {
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay) - 1;
                }
                var days = $scope.tripCompleted.detention_days;
                var total_deten = 0;
                var first_deten_value = 0;
                var second_deten_value = 0;
                var third_remaining_value = 0;
                var remaining_days_beyond = 0;
                if (days > 0) {
                    var remaining_days_after_first = days - $scope.tripCompleted.first_deten;
                    if (remaining_days_after_first > 0) {
                        var first_detention_days = days - remaining_days_after_first;
                        first_deten_value = parseInt($scope.tripCompleted.deten3) * first_detention_days;

                    } else {
                        first_deten_value = parseInt($scope.tripCompleted.deten3) * days;
                    }

                    if (remaining_days_after_first > 0) {
                        var remaining_days_after_second = remaining_days_after_first - $scope.tripCompleted.second_deten
                        if (remaining_days_after_second > 0) {
                            var second_deten_days = remaining_days_after_first - remaining_days_after_second;
                            second_deten_value = parseInt($scope.tripCompleted.deten10) * second_deten_days;
                        } else {
                            second_deten_value = parseInt($scope.tripCompleted.deten10) * remaining_days_after_first;
                        }
                    }
                    if (remaining_days_after_second > 0) {
                        var remaining_days_after_third = remaining_days_after_second - $scope.tripCompleted.third_deten
                        if (remaining_days_after_third > 0) {
                            var third_remaining_days = remaining_days_after_second - remaining_days_after_third;
                            third_remaining_value = ($scope.tripCompleted.detenN) * third_remaining_days
                        } else {
                            third_remaining_value = ($scope.tripCompleted.detenN) * remaining_days_after_second
                        }

                    }
                    if (remaining_days_after_third > 0) {
                        remaining_days_beyond = remaining_days_after_third * ($scope.tripCompleted.detenN);
                    }
                }
                total_deten = first_deten_value + second_deten_value + third_remaining_value + remaining_days_beyond;

                var total_deten_cus = 0;
                var first_deten_value_cus = 0;
                var second_deten_value_cus = 0;
                var third_remaining_value_cus = 0;
                var remaining_days_beyond_cus = 0;
                if (days > 0) {
                    var remaining_days_after_first_cus = days - $scope.tripCompleted.cus_deten_first;
                    if (remaining_days_after_first_cus > 0) {
                        var first_detention_days_cus = days - remaining_days_after_first_cus;
                        first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * first_detention_days_cus;

                    } else {
                        first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * days;
                    }

                    if (remaining_days_after_first_cus > 0) {
                        var remaining_days_after_second_cus = remaining_days_after_first_cus - $scope.tripCompleted.cus_deten_second
                        if (remaining_days_after_second_cus > 0) {
                            var second_deten_days_cus = remaining_days_after_first_cus - remaining_days_after_second_cus;
                            second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * second_deten_days_cus;
                        } else {
                            second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * remaining_days_after_first_cus;
                        }
                    }


                    if (remaining_days_after_second_cus > 0) {
                        var remaining_days_after_third_cus = remaining_days_after_second_cus - $scope.tripCompleted.cus_deten_third
                        if (remaining_days_after_third_cus > 0) {
                            var third_remaining_days_cus = remaining_days_after_second_cus - remaining_days_after_third_cus;
                            third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * third_remaining_days_cus
                        } else {
                            third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * remaining_days_after_second_cus
                        }

                    }
                    if (remaining_days_after_third_cus > 0) {
                        remaining_days_beyond_cus = remaining_days_after_third_cus * ($scope.tripCompleted.cus_detenN);
                    }
                }

                total_deten_cus = first_deten_value_cus + second_deten_value_cus + third_remaining_value_cus + remaining_days_beyond_cus;
                $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
                $scope.tripCompleted.pod_data.pod_customer_data.detentions = total_deten_cus;
            };

            $scope.podOwnedSave = function (tripCompleted, podOwned, lrData) {
                $scope.podsave = true;
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.completedTrip = tripCompleted;
                $scope.podDetailsForm = podOwned;
                if ($scope.podDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true;
                    $scope.podsave = false;
                } else {
                    if (tripCompleted.pod_uploaded_data.documentLink === null || tripCompleted.pod_uploaded_data.documentLink === undefined) {
                        $scope.pod_das = ''
                    } else {
                        $scope.pod_das = tripCompleted.pod_uploaded_data.documentLink
                    }
                    if (tripCompleted.pod_data.vehicle_tds_url === null || tripCompleted.pod_data.vehicle_tds_url === undefined) {
                        $scope.vehicle_tds_url = ''
                    } else {
                        $scope.vehicle_tds_url = tripCompleted.pod_data.vehicle_tds_url
                    }
                    if (tripCompleted.pod_data.other_doc_pic === null || tripCompleted.pod_data.other_doc_pic === undefined) {
                        $scope.other_doc_pic = ''
                    } else {
                        $scope.other_doc_pic = tripCompleted.pod_data.other_doc_pic
                    }
                    $scope.podSaveData = {
                        pod_unloading_date: tripCompleted.pod_unloading_date,
                        tat: tripCompleted.tat,
                        pod_unloading_time: tripCompleted.pod_unloading_time,
                        pod_trip_end_time: tripCompleted.pod_trip_end_time,
                        detention_days: tripCompleted.detention_days,
                        pod_updated_by: tripCompleted.pod_updated_by.username,
                        pod_trip_end_date: tripCompleted.pod_trip_end_date,
                        id: tripCompleted.id,
                        lr: (lrData && lrData.length > 0) ? lrData : undefined,
                        pod_data: {
                            pod_das: $scope.pod_das,
                            front_pod_pic: tripCompleted.pod_data.front_pod_pic,
                            back_pod_pic: tripCompleted.pod_data.back_pod_pic,
                            other_doc_pic: $scope.other_doc_pic,
                            vehicle_tds_url: $scope.vehicle_tds_url,
                            pod_status: 'Uploaded'
                        },
                        invoice_data: {
                            invoice_number: tripCompleted.invoice_data.invoice_number ? tripCompleted.invoice_data.invoice_number : '',
                            front_invoice: tripCompleted.invoice_data.front_invoice,
                            back_invoice: tripCompleted.invoice_data.back_invoice
                        },
                        e_way_bill_data: {
                            e_way_bill_number: tripCompleted.e_way_bill_data.e_way_bill_number,
                            e_way_bill: tripCompleted.e_way_bill_data.pic_url
                        }
                    };

                    AccountManagerServices.putOwnedPodSaveData($scope.podSaveData).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            setTimeout(function () {
                                swal({
                                    title: "Good job!",
                                    text: response.code.message,
                                    type: "success",
                                    confirmButtonText: "OK"
                                },
                                    function (isConfirm) {
                                        if (isConfirm) {
                                            $('#myTab a[data-target="#customerDetails"]').click();
                                        }
                                    });
                            }, 1000);
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                        $scope.podsave = false;
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
            };

            $scope.podMarketSave = function (tripCompleted, podMarket, lrData) {
                console.log('tripCompleted', tripCompleted);
                $scope.podsave = true;
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.completedTrip = tripCompleted;
                $scope.podDetailsForm = podMarket;
                if ($scope.podDetailsForm.$invalid == true || $scope.hasDuplicateLRNumbers()) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true;
                    $scope.podsave = false;
                } else {
                    if (tripCompleted.pod_uploaded_data.documentLink === null || tripCompleted.pod_uploaded_data.documentLink === undefined) {
                        $scope.pod_das = ''
                    } else {
                        $scope.pod_das = tripCompleted.pod_uploaded_data.documentLink
                    }
                    if (tripCompleted.pod_data.vehicle_tds_url === undefined || tripCompleted.pod_data.vehicle_tds_url === null) {
                        $scope.vehicle_tds_url = ''
                    } else {
                        $scope.vehicle_tds_url = tripCompleted.pod_data.vehicle_tds_url
                    }

                    if (tripCompleted.pod_data.other_doc_pic === undefined || tripCompleted.pod_data.other_doc_pic === null) {
                        $scope.other_doc_pic = ''
                    } else {
                        $scope.other_doc_pic = tripCompleted.pod_data.other_doc_pic
                    }

                    $scope.marketpodSaveData = {
                        pod_unloading_date: tripCompleted.pod_unloading_date,
                        tat: tripCompleted.tat,
                        pod_unloading_time: tripCompleted.pod_unloading_time,
                        pod_trip_end_time: tripCompleted.pod_trip_end_time,
                        detention_days: tripCompleted.detention_days,
                        pod_updated_by: tripCompleted.pod_updated_by.username,
                        pod_trip_end_date: tripCompleted.pod_trip_end_date,
                        id: tripCompleted.id,
                        lr_data: (lrData && lrData.length > 0) ? lrData : undefined,
                        pod_data: {
                            pod_das: $scope.pod_das,
                            vehicle_tds_url: $scope.vehicle_tds_url,
                            front_pod_pic: tripCompleted.pod_data.front_pod_pic,
                            back_pod_pic: tripCompleted.pod_data.back_pod_pic,
                            other_doc_pic: $scope.other_doc_pic,
                            pod_status: 'Uploaded'
                        },
                        invoice_data: {
                            invoice_number: tripCompleted.invoice_data.invoice_number ? tripCompleted.invoice_data.invoice_number : '',
                            front_invoice: tripCompleted.invoice_data.front_invoice,
                            back_invoice: tripCompleted.invoice_data.back_invoice
                        },
                        e_way_bill_data: {
                            e_way_bill_number: tripCompleted.e_way_bill_data.e_way_bill_number,
                            e_way_bill: tripCompleted.e_way_bill_data.pic_url
                        }
                    };
                    AccountManagerServices.putMarketPodSaveData($scope.marketpodSaveData).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            setTimeout(function () {
                                swal({
                                    title: "Good job!",
                                    text: response.code.message,
                                    type: "success",
                                    confirmButtonText: "OK"
                                },
                                    function (isConfirm) {
                                        if (isConfirm) {
                                            $('#myTab a[data-target="#customerDetails"]').click();
                                        }
                                    });
                            }, 100);
                        } else {
                            $scope.podsave = false;
                            swal("Cancelled", response.code.message, "error")
                        }
                        $scope.podsave = false;
                    }, function (err) {
                        $scope.podsave = false;
                        alert('Error Occurred')
                    })
                }
            };

            $scope.tabActiveCust = function () {
                $scope.isCustActive = '';
                $scope.isCompActive = 'active';
                $scope.isPodActive = '';
                $('#myTab a[data-target="#brokerDetails"]').click();
            };

            $scope.ownedPodEdit_disable = false;
            $scope.ownedPodEdit = function (tripCompleted, podOwned) {
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.customerDetailsForm = podOwned;
                $scope.completedTrip = tripCompleted;
                if ($scope.customerDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                } else {
                    $scope.ownedPodEdit_disable = true;
                    tripCompleted.pod_data.pod_status = 'Completed';
                    AccountManagerServices.putOwnedPodEdit(tripCompleted).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            swal("Good job!", response.code.message, "success");
                            $scope.isCustActive = '';
                            $scope.isCompActive = '';
                            $scope.isPodActive = '';
                            angular.element('#pod_Edit').modal('hide');
                            $scope.completedTripFilter($scope.pagination.current, $scope.filterData);
                            // window.location.href = "/account/#/completed-trip";
                            // window.location.reload();
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
            };


            $scope.podEdit_disable = false;
            $scope.podEdit = function (tripCompleted, brokerDetails) {
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.brokerDetailsForm = brokerDetails;
                $scope.completedTrip = tripCompleted;
                console.log('$scope.completedTrip', $scope.completedTrip);
                if ($scope.brokerDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                } else {
                    $scope.podEdit_disable = true;
                    $scope.completedTrip.pod_data.pod_status = 'Completed';
                    $scope.completedTrip.txn = $scope.txn;
                    AccountManagerServices.putPodEdit($scope.completedTrip).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            swal("Good job!", response.code.message, "success");
                            $scope.isCustActive = '';
                            $scope.isCompActive = '';
                            $scope.isPodActive = '';
                            angular.element('#pod_Edit').modal('hide');
                            $scope.completedTripFilter($scope.pagination.current, $scope.filterData);
                        } else {
                            $scope.podEdit_disable = false;
                            swal("Cancelled", response.code.message, "error")
                        }
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
                // }

            };

            $scope.podEditSendForApproval = function (tripCompleted, brokerDetails) {
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.brokerDetailsForm = brokerDetails;
                $scope.completedTrip = tripCompleted;
                if ($scope.brokerDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                } else {
                    $scope.podEdit_disable = true;
                    AccountManagerServices.putPodEditSendForApproval($scope.completedTrip).then(function (response) {
                        if (response?.data?.code?.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response?.data?.data_list?.pod_status;
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_data.pod_broker_data.broker_charge_status = response?.data?.data_list?.broker_charge_status;
                            swal("Good job!", response.data.code.message, "success");
                            $scope.isCustActive = '';
                            $scope.isCompActive = '';
                            $scope.isPodActive = '';
                            angular.element('#pod_Edit').modal('hide');
                            $scope.completedTripFilter($scope.pagination.current, $scope.filterData);
                        } else {
                            $scope.podEdit_disable = false;
                            swal("Cancelled", response?.data?.code?.message, "error")
                        }
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
            };

            //Reject Section will be shown for App uploaded POD
            $scope.showRejectPODSection = function (tripCompleted) {
                return tripCompleted.is_market &&
                    tripCompleted.pod_data.pod_status === 'Pending' &&
                    $scope.isMarketPODAlreadyUploaded &&
                    !($scope.isNullValue(tripCompleted.pod_data.front_pod_pic) &&
                        $scope.isNullValue(tripCompleted.pod_data.back_pod_pic))
            }

            $scope.showRejectedPODRemarks = function (tripCompleted) {
                return tripCompleted.is_market &&
                    tripCompleted.pod_data.pod_status === 'Rejected' &&
                    $scope.isNullValue(tripCompleted.pod_data.front_pod_pic) &&
                    $scope.isNullValue(tripCompleted.pod_data.back_pod_pic) &&
                    tripCompleted.pod_data.reject_remark;
            }
            
            $scope.rejectPOD = function (tripCompleted) {
                const { trip_code, pod_reject_remarks } = tripCompleted;
                if ($scope.isNullValue(pod_reject_remarks)) {
                    swal("Please fill Reject Remarks", "", "error");
                    return;
                }
                swal({
                    title: "Are you sure?",
                    text: "You want to reject this POD?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Yes",
                    cancelButtonText: "No",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                    function (isConfirm) {
                        if (isConfirm) {
                            AccountManagerServices.rejectPOD({ trip_code, 'remark': pod_reject_remarks }).then(function (response) {
                                if (response.data.code.status == 200) {
                                    setTimeout(function () {
                                        swal("Good job!", response.data.code.message, "success");
                                        angular.element('#pod_Edit').modal('hide');
                                        $scope.completedTripFilter($scope.pagination.current, $scope.filterData);
                                    }, 150);
                                } else {
                                    swal("Cancelled", response.code.message, "error")
                                }
                            }, function (err) {
                                alert('Error Occurred')
                            })
                        } else {
                            swal("Cancelled", "POD not rejected", "error");
                        }
                    })
            }

            $scope.getEntitiesForLoadingUnloading = function (type) {
                AccountManagerServices.getEntitylist(type).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.entity_list = response.data.entity_data;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })

            }
        };

        $scope.podDataEdit_v2 = function (completedTrip) {
            console.log('completedTrip', completedTrip);
            angular.element("input[type='file']").val(null);
            $('#myTab a[data-target="#podDetails"]').click();
            $scope.indexForUpdatePod = $scope.completedTrips_v_2.indexOf(completedTrip);
            $rootScope.indexForUpdatePodOwned = $scope.indexForUpdatePod;
            $rootScope.completedTripsOwned = $scope.completedTrips_v_2;
            $scope.v_type = completedTrip.order_data.v_type;
            $scope.tripCompleted = {};
            AccountManagerServices.gettingPod(completedTrip.id).then(function (response) {
                console.log('response_here', response);
                var lr = '';
                if (response.data.code.status == 200) {
                    var oneDay = 24 * 60 * 60 * 1000;
                    angular.forEach(response.data.data.lr.lr_data, function (value, key) {
                        if (value.lr_reciept != '') {
                            lr = value.lr_reciept + ', '
                        }
                    });
                    $scope.response = response;
                    $scope.tripCompleted.is_market = response.data.data.is_market;
                    $scope.tripCompleted.can_upload = response.data.can_upload;
                    $scope.tripCompleted.tat = response.data.data.tat;
                    $scope.tripCompleted.id = completedTrip.id;
                    $scope.tripCompleted.pod_updated_by = response.data.data.pod_updated_by;
                    $scope.tripCompleted.cus_deten3 = response.data.data.cus_deten3;
                    $scope.tripCompleted.cus_deten10 = response.data.data.cus_deten10;
                    $scope.tripCompleted.cus_detenN = response.data.data.cus_detenN;
                    $scope.tripCompleted.cus_deten_first = response.data.data.cus_deten_first;
                    $scope.tripCompleted.cus_deten_second = response.data.data.cus_deten_second;
                    $scope.tripCompleted.cus_deten_third = response.data.data.cus_deten_third;
                    $scope.tripCompleted.entity_name = response.data.data.entity_name;
                    $scope.tripCompleted.load_charge = response.data.data.load_charge;
                    $scope.tripCompleted.customer_detention_rate = 'For ' + response.data.data.cus_deten_first + ' days' + ' ' + ':' + response.data.data.cus_deten3 + ',' + '  ' + 'For ' + response.data.data.cus_deten_second + ' days' + ' ' + ':' + response.data.data.cus_deten10 + ',' + '  ' + 'Beyond ' + response.data.data.cus_deten_third + ' days' + ': ' + response.data.data.cus_detenN;
                    $scope.tripCompleted.first_deten = response.data.data.first_deten;
                    $scope.tripCompleted.second_deten = response.data.data.second_deten;
                    $scope.tripCompleted.third_deten = response.data.data.third_deten;
                    $scope.tripCompleted.broker_detention_rate = 'For ' + response.data.data.first_deten + ' days' + ' ' + ':' + response.data.data.deten3 + ',' + '  ' + 'For ' + response.data.data.second_deten + ' days' + ' ' + ':' + response.data.data.deten10 + ',' + '  ' + 'Beyond ' + response.data.data.third_deten + ' days' + ': ' + response.data.data.detenN;
                    $scope.tripCompleted.deten3 = response.data.data.deten3;
                    $scope.tripCompleted.deten10 = response.data.data.deten10;
                    $scope.tripCompleted.detenN = response.data.data.detenN;
                    $scope.tripCompleted.lr = lr;
                    if (response.data.data.pod_updated_by.mobile_no === undefined) {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username
                    } else {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username + ',' + response.data.data.pod_updated_by.mobile_no
                    }
                    if (!response.data.data.pod_trip_end_date)
                        $scope.tripCompleted.pod_trip_end_date = completedTrip.trip_end_date;
                    else
                        $scope.tripCompleted.pod_trip_end_date = $filter('date')(response.data.data.pod_trip_end_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_trip_end_time)
                        $scope.tripCompleted.pod_trip_end_time = completedTrip.trip_end_time;
                    else
                        $scope.tripCompleted.pod_trip_end_time = $filter('date')(response.data.data.pod_trip_end_time, 'HH:mm');
                    if (!response.data.data.pod_unloading_date)
                        $scope.tripCompleted.pod_unloading_date = completedTrip.unloading_date;
                    else
                        $scope.tripCompleted.pod_unloading_date = $filter('date')(response.data.data.pod_unloading_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_unloading_time)
                        $scope.tripCompleted.pod_unloading_time = completedTrip.unloading_time;
                    else
                        $scope.tripCompleted.pod_unloading_time = $filter('date')(response.data.data.pod_unloading_time, 'HH:mm');

                    var day1 = new Date($scope.tripCompleted.pod_trip_end_date + " " + $scope.tripCompleted.pod_trip_end_time)
                    var day2 = new Date($scope.tripCompleted.pod_unloading_date + " " + $scope.tripCompleted.pod_unloading_time)
                    var one_day = 1000 * 60 * 60 * 24;
                    var date1_ms = day1.getTime();
                    var date2_ms = day2.getTime();
                    var difference_ms = date2_ms - date1_ms;
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay) - 1;
                    if ($scope.tripCompleted.detention_days === -1) {
                        $scope.tripCompleted.detention_days = 0;
                    }
                    var total_deten = 0;
                    var first_deten_value = 0;
                    var second_deten_value = 0;
                    var third_remaining_value = 0;
                    var remaining_days_beyond = 0;
                    var days = $scope.tripCompleted.detention_days;
                    if (['CIPL', 'UNBR', 'FKRT'].indexOf(completedTrip.order_data.customer_code) >= 0) {
                        if (days > 0) {
                            var remaining_days_after_first = days - $scope.tripCompleted.first_deten;
                            if (remaining_days_after_first > 0) {
                                var first_detention_days = days - remaining_days_after_first;
                                first_deten_value = parseInt($scope.tripCompleted.deten3) * first_detention_days;

                            } else {
                                first_deten_value = parseInt($scope.tripCompleted.deten3) * days;
                            }

                            if (remaining_days_after_first > 0) {
                                var remaining_days_after_second = remaining_days_after_first - $scope.tripCompleted.second_deten
                                if (remaining_days_after_second > 0) {
                                    var second_deten_days = remaining_days_after_first - remaining_days_after_second;
                                    second_deten_value = parseInt($scope.tripCompleted.deten10) * second_deten_days;
                                } else {
                                    second_deten_value = parseInt($scope.tripCompleted.deten10) * remaining_days_after_first;
                                }
                            }


                            if (remaining_days_after_second > 0) {
                                var remaining_days_after_third = remaining_days_after_second - $scope.tripCompleted.third_deten
                                if (remaining_days_after_third > 0) {
                                    var third_remaining_days = remaining_days_after_second - remaining_days_after_third;
                                    third_remaining_value = ($scope.tripCompleted.detenN) * third_remaining_days
                                } else {
                                    third_remaining_value = ($scope.tripCompleted.detenN) * remaining_days_after_second
                                }

                            }
                            if (remaining_days_after_third > 0) {
                                remaining_days_beyond = remaining_days_after_third * ($scope.tripCompleted.detenN);
                            }
                        }
                        total_deten = first_deten_value + second_deten_value + third_remaining_value + remaining_days_beyond;
                    } else {
                        total_deten = 0;
                    }
                    var total_deten_cus = 0;
                    var first_deten_value_cus = 0;
                    var second_deten_value_cus = 0;
                    var third_remaining_value_cus = 0;
                    var remaining_days_beyond_cus = 0;
                    if (['CIPL', 'UNBR', 'FKRT'].indexOf(completedTrip.order_data.customer_code) >= 0) {
                        if (days > 0) {
                            var remaining_days_after_first_cus = days - $scope.tripCompleted.cus_deten_first;
                            if (remaining_days_after_first_cus > 0) {
                                var first_detention_days_cus = days - remaining_days_after_first_cus;
                                first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * first_detention_days_cus;

                            } else {
                                first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * days;
                            }

                            if (remaining_days_after_first_cus > 0) {
                                var remaining_days_after_second_cus = remaining_days_after_first_cus - $scope.tripCompleted.cus_deten_second
                                if (remaining_days_after_second_cus > 0) {
                                    var second_deten_days_cus = remaining_days_after_first_cus - remaining_days_after_second_cus;
                                    second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * second_deten_days_cus;
                                } else {
                                    second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * remaining_days_after_first_cus;
                                }
                            }


                            if (remaining_days_after_second_cus > 0) {
                                var remaining_days_after_third_cus = remaining_days_after_second_cus - $scope.tripCompleted.cus_deten_third
                                if (remaining_days_after_third_cus > 0) {
                                    var third_remaining_days_cus = remaining_days_after_second_cus - remaining_days_after_third_cus;
                                    third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * third_remaining_days_cus
                                } else {
                                    third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * remaining_days_after_second_cus
                                }

                            }
                            if (remaining_days_after_third_cus > 0) {
                                remaining_days_beyond_cus = remaining_days_after_third_cus * ($scope.tripCompleted.cus_detenN);
                            }
                        }

                        total_deten_cus = first_deten_value_cus + second_deten_value_cus + third_remaining_value_cus + remaining_days_beyond_cus;
                    } else {
                        total_deten_cus = 0;
                    }
                    $scope.tripCompleted.pod_data = response.data.data.pod_data;
                    if ($scope.tripCompleted.pod_data.pod_broker_data.detention === 0 || $scope.tripCompleted.pod_data.pod_broker_data.detention === '') {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = response.data.data.pod_data.pod_broker_data.detention;
                    }

                    if ($scope.tripCompleted.pod_data.pod_customer_data.detentions === 0 || $scope.tripCompleted.pod_data.pod_customer_data.detentions === '') {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = total_deten_cus;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = response.data.data.pod_data.pod_customer_data.detentions;
                    }
                    $scope.tripCompleted.tds = response.data.data.tds;

                    if ($scope.tripCompleted.tds != null) {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = $scope.tripCompleted.tds
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = '';
                    }
                    if (response.data.data.pod_data.pod_customer_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = response.data.data.pod_data.pod_customer_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.loading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = response.data.data.pod_data.pod_customer_data.loading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.surcharge === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.surcharge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.surcharge = response.data.data.pod_data.pod_customer_data.surcharge
                    }
                    if (response.data.data.pod_data.pod_customer_data.unloading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = response.data.data.pod_data.pod_customer_data.unloading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = response.data.data.pod_data.pod_customer_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = 'NONE';
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = response.data.data.pod_data.pod_customer_data.deduction_comment
                    }
                    if (response.data.data.pod_data.pod_broker_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = response.data.data.pod_data.pod_broker_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.tds === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = response.data.data.pod_data.pod_customer_data.tds
                    }
                    if (response.data.data.pod_data.pod_broker_data.loading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = response.data.data.pod_data.pod_broker_data.loading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.unloading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = response.data.data.pod_data.pod_broker_data.unloading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = response.data.data.pod_data.pod_broker_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_broker_data.b_surcharge === "" || response.data.data.pod_data.pod_broker_data.b_surcharge === undefined) {
                        $scope.tripCompleted.pod_data.pod_broker_data.b_surcharge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.b_surcharge = response.data.data.pod_data.pod_broker_data.b_surcharge
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = 'None';
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = response.data.data.pod_data.pod_broker_data.deduction_comment
                    }
                    if (response.data.data.unld_ent_pay_status == 1 || response.data.data.unld_ent_pay_status == 2) {
                        $scope.unloadReadonly = true;
                    } else {
                        $scope.unloadReadonly = false;
                    }
                    if (response.data.data.unld_ent_pay_status == 1 || response.data.data.unld_ent_pay_status == 2) {
                        $scope.loadReadonly = true;
                    } else {
                        $scope.loadReadonly = false;
                    }
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }

            }, function (err) {
                alert('Error Occurred')
            });

            $scope.changeDateTimeInPOD = function (data) {
                var oneDay = 24 * 60 * 60 * 1000;
                $scope.tripCompleted = data;
                var day1 = new Date($scope.tripCompleted.pod_trip_end_date + " " + $scope.tripCompleted.pod_trip_end_time);
                var day2 = new Date($scope.tripCompleted.pod_unloading_date + " " + $scope.tripCompleted.pod_unloading_time);
                var one_day = 1000 * 60 * 60 * 24;
                var date1_ms = day1.getTime();
                var date2_ms = day2.getTime();
                var difference_ms = date2_ms - date1_ms;
                if (difference_ms == 0) {
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay);
                } else {
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay) - 1;
                }
                var days = $scope.tripCompleted.detention_days;
                var total_deten = 0;
                var first_deten_value = 0;
                var second_deten_value = 0;
                var third_remaining_value = 0;
                var remaining_days_beyond = 0;
                if (days > 0) {
                    var remaining_days_after_first = days - $scope.tripCompleted.first_deten;
                    if (remaining_days_after_first > 0) {
                        var first_detention_days = days - remaining_days_after_first;
                        first_deten_value = parseInt($scope.tripCompleted.deten3) * first_detention_days;

                    } else {
                        first_deten_value = parseInt($scope.tripCompleted.deten3) * days;
                    }

                    if (remaining_days_after_first > 0) {
                        var remaining_days_after_second = remaining_days_after_first - $scope.tripCompleted.second_deten
                        if (remaining_days_after_second > 0) {
                            var second_deten_days = remaining_days_after_first - remaining_days_after_second;
                            second_deten_value = parseInt($scope.tripCompleted.deten10) * second_deten_days;
                        } else {
                            second_deten_value = parseInt($scope.tripCompleted.deten10) * remaining_days_after_first;
                        }
                    }
                    if (remaining_days_after_second > 0) {
                        var remaining_days_after_third = remaining_days_after_second - $scope.tripCompleted.third_deten
                        if (remaining_days_after_third > 0) {
                            var third_remaining_days = remaining_days_after_second - remaining_days_after_third;
                            third_remaining_value = ($scope.tripCompleted.detenN) * third_remaining_days
                        } else {
                            third_remaining_value = ($scope.tripCompleted.detenN) * remaining_days_after_second
                        }

                    }
                    if (remaining_days_after_third > 0) {
                        remaining_days_beyond = remaining_days_after_third * ($scope.tripCompleted.detenN);
                    }
                }
                total_deten = first_deten_value + second_deten_value + third_remaining_value + remaining_days_beyond;

                var total_deten_cus = 0;
                var first_deten_value_cus = 0;
                var second_deten_value_cus = 0;
                var third_remaining_value_cus = 0;
                var remaining_days_beyond_cus = 0;
                if (days > 0) {
                    var remaining_days_after_first_cus = days - $scope.tripCompleted.cus_deten_first;
                    if (remaining_days_after_first_cus > 0) {
                        var first_detention_days_cus = days - remaining_days_after_first_cus;
                        first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * first_detention_days_cus;

                    } else {
                        first_deten_value_cus = parseInt($scope.tripCompleted.cus_deten3) * days;
                    }

                    if (remaining_days_after_first_cus > 0) {
                        var remaining_days_after_second_cus = remaining_days_after_first_cus - $scope.tripCompleted.cus_deten_second
                        if (remaining_days_after_second_cus > 0) {
                            var second_deten_days_cus = remaining_days_after_first_cus - remaining_days_after_second_cus;
                            second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * second_deten_days_cus;
                        } else {
                            second_deten_value_cus = parseInt($scope.tripCompleted.cus_deten10) * remaining_days_after_first_cus;
                        }
                    }


                    if (remaining_days_after_second_cus > 0) {
                        var remaining_days_after_third_cus = remaining_days_after_second_cus - $scope.tripCompleted.cus_deten_third
                        if (remaining_days_after_third_cus > 0) {
                            var third_remaining_days_cus = remaining_days_after_second_cus - remaining_days_after_third_cus;
                            third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * third_remaining_days_cus
                        } else {
                            third_remaining_value_cus = ($scope.tripCompleted.cus_detenN) * remaining_days_after_second_cus
                        }

                    }
                    if (remaining_days_after_third_cus > 0) {
                        remaining_days_beyond_cus = remaining_days_after_third_cus * ($scope.tripCompleted.cus_detenN);
                    }
                }

                total_deten_cus = first_deten_value_cus + second_deten_value_cus + third_remaining_value_cus + remaining_days_beyond_cus;
                $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
                $scope.tripCompleted.pod_data.pod_customer_data.detentions = total_deten_cus;
            };

            $scope.podOwnedSave = function (tripCompleted, podOwned) {
                $scope.podsave = true;
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.completedTrip = tripCompleted;
                $scope.podDetailsForm = podOwned;
                if ($scope.podDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true;
                    $scope.podsave = false;
                } else {
                    if (tripCompleted.pod_uploaded_data.documentLink === null || tripCompleted.pod_uploaded_data.documentLink === undefined) {
                        $scope.pod_das = ''
                    } else {
                        $scope.pod_das = tripCompleted.pod_uploaded_data.documentLink
                    }

                    if (tripCompleted.pod_data.vehicle_tds_url === null || tripCompleted.pod_data.vehicle_tds_url === undefined) {
                        $scope.vehicle_tds_url = ''
                    } else {
                        $scope.vehicle_tds_url = tripCompleted.pod_data.vehicle_tds_url
                    }
                    if (tripCompleted.pod_data.other_doc_pic === null || tripCompleted.pod_data.other_doc_pic === undefined) {
                        $scope.other_doc_pic = ''
                    } else {
                        $scope.other_doc_pic = tripCompleted.pod_data.other_doc_pic
                    }
                    $scope.podSaveData = {
                        pod_unloading_date: tripCompleted.pod_unloading_date,
                        tat: tripCompleted.tat,
                        pod_unloading_time: tripCompleted.pod_unloading_time,
                        pod_trip_end_time: tripCompleted.pod_trip_end_time,
                        detention_days: tripCompleted.detention_days,
                        pod_updated_by: tripCompleted.pod_updated_by.username,
                        pod_trip_end_date: tripCompleted.pod_trip_end_date,
                        id: tripCompleted.id,
                        lr: tripCompleted.lr,
                        pod_data: {
                            pod_das: $scope.pod_das,
                            front_pod_pic: tripCompleted.pod_data.front_pod_pic,
                            back_pod_pic: tripCompleted.pod_data.back_pod_pic,
                            other_doc_pic: $scope.other_doc_pic,
                            vehicle_tds_url: $scope.vehicle_tds_url,
                            pod_status: 'Uploaded'
                        }
                    };

                    AccountManagerServices.putOwnedPodSaveData($scope.podSaveData).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            setTimeout(function () {
                                swal({
                                    title: "Good job!",
                                    text: response.code.message,
                                    type: "success",
                                    confirmButtonText: "OK"
                                },
                                    function (isConfirm) {
                                        if (isConfirm) {
                                            $('#myTab a[data-target="#customerDetails"]').click();
                                        }
                                    });
                            }, 1000);
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                        $scope.podsave = false;
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
            };

            $scope.podMarketSave = function (tripCompleted, podMarket) {
                console.log('tripCompleted', tripCompleted);
                $scope.podsave = true;
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.completedTrip = tripCompleted;
                $scope.podDetailsForm = podMarket;
                if ($scope.podDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true;
                    $scope.podsave = false;
                } else {
                    if (tripCompleted.pod_uploaded_data.documentLink === null || tripCompleted.pod_uploaded_data.documentLink === undefined) {
                        $scope.pod_das = ''
                    } else {
                        $scope.pod_das = tripCompleted.pod_uploaded_data.documentLink
                    }
                    if (tripCompleted.pod_data.vehicle_tds_url === undefined || tripCompleted.pod_data.vehicle_tds_url === null) {
                        $scope.vehicle_tds_url = ''
                    } else {
                        $scope.vehicle_tds_url = tripCompleted.pod_data.vehicle_tds_url
                    }

                    if (tripCompleted.pod_data.other_doc_pic === undefined || tripCompleted.pod_data.other_doc_pic === null) {
                        $scope.other_doc_pic = ''
                    } else {
                        $scope.other_doc_pic = tripCompleted.pod_data.other_doc_pic
                    }

                    $scope.marketpodSaveData = {
                        pod_unloading_date: tripCompleted.pod_unloading_date,
                        tat: tripCompleted.tat,
                        pod_unloading_time: tripCompleted.pod_unloading_time,
                        pod_trip_end_time: tripCompleted.pod_trip_end_time,
                        detention_days: tripCompleted.detention_days,
                        pod_updated_by: tripCompleted.pod_updated_by.username,
                        pod_trip_end_date: tripCompleted.pod_trip_end_date,
                        id: tripCompleted.id,
                        lr: tripCompleted.lr,
                        pod_data: {
                            pod_das: $scope.pod_das,
                            vehicle_tds_url: $scope.vehicle_tds_url,
                            front_pod_pic: tripCompleted.pod_data.front_pod_pic,
                            back_pod_pic: tripCompleted.pod_data.back_pod_pic,
                            other_doc_pic: $scope.other_doc_pic,
                            pod_status: 'Uploaded'
                        }
                    };
                    AccountManagerServices.putMarketPodSaveData($scope.marketpodSaveData).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.podsave = false;
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            setTimeout(function () {
                                swal({
                                    title: "Good job!",
                                    text: response.code.message,
                                    type: "success",
                                    confirmButtonText: "OK"
                                },
                                    function (isConfirm) {
                                        if (isConfirm) {
                                            $('#myTab a[data-target="#customerDetails"]').click();
                                        }
                                    });
                            }, 100);
                        } else {
                            $scope.podsave = false;
                            swal("Cancelled", response.code.message, "error")
                        }
                    }, function (err) {
                        $scope.podsave = false;
                        alert('Error Occurred')
                    })
                }
            };

            $scope.tabActiveCust = function () {
                $scope.isCustActive = '';
                $scope.isCompActive = 'active';
                $scope.isPodActive = '';
                $('#myTab a[data-target="#brokerDetails"]').click();
            };

            $scope.ownedPodEdit_disable = false;
            $scope.ownedPodEdit = function (tripCompleted, podOwned) {
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.customerDetailsForm = podOwned;
                $scope.completedTrip = tripCompleted;
                if ($scope.customerDetailsForm.$invalid == true) {
                    swal("Please fill all required fields.", "", "error");
                    $scope.submitted = true
                } else {
                    $scope.ownedPodEdit_disable = true;
                    tripCompleted.pod_data.pod_status = 'Completed';
                    AccountManagerServices.putOwnedPodEdit(tripCompleted).then(function (response) {
                        if (response.code.status == 200) {
                            $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                            swal("Good job!", response.code.message, "success");
                            $scope.isCustActive = '';
                            $scope.isCompActive = '';
                            $scope.isPodActive = '';
                            angular.element('#pod_Edit').modal('hide');
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    }, function (err) {
                        alert('Error Occurred')
                    })
                }
            };




            $scope.podEdit_disable = false;
            $scope.podEdit = function (tripCompleted, brokerDetails) {
                $scope.indexForUpdatePodOwned = $rootScope.indexForUpdatePodOwned;
                $scope.completedTrips = $rootScope.completedTripsOwned;
                $scope.brokerDetailsForm = brokerDetails;
                $scope.completedTrip = tripCompleted;
                console.log('$scope.completedTrip', $scope.completedTrip);
                if ($scope.completedTrip.pod_data.pod_broker_data.detention < 25000) {
                    $scope.completedTrip.otp = null;
                }
                // Temporary added condition for pay  via entity for loading charges

                if ($scope.completedTrip.pod_data.payVialoadEntity === undefined || $scope.completedTrip.pod_data.payVialoadEntity === null) {
                    $scope.completedTrip.pod_data.payVialoadEntity = false;
                    swal("Loading charges should be paid by Entity only !", "", "error");
                } else {
                    $scope.completedTrip.pod_data.payVialoadEntity = tripCompleted.pod_data.payVialoadEntity;
                    if ($scope.brokerDetailsForm.$invalid == true) {
                        swal("Please fill all required fields.", "", "error");
                        $scope.submitted = true
                    } else {
                        $scope.podEdit_disable = true;
                        $scope.completedTrip.pod_data.pod_status = 'Completed';
                        $scope.completedTrip.txn = $scope.txn;

                        if ($scope.completedTrip.pod_data.payViaUnloadEntity === undefined || $scope.completedTrip.pod_data.payViaUnloadEntity === null) {
                            $scope.completedTrip.pod_data.payViaUnloadEntity = false;
                        } else {
                            $scope.completedTrip.pod_data.payViaUnloadEntity = tripCompleted.pod_data.payViaUnloadEntity;
                        }
                        AccountManagerServices.putPodEdit($scope.completedTrip).then(function (response) {
                            if (response.code.status == 200) {
                                $scope.completedTrips[$scope.indexForUpdatePodOwned].pod_status = response.data_list;
                                swal("Good job!", response.code.message, "success");
                                $scope.isCustActive = '';
                                $scope.isCompActive = '';
                                $scope.isPodActive = '';
                                angular.element('#pod_Edit').modal('hide');
                            } else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            alert('Error Occurred')
                        })
                    }
                }

            };

            $scope.getEntitiesForLoadingUnloading = function (type) {
                AccountManagerServices.getEntitylist(type).then(function (response) {
                    if (response.data.code.status == 200) {
                        $scope.entity_list = response.data.entity_data;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })

            }
        };

        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };
        $scope.i = {
            'spinner': false
        };
        $scope.downLoadZipPod = function (i) {
            i.spinner = true;
            var name = i.order_code + '_' + 'pod';
            AccountManagerServices.downloadZip(i).then(function (response) {
                i.spinner = false;
                console.log('response', response)
                var a = document.createElement('a');
                var blob = new Blob([response], {
                    'type': "application/zip"
                });
                a.href = URL.createObjectURL(blob);
                a.download = name;
                a.click();
            })
        };

        $scope.podToKredX = function (i) {
            var pod_data = {
                'pod_data': '',
                'order_code': '',
                'trip_code': ''
            };
            pod_data['pod_data'] = i.pod_data;
            pod_data['order_code'] = i.order_code;
            pod_data['trip_code'] = i.trip_code;
            console.log('THE_data', pod_data);
            swal({
                title: "Are you sure?",
                text: "You want to send POD to KredX",
                type: "warning",
                showCancelButton: true,
                showLoaderOnConfirm: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: false,
                closeOnCancel: false
            }, function (isConfirm) {
                if (isConfirm) {
                    AccountManagerServices.postPOD(pod_data).then(function (response) {
                        console.log('postPOD', response);
                        if (response.data.code.status == 202) {
                            swal("Good job!", response.data.code.message, "success");
                        } else {
                            swal("Cancelled", response.data.code.message, "error")
                        }
                    }, function (err) {
                        alert('Error Occurred')
                    })
                } else {
                    swal("Cancelled", "Not Sent.", "error");
                }
            })
        };

        $scope.statusKredX = function (trip_id) {
            AccountManagerServices.getStatusKredX(trip_id).then(function (response) {
                console.log('postPOD', response);
                if (response.data.code.status == 200) {
                    swal("Good job!", response.data.code.message, "success");
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.podFrontPicture = function (pod_pic) {
            console.log('pod_pic', pod_pic);
            $scope.showPodFrontPic = null;
            if (!pod_pic) {
                setTimeout(function() {
                    $('#pod_front').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(pod_pic)) {
                $scope.showPodFrontPic = pod_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(pod_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showPodFrontPic = response.data.url;
                    } else {
                        $('#pod_front').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showPodFrontPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + pod_pic
            }

        };

        $scope.showLRReceiptPic = function(lrPic) {
            $scope.showLRPic = null;
            if (!lrPic) {
                setTimeout(function() {
                    $('#lr_no_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(lrPic)) {
                $scope.showLRPic = lrPic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(lrPic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showLRPic = response.data.url;
                    } else {
                        $('#lr_no_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showLRPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lrPic
            }

        }
        // View POD
        $scope.viewPod = function (documentLink) {
            $scope.showPodFrontPic = documentLink;
        }

        // Delete POD from DAS
        $scope.deleteUploadedPod = function (entity_id) {
            AccountManagerServices.deleteUploadedPod(entity_id).then(function (response) {
                console.log('deleteUploadedPod_response', response);
                if (response.data.code.status == 200) {
                    swal("Good job!", response.data.code.message, "success");
                    $scope.tripCompleted.pod_uploaded_data.documentLink = null;
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            });
        }

        $scope.showPodBackPicture = function (pod_back_pic) {
            $scope.showPodBackPic = null;
            if (!pod_back_pic) {
                setTimeout(function() {
                    $('#pod_back').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(pod_back_pic)) {
                $scope.showPodBackPic = pod_back_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(pod_back_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showPodBackPic = response.data.url;
                    } else {
                        $('#pod_back').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showPodBackPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + pod_back_pic
            }

        };
        $scope.showOtherDocPicture = function (other_doc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(other_doc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showOtherDocPic = response.data.url;
                });
            } else {
                $scope.showOtherDocPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + other_doc_pic
            }

        };
        $scope.showTDSCertificatePicture = function (tds_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(tds_pic).then(function (response) {

                    // console.log(response)
                    $scope.showTdsCertificatePic = response.data.url;
                });
            } else {
                $scope.showTdsCertificatePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + tds_pic
            }

        };

        $scope.calculateDays = function (repot_dat, repot_time, upload_dat, upload_time) {
            var day1 = new Date(repot_dat + " " + repot_time)
            var day2 = new Date(upload_dat + " " + upload_time)
            var one_day = 1000 * 60 * 60 * 24;
            var date1_ms = day1.getTime();
            var date2_ms = day2.getTime();
            var difference_ms = date2_ms - date1_ms;
            var days = Math.round(difference_ms / one_day);
            $scope.tripCompleted.detention_days = days;
            console.log('numbers of days', days)
            console.log($scope.response)
            var days = $scope.tripCompleted.detention_days;
            var total_deten = 0;
            for (var i = 1; i <= days; i++) {
                if (i <= 3) {
                    total_deten += parseInt($scope.response.data.data.deten3);
                } else if (i <= 10) {
                    total_deten += parseInt($scope.response.data.data.deten10);
                } else if (i > 10) {
                    total_deten += parseInt($scope.response.data.data.detenN);
                }
            }
            $scope.tripCompleted.broker_detention_rate = total_deten;
            $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
        };

        /**
         * $scope.editTripTimes
         * By: Prafull
         * @param completedTrip
         */

        $scope.editTripTimes = function (completedTrip) {
            var indexForUpdateTripTiming = $scope.completedTrips.indexOf(completedTrip);
            $rootScope.completedTripsOwned = $scope.completedTrips;
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: '../static/apps/gobolt_opc/account_manager/template/trip/trip_time_change.html',
                controller: [
                    '$scope', '$modalInstance',
                    function ($scope, $modalInstance) {
                        $scope.dataForTimeUpdate = {
                            'trip_id': completedTrip.id,
                            'vehiclePlacementDate': completedTrip.vehicle_placement_date,
                            'vehiclePlacementTime': completedTrip.vehicle_placement_time.slice(0, 5),
                            'tripStartDate': completedTrip.trip_start_date.split('T')[0],
                            'tripStartTime': (completedTrip.trip_start_time.split('T')[1]).slice(0, 5),
                            'tripEndDate': completedTrip.trip_end_date,
                            'tripEndTime': completedTrip.trip_end_time,
                            'vehicleUnloadingDate': completedTrip.unloading_date,
                            'vehicleUnloadingTime': completedTrip.unloading_time
                        };
                        $scope.changeTripTime = function (dataForUpdate) {
                            if ((dataForUpdate.vehiclePlacementTime).length < 6) {
                                var placementTime = dataForUpdate.vehiclePlacementTime + ':00'
                            } else {
                                placementTime = dataForUpdate.vehiclePlacementTime
                            }
                            if ((dataForUpdate.tripEndTime).length < 6) {
                                var tripEndTime = dataForUpdate.tripEndTime + ':00'
                            } else {
                                tripEndTime = dataForUpdate.tripEndTime
                            }
                            if ((dataForUpdate.vehicleUnloadingTime).length < 6) {
                                var unloadingTime = dataForUpdate.vehicleUnloadingTime + ':00'
                            } else {
                                unloadingTime = dataForUpdate.vehicleUnloadingTime
                            }
                            var updatedTripTimeData = {
                                'trip_id': dataForUpdate.trip_id,
                                'vehiclePlacementDate': dataForUpdate.vehiclePlacementDate,
                                'vehiclePlacementTime': placementTime,
                                'tripStartDate': dataForUpdate.tripStartDate,
                                'tripStartTime': dataForUpdate.tripStartTime.slice(0, 5),
                                'tripEndDate': dataForUpdate.tripEndDate,
                                'tripEndTime': tripEndTime,
                                'vehicleUnloadingDate': dataForUpdate.vehicleUnloadingDate,
                                'vehicleUnloadingTime': unloadingTime
                            };
                            $scope.completedTrips = $rootScope.completedTripsOwned;
                            swal({
                                title: "Are you sure?",
                                text: "You want to make changes in Trip ",
                                type: "warning",
                                showCancelButton: true,
                                showLoaderOnConfirm: true,
                                confirmButtonColor: "#DD6B55",
                                confirmButtonText: "Yes, update it!",
                                cancelButtonText: "No, cancel !",
                                closeOnConfirm: false,
                                closeOnCancel: false
                            }, function (isConfirm) {
                                if (isConfirm) {
                                    setTimeout(function () {
                                        AccountManagerServices.editTimesTrip(updatedTripTimeData).then(function (response) {
                                            console.log('rs', response)
                                            if (response.code.status == 200) {
                                                swal("Good job!", response.code.message, "success");
                                                $scope.completedTrips[indexForUpdateTripTiming] = response.res_data;
                                                $scope.cancel();
                                                //$('#pod').modal('hide')
                                            } else {
                                                swal("Cancelled", response.code.message, "error")
                                            }
                                        }, function (err) {
                                            alert('Error Occurred')
                                        })
                                    }, 500)
                                } else {
                                    swal("Cancelled", "Trip details not updated:)", "error");
                                }
                            })
                        };
                        $scope.cancel = function () {
                            $modalInstance.close();
                        };

                    }
                ],
                size: 'lg'
            })
        };

        $scope.reviewCharges = function (completedTrip) {
            $rootScope.charges = {
                'toll': 0,
                'unloading': 0,
                'detention': 0,
                'trip_code': completedTrip.trip_code,
                'material_tax': 0.0
            };
            console.log('completedTrip', completedTrip);
            var indexForUpdateTripTiming = $scope.completedTrips.indexOf(completedTrip);
            $rootScope.completedTripsOwned = $scope.completedTrips;
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: '../static/apps/gobolt_opc/account_manager/template/trip/review_charges.html',
                controller: [
                    '$scope', '$modalInstance',
                    function ($scope, $modalInstance) {
                        $scope.chargesUpdation = function (charges, chargesForm) {
                            $scope.chargesForm = chargesForm;
                            if (!$scope.chargesForm.$invalid) {
                                AccountManagerServices.chargeUpdateService(charges).then(function (response) {
                                    console.log('rs_here', response);
                                    if (response.data.code.status == 200) {
                                        swal("Good job!", response.data.code.message, "success");
                                        //$('#review_charge').modal('hide')
                                        modalInstance.close();
                                    } else {
                                        swal("Cancelled", response.data.code.message, "error")
                                    }
                                }, function (err) {
                                    alert('Error Occurred')
                                });
                            } else {
                                swal("Still can't be negative!", "", "error");
                                $scope.submitted = true;
                            }
                        };
                        $scope.cancel = function () {
                            $modalInstance.close();
                        };
                    }
                ],
                size: 'lg'
            })
        };

        $scope.gatePass = function () {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: '../static/apps/gobolt_opc/account_manager/template/trip/gate_pass.html',
                controller: [
                    '$scope', '$modalInstance',
                    function ($scope, $modalInstance) {
                        $scope.gatePassUpdation = function (file) {
                            if (file == undefined) {
                                swal("oops!", 'Select a file first!', "warning");
                                return;
                            }
                            AccountManagerServices.gatePassUpdateService(file).then(function (response) {
                                console.log('resp_here', response);
                                if (response.data.code.status == 200) {
                                    if (response.data.leftover) {
                                        swal("Done Except", response.data.leftover, "success");
                                    } else {
                                        swal("Good job!", response.data.code.message, "success");
                                    }
                                } else {
                                    swal("Cancelled", response.data.code.message, "error")
                                }
                            }, function (err) {
                                alert('Error Occurred')
                            })
                        };
                        $scope.cancel = function () {
                            $modalInstance.close();
                        };
                    }
                ],
                size: 'lg'
            })
        };

        $scope.getCharges = function (trip_code) {
            AccountManagerServices.getTripCharges(trip_code).then(function (response) {
                console.log('charges_response', response);
                if (response.data.code.status == 200) {
                    $rootScope.charges.toll = response.data.charges.toll;
                    $rootScope.charges.detention = response.data.charges.detention;
                    $rootScope.charges.unloading = response.data.charges.unloading;
                    $rootScope.charges.material_tax = response.data.charges.material_tax;
                }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        /******************************* LR Edit for account *******************************************/

        $scope.getOwnLrDetails = function (orderAssign) {
            $scope.indexForLrEdit = $scope.completedTrips.indexOf(orderAssign);
            $rootScope.abc = $scope.indexForLrEdit;
            $rootScope.completedTrips = $scope.completedTrips;


            var modalInstance = $modal.open({
                animation: true,
                templateUrl: '../static/apps/gobolt_opc/account_manager/template/trip/completed_lr_update.html',
                controller: [
                    '$scope', '$modalInstance',
                    function ($scope, $modalInstance) {
                        $scope.is_market = orderAssign.is_market;
                        $scope.completedTrips = $rootScope.completedTrips
                        $scope.lrDataVehicle = {
                            'driver_photo_vehicle': '',
                            'seal_no': '',
                            'trip_id': ''
                        };
                        $scope.lrInfo = [{
                            lr_reciept: '',
                            pic_url: ''
                        }];
                        AccountManagerServices.getlrData(orderAssign.id).then(function (response) {
                            if (response.data.code.status == 200) {
                                $scope.lrInfo = response.data.data_list.lr;
                                $scope.lrDataVehicle.driver_photo_vehicle = response.data.data_list.driver_photo_vehicle;
                                $scope.lrDataVehicle.seal_no = response.data.data_list.seal_no;
                                $scope.lrDataVehicle.trip_id = response.data.data_list.trip_id;
                                $scope.lrDataVehicle.front_invoice = response.data.data_list.front_invoice;
                                $scope.lrDataVehicle.back_invoice = response.data.data_list.front_invoice;
                            } else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                        $scope.lrInfoDetails = function (lrinfo, formType, data, lrEdit) {
                            $scope.lrEdit = lrEdit;
                            if ($scope.lrEdit.$invalid == true && formType == 4) {

                            } else {
                                spinnerService.show('html5spinner');
                                $scope.lrInfoData = {
                                    'lrinfoData': lrinfo,
                                    'formType': formType,
                                    'dataVehicle': data
                                };
                            }
                        };

                        var wrapper = $(".input_fields_wrap");
                        $scope.lrAdditionalInfo = function () {
                            if ($scope.lrInfo.length >= 1) {
                                $scope.active = true
                            }
                            var newItemNo = $scope.lrInfo.length + 1;
                            $scope.lrInfo.push({
                                lr_reciept: '',
                                pic_url: ''
                            });
                        };

                        $scope.removeAddInfo = function (index) {
                            $scope.idx = 'remove_' + index;
                            $scope.del_lr = 'remove_' + 1;
                            if ($scope.idx === $scope.del_lr) {
                                $scope.active = false
                            }
                            //$scope.active = false
                            var lastItem = $scope.lrInfo.length - 1;
                            $scope.lrInfo.splice(lastItem);
                        };

                        $scope.cancel = function () {
                            $modalInstance.close();
                        };

                        $scope.uploadLrdetails = function (lrinfo, formType, data, lrEdit) {
                            $rootScope.updateLr = true;
                            $scope.lrEdit = lrEdit;
                            if ($scope.lrEdit.$invalid == true) {

                            } else {
                                spinnerService.show('html5spinner');
                                $scope.lrInfoData = {
                                    'lrinfoData': lrinfo,
                                    'formType': formType,
                                    'dataVehicle': data,
                                    'trip_id': $scope.lrDataVehicle.trip_id
                                };
                                AccountManagerServices.updateLR($scope.lrInfoData).then(function (response) {
                                    if (response.data.code.status === 200) {
                                        $timeout(function () {
                                            spinnerService.hide('html5spinner');
                                            swal("LR is sucessfully updated.", "", "success")
                                            $scope.completedTrips[$rootScope.abc].lr.lr_data = response.data.lr.lr_data;
                                            $scope.completedTrips[$rootScope.abc].invoice_data.front_invoice = response.data.invoice_data.front_invoice;
                                            $scope.completedTrips[$rootScope.abc].invoice_data.back_invoice = response.data.invoice_data.back_invoice;
                                            $('#uploadLrOngoing').modal('hide')
                                            $modalInstance.close();

                                        })
                                    } else {
                                        $scope.loading = false;
                                        swal("Oops!", response.data.code.message, "error");
                                        spinnerService.hide('html5spinner');
                                    }
                                },
                                    function (error) {
                                        alert('Error Occurred')
                                        $scope.loading = false;
                                        swal("Oops!", "Something went wrong.", "error");
                                    }
                                )
                            }
                        };
                    }
                ],
                size: 'lg'
            })
        };


        $scope.driverDetailsAccount = function (orderAssign) {
            AccountManagerServices.driverDataAccount(orderAssign.id).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.driver = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };

        $scope.vehicleDetailAccount = function (vehicleData) {
            AccountManagerServices.vehicleDataAccount(vehicleData.id).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.vehicle = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (error) {
                alert('Error Occurred')
            })
        };

        /**
         *Filter
         *Prafull
         */

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([
                function (callback) {
                    // request no 1
                    // AccountManagerServices.getAllLrLists().then(function (response) {
                    //     if (response.data.results.code.status === 200) {
                    //         $scope.lrName = response.data.results.data_list;
                    //         callback();
                    //     } else {
                    //         alert(response.data.results.code.message)
                    //     }
                    // }, function (err) {
                    //     swal("Oops", 'No internet connection.', "error")
                    // });
                },

                //function (callback) {
                //    //request no 2
                //    AccountManagerServices.getAllCustomerLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.customerName = response.data.customer;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},
                //
                //function (callback) {
                //    //request no 3
                //    AccountManagerServices.getAllLocationLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.locationNmae = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},
                //
                //function (callback) {
                //    //request no 4
                //    AccountManagerServices.getAllBrokerList().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.brokerName = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //},

                //function (callback) {
                //    //request no 5
                //    AccountManagerServices.getAllVehicleLists().then(function (response) {
                //        if (response.data.code.status === 200) {
                //            $scope.vehicleNo = response.data.data_list;
                //            callback();
                //        } else {
                //            alert(response.data.code.message)
                //        }
                //    }, function (err) {
                //        swal("Oops", 'No internet connection.', "error")
                //    });
                //}
            ])
        };

        //$scope.asyncRequestForFilter();
        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            AccountManagerServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };

        $scope.getCustomerListNewAcc = function () {
            $scope.customerName = [];
            AccountManagerServices.getAllCustomerListsNew().then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.customerName = response.data.customer;
                } else {
                    if (response.data.code.status === 100) {
                        angular.forEach(response.data.customer, function (value, key) {
                            $scope.customerName.push(value._source)
                        });
                    } else {
                        swal("Oops", response.data.code.message, "error");
                    }
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', "error")
            })
        };

        $scope.getLocationNewAcc = function (customerName) {
            $scope.filterData.origin = null;
            $scope.filterData.destination = null;
            $scope.origin = [];
            $scope.destination = [];

            AccountManagerServices.getLocationListsNew(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.locationNmae = response.data.location;
                } else {
                    if (response.data.code.status === 100) {
                        $scope.y = response.data.location;
                        angular.forEach(response.data.com.origin_data, function (value, key) {
                            $scope.origin.push({
                                'location_name': value
                            });
                        });
                        angular.forEach(response.data.com.destination_data, function (value, key) {
                            $scope.destination.push({
                                'location_name': value
                            });
                        });
                    } else {
                        swal("Oops", response.data.code.message, "error");
                    }
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };

        $scope.getBrokerNewAcc = function (customer) {
            $scope.filterData = {
                vehicle_type: '',
                customer: customer,
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: $scope.filterData.date_vehicle_required,
                to_date_vehicle_required: $scope.filterData.to_date_vehicle_required,
                s_from_date: '',
                s_to_date: '',
                monthRange: $scope.filterData.monthRange

            };
            $scope.brokerName = [];
            var id = {
                'customer_id': null
            };
            if (customer) {
                id['customer_id'] = customer.id;
            }
            AccountManagerServices.getAllBrokerListNew(id).then(function (response) {
                console.log('broker_list', response);
                if (response.data.code.status === 100) {
                    $scope.brokerName = response.data.data_list;
                    if (customer) {
                        var newArr = [];
                        $scope.brokx = response.data.data_list;
                        angular.forEach($scope.brokerName, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value._source.broker_data.broker_code, val2._source.broker_data.broker_code)) {
                                        exists = true
                                    }
                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            } else {
                                newArr.push(value);
                            }
                        });
                        $scope.brokerName = newArr;
                    }
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };

        $scope.checkIfCustAcc = function (data) {
            if (data) { } else {
                swal("Oops", 'Please select customer First.', "warning")
            }
        };

        $scope.$watch('filterData.customer', function(newValue) {
            if (newValue === null || newValue === undefined) {
              $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.date_vehicle_required', function() {
            if ($scope.filterData.date_vehicle_required) {
                $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.to_date_vehicle_required', function() {
            if ($scope.filterData.to_date_vehicle_required) {
                $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.monthRange', function() {
            if ($scope.filterData.monthRange && $scope.filterData.monthRange.monthIntervalValue) {
                $scope.filterData.date_vehicle_required = undefined;
                $scope.filterData.to_date_vehicle_required = undefined;
            }
        });

        $scope.destinationBasedOriginAcc = function (data) {
            $scope.destination = [];
            if (data === undefined || data === null) {
                $scope.filterData.destination = null;
            }
            if (data !== undefined && data !== null) {
                angular.forEach($scope.y, function (value, key) {
                    if (data.location_name === value.origin_data.location_name) {
                        $scope.destination.push({
                            'location_name': value.destination_data.location_name
                        })
                    }
                });
            } else {
                angular.forEach($scope.y, function (value, key) {
                    $scope.destination.push({
                        'location_name': value.destination_data.location_name
                    });
                });
            }
            var newArr = [];
            angular.forEach($scope.destination, function (value, key) {
                var exists = false;
                if (newArr.length) {
                    angular.forEach(newArr, function (val2, key) {
                        if (angular.equals(value.location_name, val2.location_name)) {
                            exists = true
                        }
                    });
                    if (exists === false) {
                        newArr.push(value);
                    }
                } else {
                    newArr.push(value);
                }

            });
            $scope.destination = newArr;
        };

        $scope.oDBasedBroker = function (customer) {
            var newArr = [];
            if ($scope.filterData.origin && $scope.filterData.destination) {
                angular.forEach($scope.brokx, function (value, key) {
                    if ((angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) &&
                        (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name))) {
                        newArr.push(value);
                    }
                });
            } else if ($scope.filterData.origin) {
                angular.forEach($scope.brokx, function (value, key) {
                    if (angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) {
                        newArr.push(value);
                    }
                });

            } else if ($scope.filterData.destination) {
                angular.forEach($scope.brokx, function (value, key) {
                    if (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name)) {
                        newArr.push(value);
                    }
                });
            } else if (!$scope.filterData.origin && !$scope.filterData.destination) {
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    date_vehicle_required: $scope.filterData.date_vehicle_required,
                    to_date_vehicle_required: $scope.filterData.to_date_vehicle_required,
                    s_from_date: '',
                    s_to_date: '',
                    monthRange:  $scope.filterData.monthRange

                };
                $scope.brokerName = [];
                angular.forEach($scope.brokx, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value._source.broker_data.broker_code, val2._source.broker_data.broker_code)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    } else {
                        newArr.push(value);
                    }
                });
            }
            $scope.brokerName = newArr;
        };

        $scope.oDBasedVehNo = function (customer) {
            //console.log('filterdata', $scope.filterData);
            var newArr = [];
            if ($scope.filterData.origin && $scope.filterData.destination) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if ((angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) &&
                        (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name))) {
                        newArr.push(value);
                    }
                });
            } else if ($scope.filterData.origin) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if (angular.equals(value._source.location_data.origin_data.location_name, $scope.filterData.origin.location_name)) {
                        newArr.push(value);
                    }
                });

            } else if ($scope.filterData.destination) {
                angular.forEach($scope.vehnox, function (value, key) {
                    if (angular.equals(value._source.location_data.destination_data.location_name, $scope.filterData.destination.location_name)) {
                        newArr.push(value);
                    }
                });
            } else if (!$scope.filterData.origin && !$scope.filterData.destination) {
                $scope.filterData = {
                    vehicle_type: '',
                    customer: customer,
                    origin: '',
                    destination: '',
                    broker: '',
                    date_vehicle_required: $scope.filterData.date_vehicle_required,
                    to_date_vehicle_required: $scope.filterData.to_date_vehicle_required,
                    s_from_date: '',
                    s_to_date: '',
                    monthRange:  $scope.filterData.monthRange

                };
                $scope.vehicleNo = [];
                angular.forEach($scope.vehnox, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    } else {
                        newArr.push(value);
                    }
                });
            }
            $scope.vehicleNo = newArr;
            //console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
        };

        $scope.destinationLocationAcc = function (data, data_1) {
            if (data === undefined || data === null) {
                $scope.filterData.origin = null;
                $scope.origin = [];
                if (data_1 !== undefined && data_1 !== null) {
                    angular.forEach($scope.y, function (value, key) {
                        if (data_1.location_name === value.destination_data.location_name) {
                            $scope.origin.push({
                                'location_name': value.origin_data.location_name
                            })
                        }
                    });
                }

                if (data_1 === undefined || data_1 == null) {
                    $scope.filterData.origin = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.origin.push({
                            'location_name': value.origin_data.location_name
                        });
                    });
                }
                var newArr = [];
                angular.forEach($scope.origin, function (value, key) {
                    var exists = false;
                    if (newArr.length) {
                        angular.forEach(newArr, function (val2, key) {
                            if (angular.equals(value.location_name, val2.location_name)) {
                                exists = true
                            }
                        });
                        if (exists === false) {
                            newArr.push(value);
                        }
                    } else {
                        newArr.push(value);
                    }
                });

                $scope.origin = newArr;
            } else {
                if (data_1 === undefined || data_1 == null) {
                    $scope.origin = [];
                    $scope.filterData.origin = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.origin.push({
                            'location_name': value.origin_data.location_name
                        });
                    });
                    var newArr = [];
                    angular.forEach($scope.origin, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value.location_name, val2.location_name)) {
                                    exists = true
                                }

                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        } else {
                            newArr.push(value);
                        }

                    });
                    $scope.origin = newArr;
                    $scope.destination = [];
                    $scope.filterData.destination = null;
                    angular.forEach($scope.y, function (value, key) {
                        $scope.destination.push({
                            'location_name': value.destination_data.location_name
                        });
                    });
                    var newArr = [];
                    angular.forEach($scope.destination, function (value, key) {
                        var exists = false;
                        if (newArr.length) {
                            angular.forEach(newArr, function (val2, key) {
                                if (angular.equals(value.location_name, val2.location_name)) {
                                    exists = true
                                }

                            });
                            if (exists === false) {
                                newArr.push(value);
                            }
                        } else {
                            newArr.push(value);
                        }
                    });
                    $scope.destination = newArr;
                }
            }
        };

        $scope.getVehNoNewAcc = function (customer) {
            console.log('cust', customer);
            $scope.filterData = {
                vehicle_type: '',
                customer: customer,
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: $scope.filterData.date_vehicle_required,
                to_date_vehicle_required: $scope.filterData.to_date_vehicle_required,
                s_from_date: '',
                s_to_date: '',
                vehicle_no: '',
                monthRange:  $scope.filterData.monthRange
            };
            $scope.vehicleNo = [];
            var id = null;
            if (customer) {
                id = customer.id;
            }
            AccountManagerServices.getAllVehNoListNew(id).then(function (response) {
                console.log('response_vehno', response);
                if (response.data.code.status === 100) {
                    $scope.vehicleNo = response.data.data_list;
                    if (customer) {
                        var newArr = [];
                        $scope.vehnox = response.data.data_list;
                        angular.forEach($scope.vehicleNo, function (value, key) {
                            var exists = false;
                            if (newArr.length) {
                                angular.forEach(newArr, function (val2, key) {
                                    if (angular.equals(value._source.vehicle_data.id, val2._source.vehicle_data.id)) {
                                        exists = true
                                    }
                                });
                                if (exists === false) {
                                    newArr.push(value);
                                }
                            } else {
                                newArr.push(value);
                            }
                        });
                        $scope.vehicleNo = newArr;
                    }
                    console.log('len_$scope.vehicleNo', $scope.vehicleNo.length);
                } else {
                    alert(response.data.code.message)
                }
            }, function (err) {
                swal("Oops", 'No internet connection.', "error")
            });
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};


        $scope.filterData = {
            lr: '',
            customer: '',
            origin: '',
            destination: '',
            broker: '',
            date_vehicle_required: '',
            to_date_vehicle_required: '',
            vehicle_no: ''
        };

        /**
         * Update Info of Driver in trip
         */

        $scope.editDriverInfoCompleted = function (data) {
            $scope.driver_code = data.order_data.driver_code;
            $scope.updateDriverDataTrip = {
                'driver_code': data.order_data.driver_code,
                'trip_id': data.id
            };
            swal({
                title: "Are you sure?",
                text: "You want to update driver info.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, update it!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    AccountManagerServices.updateDriverInfoTrip($scope.updateDriverDataTrip).then(function (response) {
                        if (response.code.status == 200) {
                            swal("Good Job!", "Driver Data Updated Successfully.", "success")
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                } else {
                    swal("Cancelled", "Driver details not updated:)", "error");
                }
            })
        };

        /**
         * Update Vehicle Market Data
         */

        $scope.editVehicleInfoCompleted = function (data) {
            $scope.updateVehicleDataTrip = {
                'trip_id': data.id
            };
            swal({
                title: "Are you sure?",
                text: "You want to update vehicle info.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, update it!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    AccountManagerServices.updateVehicleInfoTrip($scope.updateVehicleDataTrip).then(function (response) {
                        if (response.code.status == 200) {
                            swal("Good Job!", "Vehicle Data Updated Successfully.", "success")
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                } else {
                    swal("Cancelled", "Driver details not updated:)", "error");
                }
            })
        };

        $scope.pagination = {};
        $scope.completedTripFilter = function (count, filterData) {
            console.log('count', count)
            $scope.loading = true;
            this.filterData = filterData;
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.broker_id = '';
            $scope.customer_code = '';
            $scope.pod_status = '';
            $scope.broker_charge_status = '';

            if (this.filterData != undefined) {
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker._source.broker_data.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id;

                if (filterData.podStatusName)
                    $scope.pod_status = filterData.podStatusName.name

                if(filterData.monthRange && filterData.monthRange.monthIntervalValue!=undefined){
                    const currentDate = new Date();
                    $scope.to_date_vehicle_required = currentDate.toISOString().split('T')[0];
                    currentDate.setMonth(currentDate.getMonth() - filterData.monthRange.monthIntervalValue);
                    $scope.date_vehicle_required = currentDate.toISOString().split('T')[0];
                }

                if (filterData.brokerChargesStatus)
                    $scope.broker_charge_status = filterData.brokerChargesStatus.label;
            }


            $scope.filterDataObject = {
                customer: $scope.customer_code,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no,
                broker: $scope.broker_id,
                pod_status: $scope.pod_status,
                broker_charge_status: $scope.broker_charge_status
            };

            AccountManagerServices.completedTripFilterService(count, $scope.filterDataObject).then(function (response) {
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {
                    $scope.completedTrips = response.data.results.trip_data;
                    $scope.wf = response.data.results.wf;
                    $scope.completedTripPage.count = response.data.count;
                    //Reset Bulk approve related data
                    $scope.selectedTripsCount = 0;
                    $scope.selectedTripsData = [];
                    $scope.tripsSelected.allTripsSelected = false;

                    $scope.initScrollSync();
                } else {
                    swal("oops!", response.data.results.code.message, "error")
                }

            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
        };

        $scope.completedExportData = function (filterData) {
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.broker_id = '';
            $scope.customer_id = '';
            $scope.pod_status = '';
            $scope.broker_charge_status = '';

            if (filterData.vehicle_number)
                $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;

            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;

            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;

            if (filterData.date_vehicle_required)
                $scope.date_vehicle_required = filterData.date_vehicle_required;

            if (filterData.to_date_vehicle_required)
                $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

            if (filterData.lr)
                $scope.lr_no = filterData.lr.lr_reciept;

            if (filterData.broker){
                if(filterData.broker.id){
                    $scope.broker_id = filterData.broker.id;
                }else{
                    $scope.broker_id = filterData.broker._source.broker_data.id;
                }
            }

            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;

            if (filterData.podStatusName)
                $scope.pod_status = filterData.podStatusName.name

            if(filterData.monthRange && filterData.monthRange.monthIntervalValue!=undefined){
                const currentDate = new Date();
                $scope.to_date_vehicle_required = currentDate.toISOString().split('T')[0];
                currentDate.setMonth(currentDate.getMonth() - filterData.monthRange.monthIntervalValue);
                $scope.date_vehicle_required = currentDate.toISOString().split('T')[0];
            }

            if (filterData.brokerChargesStatus)
                $scope.broker_charge_status = filterData.brokerChargesStatus.label;

            $scope.filterDataObject = {
                customer: $scope.customer_id,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no,
                broker: $scope.broker_id,
                pod_status: $scope.pod_status,
                broker_charge_status: $scope.broker_charge_status
            };
            $scope.csvloading = true;

            var generate_completed_trip_csv = function (completedTripExport) {
                $scope.csvloading = false;
                $scope.completeExport = completedTripExport;
                $scope.downloadCompletedCsv = [];

                angular.forEach($scope.completeExport, function (value, key) {
                    $scope.lr_no = "";
                    angular.forEach(value.lr.lr_data, function (value, key) {
                        $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                    });

                    if (value.trip_end_date === null) {
                        $scope.trip_end_date = ''
                    } else {
                        $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
                    }
                    if (value.is_market === true) {
                        $scope.market = 'yes'
                    } else {
                        $scope.market = 'no'
                    }
                    if (value.lane.lane_code === undefined) {
                        $scope.route = ''
                    } else {
                        $scope.route = value.lane.lane_code
                    }
                    try {
                        var start_date_time = new Date(value.trip_start_date.split('T')[0] + " " + value.trip_start_time.split('T')[1]);
                        var end_date_time = new Date(value.trip_end_date + " " + value.trip_end_time);
                        diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
                        diff /= 60;
                        num = Math.abs(Math.round(diff));
                        var hours = (num / 60);
                        var rhours = Math.floor(hours);
                        var minutes = (hours - rhours) * 60;
                        var rminutes = Math.round(minutes);
                        var total_time = rhours + "hr " + rminutes + "min";
                    } catch (err) {
                        total_time = ''
                    }

                    $scope.completedExportValue = {
                        "Trip Code": value.trip_code,
                        "Market": $scope.market,
                        "Lr No": $scope.lr_no,
                        "Invoice No.": value.invoice_data.invoice_number ? value.invoice_data.invoice_number : '',
                        "E-way Bill No.": value.e_way_bill.e_way_bill_number ? value.e_way_bill.e_way_bill_number : '',
                        "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                        "Origin": value.order_data.origin.location_name,
                        "Destination": value.order_data.destination.location_name,
                        "Driver Nname": value.order_data.driver_name + '/' + value.order_data.driver_no,
                        "Vehicle No": value.order_data.vehicle_no,
                        "Route": $scope.route,
                        "Trip Status": value.trip_status.status_name,
                        "Placement Date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
                        "Start Date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
                        "Reporting Date": $scope.trip_end_date,
                        'Total Time': total_time,
                        "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code,
                        "POD Status": value.pod_status,
                        "Broker Charges Status": value.is_market ? value?.pod_data?.pod_broker_data?.broker_charge_status : "",
                        "Broker Loading Charges": value.is_market ? value?.pod_data?.pod_broker_data?.loading_charge : "",
                        "Broker Unloading Charges": value.is_market ? value?.pod_data?.pod_broker_data?.unloading_charge : "",
                        "Broker Damage/Shortage": value.is_market ? value?.pod_data?.pod_broker_data?.deduction_value : "",
                        "Broker Detention": value.is_market ? value?.pod_data?.pod_broker_data?.detention : "",
                        "Broker Taxes": value.is_market ? value?.pod_data?.pod_broker_data?.taxes : "",
                        "Broker TDS": value.is_market ? value?.pod_data?.pod_broker_data?.tds : "",
                        "Broker Surcharge": value.is_market ? value?.pod_data?.pod_broker_data?.b_surcharge : "",
                        "Broker charges created by": value.is_market ? value?.pod_data?.pod_broker_data?.created_by ?? '' : "",
                        "Broker charges created at": value.is_market ? value?.pod_data?.pod_broker_data?.created_at ?? '' : "",
                        "Broker charges updated by": value.is_market ? value?.pod_data?.pod_broker_data?.updated_by ?? '' : "",
                        "Broker charges updated at": value.is_market ? value?.pod_data?.pod_broker_data?.updated_at ?? '' : "",
                        "Broker charges approved by": value.is_market ? value?.pod_data?.pod_broker_data?.approved_by ?? '' : "",
                        "Broker charges approved at": value.is_market ? value?.pod_data?.pod_broker_data?.approved_at ?? '' : "",
                        "POD uploaded by": value.pod_updated_by.username,
                    };
                    $scope.downloadCompletedCsv.push($scope.completedExportValue)
                });
                var mystyle = {
                    headers: true,
                    column: {
                        style: {
                            Font: {
                                Bold: "1"
                            }
                        }
                    }
                };
                alasql('SELECT * INTO XLS("Completed Trips.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'placement_date')]);

            };
            var num_of_pages = 1;
            var completedTripExport = [];
            var recur_completed_trip = function () {
                AccountManagerServices.completedTripFilterService(num_of_pages, $scope.filterDataObject).then(function (response) {
                    if (response.data.results.code.status === 200) {
                        var count = response.data.count;
                        var total_pages = Math.ceil(count / response.data.pagesize);
                        num_of_pages = num_of_pages + 1;
                        if (num_of_pages <= total_pages) {
                            completedTripExport = completedTripExport.concat(response.data.results.trip_data);
                            recur_completed_trip()
                        } else {
                            completedTripExport = completedTripExport.concat(response.data.results.trip_data);
                            generate_completed_trip_csv(completedTripExport)
                        }
                        console.log('count', count);
                        $scope.progress_bar_per = ((completedTripExport.length / count) * 100).toFixed(2);
                    } else {
                        swal("Cancelled", response.data.results.code.message, "error")
                    }

                });


            };
            recur_completed_trip();
            // recur_completed_trip()
            // trackingServices.postCompleteExportData($scope.filterDataObject).then(function (response) {
            //     $scope.csvloading = false;
            //     if (response.data.code.status === 200) {
            //         $scope.completeExport = response.data.trip_data;
            //         $scope.downloadCompletedCsv = [];
            //
            //         angular.forEach($scope.completeExport, function (value, key) {
            //             $scope.lr_no = "";
            //             angular.forEach(value.lr.lr_data, function (value, key) {
            //                 $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
            //             });
            //
            //             if (value.trip_end_date === null) {
            //                 $scope.trip_end_date = ''
            //             } else {
            //                 $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
            //             }
            //             if (value.is_market === true) {
            //                 $scope.market = 'yes'
            //             } else {
            //                 $scope.market = 'no'
            //             }
            //             if (value.lane.lane_code === undefined) {
            //                 $scope.route = ''
            //             } else {
            //                 $scope.route = value.lane.lane_code
            //             }
            //             try {
            //                 var start_date_time = new Date(value.trip_start_date.split('T')[0] + " " + value.trip_start_time.split('T')[1]);
            //                 var end_date_time = new Date(value.trip_end_date + " " + value.trip_end_time);
            //                 diff = (end_date_time.getTime() - start_date_time.getTime()) / 1000
            //                 diff /= 60;
            //                 num = Math.abs(Math.round(diff));
            //                 var hours = (num / 60);
            //                 var rhours = Math.floor(hours);
            //                 var minutes = (hours - rhours) * 60;
            //                 var rminutes = Math.round(minutes);
            //                 var total_time = rhours + "hr " + rminutes + "min";
            //             }
            //             catch (err) {
            //                 total_time = ''
            //             }
            //
            //             $scope.completedExportValue = {
            //                 "Trip Code": value.trip_code,
            //                 "Market": $scope.market,
            //                 "Lr No": $scope.lr_no,
            //                 "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
            //                 "Origin": value.order_data.origin.location_name,
            //                 "Destination": value.order_data.destination.location_name,
            //                 "Driver Nname": value.order_data.driver_name + '/' + value.order_data.driver_no,
            //                 "Vehicle No": value.order_data.vehicle_no,
            //                 "Route": $scope.route,
            //                 "Trip Status": value.trip_status.status_name,
            //                 "Placement Date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
            //                 "Start Date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
            //                 "Reporting Date": $scope.trip_end_date,
            //                 'Total Time': total_time,
            //                 "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
            //             };
            //             $scope.downloadCompletedCsv.push($scope.completedExportValue)
            //         });
            //         var mystyle = {
            //             headers: true,
            //             column: {style: {Font: {Bold: "1"}}}
            //         };
            //         alasql('SELECT * INTO XLS("Completed Trips.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'placement_date')]);
            //     }
            // })
        };

        // $scope.completedExportData = function (filterData) {
        //     this.filterData = filterData;
        //     $scope.vehicle_no = '';
        //     $scope.origin_name = '';
        //     $scope.destination_name = '';
        //     $scope.date_vehicle_required = '';
        //     $scope.to_date_vehicle_required = '';
        //     $scope.lr_no = '';
        //     $scope.broker_id = '';
        //     $scope.customer_code = '';
        //     $scope.pod_status = '';
        //
        //     if (this.filterData != undefined) {
        //         if (filterData.vehicle_number)
        //             $scope.vehicle_no = filterData.vehicle_number._source.vehicle_data.vehicle_registration_number;
        //
        //         if (filterData.origin)
        //             $scope.origin_name = filterData.origin.location_name;
        //
        //         if (filterData.destination)
        //             $scope.destination_name = filterData.destination.location_name;
        //
        //         if (filterData.date_vehicle_required)
        //             $scope.date_vehicle_required = filterData.date_vehicle_required;
        //
        //         if (filterData.to_date_vehicle_required)
        //             $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;
        //
        //         if (filterData.lr)
        //             $scope.lr_no = filterData.lr.lr_reciept;
        //
        //         if (filterData.broker)
        //             $scope.broker_id = filterData.broker._source.broker_data.id;
        //
        //         if (filterData.customer)
        //             $scope.customer_code = filterData.customer.id
        //
        //         if (filterData.podStatusName)
        //             $scope.pod_status = filterData.podStatusName.name
        //     }
        //
        //     $scope.filterDataObject = {
        //         customer: $scope.customer_code,
        //         vehicle_no: $scope.vehicle_no,
        //         origin: $scope.origin_name,
        //         destination: $scope.destination_name,
        //         from_date: $scope.date_vehicle_required,
        //         to_date: $scope.to_date_vehicle_required,
        //         lr: $scope.lr_no,
        //         pod_status: $scope.pod_status,
        //         broker: $scope.broker_id
        //     };
        //
        //     $scope.csvloading = true;
        //     AccountManagerServices.postCompleteExportData($scope.filterDataObject).then(function (response) {
        //         $scope.csvloading = false;
        //         if (response.data.code.status === 200) {
        //             console.log('THE_DATA', response);
        //             $scope.completeExport = response.data.trip_data;
        //             $scope.downloadCompletedCsv = [];
        //             angular.forEach($scope.completeExport, function (value, key) {
        //                 $scope.lr_no = "";
        //                 angular.forEach(value.lr.lr_data, function (value, key) {
        //                     $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
        //                 });
        //                 if (value.trip_end_date === null) {
        //                     $scope.trip_end_date = ''
        //                 } else {
        //                     $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time;
        //                 }
        //                 if (value.is_market === true) {
        //                     $scope.market = 'yes'
        //                 } else {
        //                     $scope.market = 'no'
        //                 }
        //                 if (value.lane.lane_code === undefined) {
        //                     $scope.route = ''
        //                 } else {
        //                     $scope.route = value.lane.lane_code
        //                 }
        //                 $scope.completedExportValue = {
        //                     "Trip Code": value.trip_code,
        //                     "Market": $scope.market,
        //                     "Lr No": $scope.lr_no,
        //                     "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
        //                     "Origin": value.order_data.origin.location_name,
        //                     "Destination": value.order_data.destination.location_name,
        //                     "Broker": value.broker_data.company,
        //                     "Broker No.": value.broker_data.mobile_no,
        //                     "Driver Name": value.order_data.driver_name + '/' + value.order_data.driver_no,
        //                     "Second Driver Name": value.co_driver.driver_name + '/' + value.co_driver.mobile_number,
        //                     "Vehicle No": value.order_data.vehicle_no,
        //                     "Route": $scope.route,
        //                     "Trip Status": value.trip_status.status_name,
        //                     "Placement Date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
        //                     "Start Date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
        //                     "Reporting Date": $scope.trip_end_date,
        //                     "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code,
        //                     "Pod Status": value.pod_status,
        //                     "Payment Status": value.payment_status.payment_status_name
        //                 };
        //                 $scope.downloadCompletedCsv.push($scope.completedExportValue);
        //             });
        //             var mystyle = {
        //                 headers: true,
        //                 column: {style: {Font: {Bold: "1"}}}
        //             };
        //             alasql('SELECT * INTO XLS("Completed Trips.xls",?) FROM ?', [mystyle, $scope.downloadCompletedCsv = $filter('orderBy')($scope.downloadCompletedCsv, 'placement_date')]);
        //         }
        //     })
        // };

        /******************************Co-Driver Data Get*******************************************************/

        $scope.coDriverDetailsPlanning = function (orderAssign) {
            AccountManagerServices.coDriverDataPlanning(orderAssign.id).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.driver = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };
        /******************************Co-Driver Data Get end*******************************************************/

        $scope.isLRNotEditable = function (lrInfo) {
            return lrInfo.gobolt_business_app === true
                && (!$scope.isNullValue(lrInfo.lr_reciept)
                    || !$scope.isNullValue(lrInfo.pic_url));
        }


    }
]);
/*********************************************************************************
 * ControllerName:completedHistory.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/

app.controller('completedHistory.listing', ['$scope', '$state', '$rootScope', '$location', 'AccountManagerServices', '$filter', '$modal', 'MasterService', '$sce',
    function ($scope, $state, $rootScope, $location, AccountManagerServices, $filter, $modal, MasterService, $sce) {


        $scope.$watch('online', function (newStatus) {
            if (newStatus === false) {
                swal("internet Connection Lost!")
            }
        });

        $scope.monthRangeList = [
            {
                monthIntervalLabel: 'Last One Month',
                monthIntervalValue: 1,
            },
            {
                monthIntervalLabel: 'Last Two Months',
                monthIntervalValue: 2,
            },
            {
                monthIntervalLabel: 'Last Three Months',
                monthIntervalValue: 3,
            },
            {
                monthIntervalLabel: 'Last Four Months',
                monthIntervalValue: 4,
            }
        ]

        $scope.checkMonthRange = function (changedDate) {
            if ($scope.filterData.date_vehicle_required && $scope.filterData.to_date_vehicle_required) {
                const fromDate = moment($scope.filterData.date_vehicle_required);
                const toDate = moment($scope.filterData.to_date_vehicle_required);

                let diffInDays = toDate.diff(fromDate, 'days');
                let isValidDateRange = true;

                if (diffInDays > 90 && !$scope.filterData.customer) {
                    isValidDateRange = false;
                    swal("Cancelled", "Please select dates within 90 days range without customer", "error");
                } else if(diffInDays > 120 && $scope.filterData.customer){
                    isValidDateRange = false;
                    swal("Cancelled", "Please select dates within 120 days range with customer", "error");
                }else if (diffInDays < 0) {
                    isValidDateRange = false;
                    swal("Cancelled", "Start Date should be less than End Date", "error");
                }
                if (isValidDateRange) {
                    return;
                }
                if (changedDate.toUpperCase() === 'STARTDATE') {
                    $scope.filterData.date_vehicle_required = undefined;
                } else {
                    $scope.filterData.to_date_vehicle_required = undefined;
                }
            }
        }

        /******************************* Master Data *****************************************/
        $scope.entityType = MasterService.entityType;
        /******************************* Master Data *****************************************/
        //Ashish
        $('#ownedCustomerNext').click(function () {
            if (!$scope.podDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });
        $('#marketCustomerNext').click(function () {
            if (!$scope.podDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });
        $('#brokerNext').click(function () {
            if (!$scope.customerDetailsForm.$invalid)
                $('.nav-tabs > .active').next('li').find('a').trigger('click');
        });


        $scope.isPodActive = 'active';
        $scope.isCustActive = '';
        $scope.isCompActive = '';


        $scope.filter_applied = 0;
        $scope.completedTripPage = {};
        $scope.completedTripHistory = function (count) {
            $scope.loading = true;
            const currentDate = new Date();
            $scope.filterData.to_date_vehicle_required = currentDate.toISOString().split('T')[0];
            currentDate.setMonth(currentDate.getMonth() - 1);
            $scope.filterData.date_vehicle_required = currentDate.toISOString().split('T')[0];
            const filterDateObject = {
                'from_date': $scope.filterData.date_vehicle_required,
                'to_date': $scope.filterData.to_date_vehicle_required
            }
            AccountManagerServices.completedHistoryFilterData(count, filterDateObject).then(function (response) {
                $scope.loading = false;
                if (response.data.results.code.status === 200) {
                    $scope.completedTripsHistory = response.data.results.trip_data;
                    $scope.completedTripPage.count = response.data.count;
                    $scope.wf = response.data.results.wf;
                } else {
                    swal('Error',response.data.results.code.message, 'error')
                }
            })
        };
        // v_2
        $scope.completedTripPage_v_2 = {};
        $scope.completedTripHistory_v_2 = function (count) {
            $scope.loading = true;
            AccountManagerServices.getCompletedTripHistory(count).then(function (response) {
                $scope.loading = false;
                if (response.code.status === 200) {
                    $scope.completedTripsHistory_v_2 = response.trip_data;
                    $scope.completedTripPage_v_2.count = response.count;
                    $scope.wf_v_2 = response.wf;
                } else {
                    alert(response.code.message)
                }
            })
        };

        //Ashish
        $scope.getAdvancePayDetails = function (order_code) {
            AccountManagerServices.advancePayDetails(order_code).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.broker_rate = response.data.details.broker_rate;
                    $scope.cash_adv = response.data.details.cash_adv;
                    $scope.broker_advance = response.data.details.broker_advance;
                    $scope.balance_paid = response.data.details.payment_due;
                } else { }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        $scope.getOwnPayDetails = function (order_code) {
            AccountManagerServices.ownAdvancePayDetails(order_code).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.ownAdvanceData = response.data.details;
                    $scope.ownAdvanceData.totalEntry = 0;
                    $scope.ownAdvanceData.totalFuel = 0;
                    $scope.ownAdvanceData.totalMeals = 0;
                    $scope.ownAdvanceData.totalPolice = 0;
                    $scope.ownAdvanceData.totalToll = 0;
                    $scope.ownAdvanceData.totalAdvance = 0;
                    $scope.ownAdvanceData.totalExpenses = 0;
                    angular.forEach($scope.ownAdvanceData, function (value) {
                        if (value.advance_data.advance_for_entry != '')
                            $scope.ownAdvanceData.totalEntry += parseInt(value.advance_data.advance_for_entry);
                        if (value.advance_data.advance_for_fuel != '')
                            $scope.ownAdvanceData.totalFuel += parseInt(value.advance_data.advance_for_fuel);
                        if (value.advance_data.advance_for_meals != '')
                            $scope.ownAdvanceData.totalMeals += parseInt(value.advance_data.advance_for_meals);
                        if (value.advance_data.advance_for_police != '')
                            $scope.ownAdvanceData.totalPolice += parseInt(value.advance_data.advance_for_police);
                        if (value.advance_data.total_advance != '')
                            $scope.ownAdvanceData.totalAdvance += parseInt(value.advance_data.total_advance);
                        if (value.actual_data.total_actual != '')
                            $scope.ownAdvanceData.totalExpenses += parseInt(value.actual_data.total_actual);
                        if (value.advance_data.advance_for_toll != '')
                            $scope.ownAdvanceData.totalToll += parseInt(value.advance_data.advance_for_toll);
                    });
                } else { }
            }, function (err) {
                alert('Error Occurred')
            })
        };
        //=======
        //                            console.log('completedTrips', value)
        //                            $scope.lr_no = "";
        //                            angular.forEach(value.lr.lr_data, function (value, key) {
        //                                $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
        //                            })
        //                            $scope.lr_no = $scope.lr_no.slice(0, -1);
        //>>>>>>> 37f0064edf61399725d0c2d53c9df0eb89cd6851

        //$scope.completedTripHistory = function () {
        //    AccountManagerServices.getCompletedTripHistory().then(function (response) {
        //        console.log('response', response)
        //        if (response.code.status == 200) {
        //            $scope.loading = false;
        //            $scope.completedTripsHistory = response.trip_data;
        //        }
        //        else {
        //            alert("Something bad happened, Please try again.")
        //        }
        //    })
        //}

        $scope.checkIfCustomer = function (customer) {
            if (!customer) {
                swal("Oops", 'Please select customer First.', "warning");
                return;
            }
        };

        $scope.$watch('filterData.customer', function(newValue) {
            if (newValue === null || newValue === undefined) {
              $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.date_vehicle_required', function() {
            if ($scope.filterData.date_vehicle_required) {
                $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.to_date_vehicle_required', function() {
            if ($scope.filterData.to_date_vehicle_required) {
                $scope.filterData.monthRange = undefined;
            }
        });

        $scope.$watch('filterData.monthRange', function() {
            if ($scope.filterData.monthRange && $scope.filterData.monthRange.monthIntervalValue) {
                $scope.filterData.date_vehicle_required = undefined;
                $scope.filterData.to_date_vehicle_required = undefined;
            }
        });

        $scope.filterData = {
            lr: '',
            customer: '',
            origin: '',
            destination: '',
            broker: '',
            date_vehicle_required: '',
            to_date_vehicle_required: ''
        };
        $scope.pagination = {};
        $scope.getSelectCustomerVal = function (count, filterData) {
            $scope.loading = true;
            $scope.filter_applied = 1;
            $scope.lr_no = '';
            $scope.customer_id = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.broker = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';


            if (filterData.lr)
                $scope.lr_no = filterData.lr.lr_reciept;
            if (filterData.customer)
                $scope.customer_id = filterData.customer.id;
            if (filterData.origin)
                $scope.origin_name = filterData.origin.location_name;
            if (filterData.destination)
                $scope.destination_name = filterData.destination.location_name;
            if (filterData.broker)
                $scope.broker = filterData.broker.id;
            if (filterData.date_vehicle_required)
                $scope.date_vehicle_required = filterData.date_vehicle_required;
            if (filterData.to_date_vehicle_required)
                $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

            if(filterData.monthRange && filterData.monthRange.monthIntervalValue!=undefined){
                const currentDate = new Date();
                $scope.to_date_vehicle_required = currentDate.toISOString().split('T')[0];
                currentDate.setMonth(currentDate.getMonth() - filterData.monthRange.monthIntervalValue);
                $scope.date_vehicle_required = currentDate.toISOString().split('T')[0];
            }

            $scope.filterDataObject = {
                lr: $scope.lr_no,
                customer: $scope.customer_id,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                broker: $scope.broker,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required
            };

            //Ashish
            $scope.completedTripFilterPage = {
                'next': '',
                'previous': '',
                'count': ''
            };
            $scope.completedTripFilter_listing = function () {
                $scope.searchText = '';
                if ($scope.completedTripFilterPage.next == null) {
                    swal("oops!", "No more records!", "error");
                } else {
                    AccountManagerServices.completedHistoryFilterData(count, $scope.filterDataObject).then(function (response) {
                        $scope.loading = false;
                        if (count === undefined) {
                            $scope.pagination.current = 1;
                        }
                        if (response.data.results.code.status === 200) {
                            $scope.completedTripsHistory = response.data.results.trip_data;
                            $scope.completedTripPage.count = response.data.count;
                            $scope.wf = response.data.results.wf;
                        } else {
                            swal('Error', response.data.results.code.message, 'error');
                        }
                    })
                }
            };
            $scope.completedTripFilter_listing();

            //AccountManagerServices.completedHistoryFilterData($scope.filterDataObject).then(function (response) {
            //    if (response.data.code.status === 200) {
            //        $scope.completedTripsHistory = response.data.trip_data;
            //    } else {
            //        swal("oops!", response.data.code.message, "error")
            //    }
            //
            //}, function (error) {
            //    alert('Error Occurred')
            //})
        };


        $scope.ongoingTripFilter = function (count, filterData) {
            this.filterData = filterData;
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.broker_id = '';
            $scope.customer_code = '';

            if (filterData != undefined) {
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id

            }
            $scope.filterDataObject = {
                customer: $scope.customer_code,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no,
                broker: $scope.broker_id
            };
            //$scope.ongoingTripFilterPage = {'next': '', 'previous': '', 'count': ''};
            $scope.onGoingTrips = '';
            $scope.ongoingTripFilterListing = function () {
                $scope.loading = true;
                AccountManagerServices.ongoingTripFilterService(count, $scope.filterDataObject).then(function (response) {
                    $scope.loading = false;
                    if (count === undefined) {
                        $scope.pagination.current = 1;
                    }
                    if (response.data.results.code.status == 200) {
                        $scope.onGoingTrips = response.data.results.trip_data;
                        $scope.wf = response.data.results.wf;

                        //if ($scope.ongoingTripFilterPage.next) {
                        //    $scope.onGoingTrips = $scope.onGoingTrips.concat(response.data.results.trip_data)
                        //}
                        //else {
                        //    $scope.onGoingTrips = angular.extend(response.data.results.trip_data);
                        //}
                        //$scope.ongoingTripFilterPage.next = response.data.next;
                        //$scope.ongoingTripFilterPage.previous = response.data.previous;
                        $scope.ongoingTripPage.count = response.data.count;
                        //if ($scope.ongoingTripFilterPage.next != null) {
                        //    $scope.ongoingTripFilterListing()
                        //}
                    } else {
                        swal(response.data.results.code.message)
                    }
                })
            };
            //$scope.ongoingTripFilterListing();
        };

        $scope.clearFilter = function () {
            $scope.completedTripsHistory = '';
            $scope.completedTripPage.next = '';
            $scope.filter_applied = 0;
            $scope.filterData = {
                lr: '',
                customer: '',
                origin: '',
                destination: '',
                date_vehicle_required: '',
                to_date_vehicle_required: ''
            }
            $scope.completedTripHistory();
        };

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([
                // function (callback) {
                //     // request no 1
                //     AccountManagerServices.getAllLrHistoryLists().then(function (response) {
                //         if (response.data.results.code.status === 200) {
                //             $scope.lrName = response.data.results.data_list;
                //             callback();
                //         } else {
                //             alert(response.data.results.code.message)
                //         }
                //     }, function (err) {
                //         swal("Oops", 'No internet connection.', "error")
                //     });
                // },

                function (callback) {
                    //request no 2
                    AccountManagerServices.getAllCustomerLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.customerName = response.data.customer;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 3
                    AccountManagerServices.getAllLocationLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.locationNmae = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 4
                    AccountManagerServices.getAllBrokerList().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.brokerName = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 5
                    AccountManagerServices.getAllVehicleLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleNo = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }

            ])
        };

        $scope.asyncRequestForFilter();

        $scope.getLrNumbers = function (data) {
            if (data.length > 3) {
                var lr = {
                    'lr_num': data
                };
                AccountManagerServices.getLrNumSer(lr).then(function (response) {
                    console.log('response', response)
                    if (response.data.code.status === 200) {
                        $scope.lrName = response.data.lr;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })
            }
        };

        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            AccountManagerServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};
        $scope.trustSrc = function (src) {
            return $sce.trustAsResourceUrl(src);
        };

        $scope.showDlPicture = function (dl_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url;
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };
        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url;
                });
            } else {
                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
            }

        };
        $scope.showRcPicture = function (rc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url;
                });
            } else {
                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
            }

        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url;
                });
            } else {
                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
            }

        };
        $scope.showLrPicture = function (lr_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrPic = response.data.url;
                });
            } else {
                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };
        $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url;
                });
            } else {
                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
            }

        };
        $scope.showLrNumPicture = function (lr_num_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url;
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }

        };
        $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehi_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverAndVehiclePic = response.data.url;
                });
            } else {
                $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic
            }

        };
        $scope.showLrNumberPicture = function (lr_num_pic) {
            $scope.showLrNumPic = null;
            if (!lr_num_pic) {
                setTimeout(function() {
                    $('#lr_number_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(lr_num_pic)) {
                $scope.showLrNumPic = lr_num_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showLrNumPic = response.data.url;
                    } else {
                        $('#lr_number_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }


        };
        $scope.showPermitPicture = function (permit_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPermitPic = response.data.url;
                });
            } else {
                $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
            }


        };

        $scope.driverDetailsAccount = function (orderAssign) {
            AccountManagerServices.driverDataAccount(orderAssign.id).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.driver = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };

        $scope.vehicleDetailAccount = function (vehicleData) {
            AccountManagerServices.vehicleDataAccount(vehicleData.id).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.vehicle = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (error) {
                alert('Error Occurred')
            })
        };

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            console.log('column', column)
            console.log('$scope.sortColumn', $scope.sortColumn)
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
            //return '';
        };

        $scope.completedTripsHistoryExportData = function (completedTripsHistory) {


            $scope.downloadCompletedHistoryCsv = [];

            angular.forEach(completedTripsHistory, function (value, key) {

                console.log('completedTripsHistory', value)
                $scope.lr_no = "";
                angular.forEach(value.lr.lr_data, function (value, key) {
                    $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                })

                if (value.trip_end_date === null) {
                    $scope.trip_end_date = ''
                } else {
                    $scope.trip_end_date = value.trip_end_date.split('T')[0] + '/' + value.trip_end_time
                    //console.log('$scope.trip_end_date', $scope.trip_end_date)
                }
                if (value.is_market === true) {
                    $scope.market = 'yes'
                } else {
                    $scope.market = 'no'
                }
                if (value.lane.lane_code === undefined) {
                    $scope.route = ''
                } else {
                    $scope.route = value.lane.lane_code
                }
                $scope.completedHistoryExportValue = {
                    "trip_code": value.trip_code,
                    "market": $scope.market,
                    "lr_no": $scope.lr_no,
                    "E-way Bill": value?.e_way_bill?.e_way_bill_number ? value.e_way_bill.e_way_bill_number : '',
                    "customer": value.company_name + '<' + value.order_data.customer_code + '>',
                    "origin": value.order_data.origin.location_name,
                    "destination": value.order_data.destination.location_name,
                    "driver_name": value.order_data.driver_name + '/' + value.order_data.driver_no,
                    "vehicle_no": value.order_data.vehicle_no,
                    "route": $scope.route,
                    "trip_status": value.trip_status.status_name,
                    "placement_date": value.vehicle_placement_date + '/' + value.vehicle_placement_time,
                    "start_date": value.trip_start_date.split('T')[0] + '/' + value.trip_start_time.split('T')[1].match(/.{5}/g)[0],
                    "reporting_date": $scope.trip_end_date,
                    "buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
                }
                $scope.downloadCompletedHistoryCsv.push($scope.completedHistoryExportValue)
                console.log('value', $scope.completedHistoryExportValue)

            })
            var mystyle = {
                headers: true,
                column: {
                    style: {
                        Font: {
                            Bold: "1"
                        }
                    }
                }
            };
            alasql('SELECT * INTO XLS("account-completed-history.xls",?) FROM ?', [mystyle, $scope.downloadCompletedHistoryCsv = $filter('orderBy')($scope.downloadCompletedHistoryCsv, 'placement_date')]);
        }

        /**
         * Name- podDataCompletedHis
         * Operation- Getting details for POD
         * @param completedTrip
         * Dev By- Prafull
         */

        $scope.HideModel = function () {
            $scope.isCustActive = '';
            $scope.isCompActive = '';
            $scope.isPodActive = 'active';
            angular.element('#pod_Edit').modal('hide');
        };

        $scope.podDataCompletedHis = function (completedTrip) {
            $('#myTab a[data-target="#podDetails"]').click();
            $scope.indexForUpdatePod = $scope.completedTripsHistory.indexOf(completedTrip);
            $rootScope.indexForUpdatePodOwned = $scope.indexForUpdatePod;
            $rootScope.completedTripsOwned = $scope.completedTripsHistory;
            $scope.tripCompleted = {};
            AccountManagerServices.gettingPod(completedTrip.id).then(function (response) {
                console.log('===>', response);
                if (response.data.code.status == 200) {
                    var oneDay = 24 * 60 * 60 * 1000;
                    $scope.response = response;
                    console.log('response', response)
                    $scope.tripCompleted.is_market = response.data.data.is_market;
                    $scope.tripCompleted.can_upload = response.data.can_upload;
                    $scope.tripCompleted.tat = response.data.data.tat;
                    $scope.tripCompleted.id = completedTrip.id;
                    $scope.tripCompleted.pod_updated_by = response.data.data.pod_updated_by;
                    $scope.tripCompleted.cus_deten3 = response.data.data.cus_deten3;
                    $scope.tripCompleted.cus_deten10 = response.data.data.cus_deten10;
                    $scope.tripCompleted.cus_detenN = response.data.data.cus_detenN;
                    $scope.tripCompleted.customer_detention_rate = 'For 3 days' + ' ' + ':' + response.data.data.cus_deten3 + ',' + '  ' + 'For 10 days' + ' ' + ':' + response.data.data.cus_deten10 + ',' + '  ' + 'Beyond 10 days' + ': ' + response.data.data.cus_detenN;
                    // $scope.tripCompleted.customer_detention_rate = response.data.data.customer_detention_rate;
                    $scope.tripCompleted.broker_detention_rate = 'For 3 days' + ' ' + ':' + response.data.data.deten3 + ',' + '  ' + 'For 10 days' + ' ' + ':' + response.data.data.deten10 + ',' + '  ' + 'Beyond 10 days' + ': ' + response.data.data.detenN;
                    $scope.tripCompleted.deten3 = response.data.data.deten3;
                    $scope.tripCompleted.deten10 = response.data.data.deten10;
                    $scope.tripCompleted.detenN = response.data.data.detenN;
                    if (response.data.data.pod_updated_by.mobile_no === undefined) {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username
                    } else {
                        $scope.tripCompleted.pod_uploaded_by = response.data.data.pod_updated_by.username + ',' + response.data.data.pod_updated_by.mobile_no
                    }
                    if (!response.data.data.pod_trip_end_date)
                        $scope.tripCompleted.pod_trip_end_date = completedTrip.trip_end_date;
                    else
                        $scope.tripCompleted.pod_trip_end_date = $filter('date')(response.data.data.pod_trip_end_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_trip_end_time)
                        $scope.tripCompleted.pod_trip_end_time = completedTrip.trip_end_time;
                    else
                        $scope.tripCompleted.pod_trip_end_time = $filter('date')(response.data.data.pod_trip_end_time, 'HH:mm');
                    if (!response.data.data.pod_unloading_date)
                        $scope.tripCompleted.pod_unloading_date = completedTrip.unloading_date;
                    else
                        $scope.tripCompleted.pod_unloading_date = $filter('date')(response.data.data.pod_unloading_date, 'yyyy-MM-dd');
                    if (!response.data.data.pod_unloading_time)
                        $scope.tripCompleted.pod_unloading_time = completedTrip.unloading_time;
                    else
                        $scope.tripCompleted.pod_unloading_time = $filter('date')(response.data.data.pod_unloading_time, 'HH:mm');

                    var day1 = new Date($scope.tripCompleted.pod_trip_end_date + " " + $scope.tripCompleted.pod_trip_end_time)
                    var day2 = new Date($scope.tripCompleted.pod_unloading_date + " " + $scope.tripCompleted.pod_unloading_time)
                    var one_day = 1000 * 60 * 60 * 24;
                    var date1_ms = day1.getTime();
                    var date2_ms = day2.getTime();
                    var difference_ms = date2_ms - date1_ms;
                    $scope.tripCompleted.detention_days = Math.round(difference_ms / oneDay) - 1;
                    if ($scope.tripCompleted.detention_days < 0) {
                        $scope.tripCompleted.detention_days = 0;
                    }
                    var days = $scope.tripCompleted.detention_days;
                    var total_deten = 0;
                    for (var i = 1; i <= days; i++) {
                        if (i <= 3) {
                            total_deten += parseInt(response.data.data.deten3);
                        } else if (i <= 10) {
                            total_deten += parseInt(response.data.data.deten10);
                        } else if (i > 10) {
                            total_deten += parseInt(response.data.data.detenN);
                        }
                    }
                    console.log('total_deten', total_deten)

                    var total_deten_cus = 0;
                    for (var j = 1; j <= days; j++) {
                        if (j <= 3) {
                            total_deten_cus += parseInt(response.data.data.cus_deten3);
                        } else if (j <= 10) {
                            total_deten_cus += parseInt(response.data.data.cus_deten10);
                        } else if (j > 10) {
                            total_deten_cus += parseInt(response.data.data.cus_detenN);
                        }
                    }
                    console.log('total_deten_cus', total_deten_cus)

                    $scope.tripCompleted.pod_data = response.data.data.pod_data;
                    console.log('$scope.tripCompleted', $scope.tripCompleted)
                    // $scope.tripCompleted.pod_data.pod_customer_data.detentions = completedTrip.customer_detention_rate * response.data.data.detention_days;
                    if ($scope.tripCompleted.pod_data.pod_broker_data.detention === 0 || $scope.tripCompleted.pod_data.pod_broker_data.detention === '') {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = total_deten;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.detention = response.data.data.pod_data.pod_broker_data.detention;
                    }

                    if ($scope.tripCompleted.pod_data.pod_customer_data.detentions === 0 || $scope.tripCompleted.pod_data.pod_customer_data.detentions === '') {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = total_deten_cus;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.detentions = response.data.data.pod_data.pod_customer_data.detentions;
                    }
                    $scope.tripCompleted.tds = response.data.data.tds;

                    if ($scope.tripCompleted.tds != null) {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = $scope.tripCompleted.tds
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.tds = '';
                    }
                    if (response.data.data.pod_data.pod_customer_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.taxes = response.data.data.pod_data.pod_customer_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.loading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.loading_charges = response.data.data.pod_data.pod_customer_data.loading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.unloading_charges === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.unloading_charges = response.data.data.pod_data.pod_customer_data.unloading_charges
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_value = response.data.data.pod_data.pod_customer_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_customer_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = 'NONE';
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.deduction_comment = response.data.data.pod_data.pod_customer_data.deduction_comment
                    }
                    if (response.data.data.pod_data.pod_broker_data.taxes === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.taxes = response.data.data.pod_data.pod_broker_data.taxes
                    }
                    if (response.data.data.pod_data.pod_customer_data.tds === "") {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_customer_data.tds = response.data.data.pod_data.pod_customer_data.tds
                    }
                    if (response.data.data.pod_data.pod_broker_data.loading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.loading_charge = response.data.data.pod_data.pod_broker_data.loading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.unloading_charge === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.unloading_charge = response.data.data.pod_data.pod_broker_data.unloading_charge
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_value === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = 0;
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_value = response.data.data.pod_data.pod_broker_data.deduction_value
                    }
                    if (response.data.data.pod_data.pod_broker_data.deduction_comment === "") {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = 'None';
                    } else {
                        $scope.tripCompleted.pod_data.pod_broker_data.deduction_comment = response.data.data.pod_data.pod_broker_data.deduction_comment
                    }
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }

            }, function (err) {
                alert('Error Occurred')
            });
        };

        // View POD
        $scope.viewPod = function (documentLink) {
            $scope.showPodFrontPic = documentLink;
        }

        // Delete POD from DAS
        $scope.deleteUploadedPod = function (entity_id) {
            AccountManagerServices.deleteUploadedPod(entity_id).then(function (response) {
                console.log('deleteUploadedPod_response', response);
                if (response.data.code.status == 200) {
                    swal("Good job!", response.data.code.message, "success");
                    $scope.tripCompleted.pod_uploaded_data.documentLink = null;
                } else {
                    swal("Cancelled", response.data.code.message, "error")
                }
            }, function (err) {
                alert('Error Occurred')
            });
        }

        $scope.podFrontPicture = function (pod_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(pod_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPodFrontPic = response.data.url;
                });
            } else {
                $scope.showPodFrontPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + pod_pic;
            }

            // console.log('showPodFpicHis', $scope.showPodFrontPic);
            $scope.loading = false;
        };
        $scope.showPodBackPicture = function (pod_back_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(pod_back_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPodBackPic = response.data.url;
                });
            } else {
                $scope.showPodBackPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + pod_back_pic;
            }

            $scope.loading = false;
        };
        $scope.showOtherDocPicture = function (other_doc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(other_doc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showOtherDocPic = response.data.url;
                });
            } else {
                $scope.showOtherDocPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + other_doc_pic;
            }

            $scope.loading = false;
        };

        $scope.showTDSCertificatePicture = function (tds_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(tds_pic).then(function (response) {

                    // console.log(response)
                    $scope.showTdsCertificatePic = response.data.url;
                });
            } else {
                $scope.showTdsCertificatePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + tds_pic;
            }

            $scope.loading = false;
        };

        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                if ($scope.completedTripsHistory) {
                    $scope.counted = $filter("filter")($scope.completedTripsHistory, query).length;
                }
            });
        };

        $scope.showFrontInvoicePicture = function (front_invoice) {
            $scope.$watch('front_invoice', function () {
                $scope.invoiceFront = null;
                $rootScope.invoiceFront = null;

                if (!front_invoice) {
                    setTimeout(function() {
                        $('#frontInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(front_invoice)) {
                    $rootScope.invoiceFront = front_invoice;
                    $scope.invoiceFront = front_invoice;
                    return;
                }

                if (pic) {
                    AccountManagerServices.picLoadServerA(front_invoice).then(function (response) {

                        // console.log(response)
                        if (response.data.code.status === 200) {
                            $scope.invoiceFront = response.data.url;
                            $rootScope.invoiceFront = response.data.url;
                        } else {
                            $('#frontInvoice').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                } else {
                    $rootScope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                    $scope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                }

            })
        };

        $scope.showBackInvoicePicture = function (back_invoice) {
            $scope.invoiceBack = null;
            $rootScope.invoiceBack = null;
            if (!back_invoice) {
                setTimeout(function() {
                    $('#backInvoice').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(back_invoice)) {
                $rootScope.invoiceBack = back_invoice;
                $scope.invoiceBack = back_invoice;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(back_invoice).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.invoiceBack = response.data.url;
                        $rootScope.invoiceBack = response.data.url;
                    } else {
                        $('#backInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $rootScope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
                $scope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
            }

        };

         $scope.showEwayBillPicture = function (e_way_bill_pic) {
            $scope.eWayBillPic = null;
            if (!e_way_bill_pic) {
                setTimeout(function() {
                    $('#e_way_bill_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(e_way_bill_pic)) {
                $scope.eWayBillPic = e_way_bill_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(e_way_bill_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.eWayBillPic = response.data.url;
                    } else {
                        $('#e_way_bill_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
            }

        };

        $scope.isFileFromGoogleStorage = function (file) {
            return file.includes('https://storage.googleapis.com');
        };

        $scope.isPdf = function (url) {
            return url && url.toLowerCase().includes(".pdf");
        };

    }
]);
/*********************************************************************************
 * ControllerName:ongoing.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/
app.controller('ongoing.listing', ['$scope', '$state', '$rootScope', '$stateParams', 'AccountManagerServices', '$location', '$timeout', 'NgMap', '$modal', 'spinnerService', '$filter',
    function ($scope, $state, $rootScope, $stateParams, AccountManagerServices, $location, $timeout, NgMap, $modal, spinnerService, $filter) {

        //Ashish
        NgMap.getMap({
            id: 'trackingMap'
        }).then(function (map) {
            $scope.trackingMap = map
        });

        $scope.counted = 1;
        $scope.setCountedValue = function () {
            $scope.$watch("searchText", function (query) {
                $scope.counted = $filter("filter")($scope.onGoingTrips, query).length;
            });
        };

        //Ashish
        $scope.getAdvancePayDetails = function (order_code) {
            AccountManagerServices.advancePayDetails(order_code).then(function (response) {
                console.log('response_here', response);
                if (response.data.code.status == 200) {
                    $scope.broker_rate = response.data.details.broker_rate;
                    $scope.cash_adv = response.data.details.cash_adv;
                    $scope.broker_advance = response.data.details.broker_advance;
                    $scope.balance_paid = response.data.details.payment_due;
                } else { }
            }, function (err) {
                alert('Error Occurred')
            })
        };


        $scope.getOwnPayDetails = function (order_code) {
            AccountManagerServices.ownAdvancePayDetails(order_code).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.ownAdvanceData = response.data.details;
                    $scope.ownAdvanceData.totalEntry = 0;
                    $scope.ownAdvanceData.totalFuel = 0;
                    $scope.ownAdvanceData.totalMeals = 0;
                    $scope.ownAdvanceData.totalPolice = 0;
                    $scope.ownAdvanceData.totalToll = 0;
                    $scope.ownAdvanceData.totalAdvance = 0;
                    $scope.ownAdvanceData.totalExpenses = 0;
                    angular.forEach($scope.ownAdvanceData, function (value) {
                        console.log('value', value);
                        if (value.advance_data.advance_for_entry != '')
                            $scope.ownAdvanceData.totalEntry += parseInt(value.advance_data.advance_for_entry);
                        if (value.advance_data.advance_for_fuel != '')
                            $scope.ownAdvanceData.totalFuel += parseInt(value.advance_data.advance_for_fuel);
                        if (value.advance_data.advance_for_meals != '')
                            $scope.ownAdvanceData.totalMeals += parseInt(value.advance_data.advance_for_meals);
                        if (value.advance_data.advance_for_police != '')
                            $scope.ownAdvanceData.totalPolice += parseInt(value.advance_data.advance_for_police);
                        if (value.advance_data.total_advance != '')
                            $scope.ownAdvanceData.totalAdvance += parseInt(value.advance_data.total_advance);
                        if (value.actual_data.total_actual != '')
                            $scope.ownAdvanceData.totalExpenses += parseInt(value.actual_data.total_actual);
                        if (value.advance_data.advance_for_toll != '')
                            $scope.ownAdvanceData.totalToll += parseInt(value.advance_data.advance_for_toll);
                    });
                    console.log('$scope.ownAdvanceData', $scope.ownAdvanceData)
                } else { }
            }, function (err) {
                alert('Error Occurred')
            })
        };

        /**
         * functionName:trip_listing
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 21/07/2017
         * testerName:
         * testDate:
         */
        $scope.ongoingTripPage = {};
        $scope.trip_listing = function (count) {
            $scope.loading = true;
            AccountManagerServices.onGoingTrip(count).then(function (response) {
                console.log('Response', response);
                $scope.loading = false;
                if (response.results.code.status === 200) {
                    $scope.onGoingTrips = response.results.trip_data;
                    $scope.wf = response.results.wf;
                    $scope.ongoingTripPage.count = response.count;
                    // try {
                    //     if ($scope.onGoingTrips.length) {
                    //         $scope.onGoingTrips = $scope.onGoingTrips.concat(response.results.trip_data)
                    //         console.log('$scope.onGoingTrips', $scope.onGoingTrips)
                    //     }
                    //     else {
                    //         $scope.searchText = '';
                    //         $scope.onGoingTrips = response.results.trip_data;
                    //         console.log('$scope.onGoingTrips', $scope.onGoingTrips)
                    //     }
                    // }
                    // catch (err) {
                    //     $scope.onGoingTrips = response.results.trip_data;
                    // }
                    // $scope.ongoingTripPage.next = response.next;
                    // $scope.ongoingTripPage.previous = response.previous;

                    // if ($scope.ongoingTripPage.next != null) {
                    //     $scope.trip_listing()
                    // }

                } else {
                    alert(response.results.code.message)
                }
            })
        };

        // v_2
        $scope.ongoingTripPage_v_2 = {};
        $scope.trip_listing_v_2 = function (count) {
            $scope.loading = true;
            AccountManagerServices.onGoingTrip(count).then(function (response) {
                console.log('Response', response);
                $scope.loading = false;
                if (response.results.code.status === 200) {
                    $scope.onGoingTrips_v_2 = response.results.trip_data;
                    $scope.wf_v_2 = response.results.wf;
                    $scope.ongoingTripPage_v_2.count = response.count;
                    // try {
                    //     if ($scope.onGoingTrips.length) {
                    //         $scope.onGoingTrips = $scope.onGoingTrips.concat(response.results.trip_data)
                    //         console.log('$scope.onGoingTrips', $scope.onGoingTrips)
                    //     }
                    //     else {
                    //         $scope.searchText = '';
                    //         $scope.onGoingTrips = response.results.trip_data;
                    //         console.log('$scope.onGoingTrips', $scope.onGoingTrips)
                    //     }
                    // }
                    // catch (err) {
                    //     $scope.onGoingTrips = response.results.trip_data;
                    // }
                    // $scope.ongoingTripPage.next = response.next;
                    // $scope.ongoingTripPage.previous = response.previous;

                    // if ($scope.ongoingTripPage.next != null) {
                    //     $scope.trip_listing()
                    // }

                } else {
                    alert(response.results.code.message)
                }
            })
        };

        $scope.showDlPicture = function (dl_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url;
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };

        $scope.showDlPicture_v2 = function (dl_pic, d) {
            $scope.name = d;
            if (pic) {
                AccountManagerServices.picLoadServerA(dl_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDlPic = response.data.url;
                });
            } else {
                $scope.showDlPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + dl_pic
            }

        };

        $scope.showInsuPicture = function (insurance_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(insurance_pic).then(function (response) {

                    // console.log(response)
                    $scope.showInsurancePic = response.data.url;
                });
            } else {
                $scope.showInsurancePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + insurance_pic
            }

        };
        $scope.showRcPicture = function (rc_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(rc_pic).then(function (response) {

                    // console.log(response)
                    $scope.showRcPic = response.data.url;
                });
            } else {
                $scope.showRcPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + rc_pic
            }

        };
        $scope.showFitPicture = function (fitness_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(fitness_pic).then(function (response) {

                    // console.log(response)
                    $scope.showFitnessPic = response.data.url;
                });
            } else {
                $scope.showFitnessPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + fitness_pic
            }


        };

        $scope.showLrPicture = function (lr_pic) {
            $scope.showLrPic = null;
            if (!lr_pic) {
                setTimeout(function() {
                    $('#lr_pic_ongoing').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(lr_pic)) {
                $scope.showLrPic = lr_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showLrPic = response.data.url;
                    } else {
                        $('#lr_pic_ongoing').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showLrPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };

        $scope.showDriverVehiclePicture = function (driver_vehicle_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehicle_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverVehiclePic = response.data.url;
                });
            } else {
                $scope.showDriverVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehicle_pic
            }

            console.log(' $scope.showDriverVehiclePic ', $scope.showDriverVehiclePic)
        };
        $scope.showLrNumPicture = function (lr_num_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(lr_num_pic).then(function (response) {

                    // console.log(response)
                    $scope.showLrNumPic = response.data.url;
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_num_pic
            }

        };
        $scope.showDriverAndVehiclePicture = function (driver_vehi_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(driver_vehi_pic).then(function (response) {

                    // console.log(response)
                    $scope.showDriverAndVehiclePic = response.data.url;
                });
            } else {
                $scope.showDriverAndVehiclePic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + driver_vehi_pic
            }

        };
        $scope.showLrNumberPicture = function (lr_pic) {
            $scope.showLrNumPic = null;
            if (!lr_pic) {
                setTimeout(function() {
                    $('#lr_number_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(lr_pic)) {
                $scope.showLrNumPic = lr_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(lr_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.showLrNumPic = response.data.url;
                    } else {
                        $('#lr_number_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $scope.showLrNumPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + lr_pic
            }

        };
        $scope.showPermitPicture = function (permit_pic) {
            if (pic) {
                AccountManagerServices.picLoadServerA(permit_pic).then(function (response) {

                    // console.log(response)
                    $scope.showPermitPic = response.data.url;
                });
            } else {
                $scope.showPermitPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + permit_pic
            }


        };

        $scope.showFrontInvoicePicture = function (front_invoice) {
            $scope.invoiceFront = null;
            $rootScope.invoiceFront = null;
            $scope.$watch('front_invoice', function () {
                if (!front_invoice) {
                    setTimeout(function() {
                        $('#frontInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }, 500);
                    return;
                }

                if ($scope.isFileFromGoogleStorage(front_invoice)) {
                    $rootScope.invoiceFront = front_invoice;
                    $scope.invoiceFront = front_invoice;
                    return;
                }

                if (pic) {
                    AccountManagerServices.picLoadServerA(front_invoice).then(function (response) {

                        // console.log(response)
                        if (response.data.code.status === 200) {
                            $scope.invoiceFront = response.data.url;
                            $rootScope.invoiceFront = response.data.url;
                        } else {
                            $('#frontInvoice').modal('hide');
                            swal("oops!", "File not found.");
                        }
                    });
                } else {
                    $rootScope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                    $scope.invoiceFront = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + front_invoice
                }

            })
        };

        $scope.showBackInvoicePicture = function (back_invoice) {
            $scope.invoiceBack = null;
            $rootScope.invoiceBack = null;
            if (!back_invoice) {
                setTimeout(function() {
                    $('#backInvoice').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(back_invoice)) {
                $rootScope.invoiceBack = back_invoice;
                $scope.invoiceBack = back_invoice;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(back_invoice).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.invoiceBack = response.data.url;
                        $rootScope.invoiceBack = response.data.url;
                    } else {
                        $('#backInvoice').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $rootScope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
                $scope.invoiceBack = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + back_invoice
            }

        };

        $scope.isNullValue = function(value) {
            return value === null || value === undefined
                || (((typeof value) === "string") ? value.trim() : value) === "";
        }

        $scope.isObjectEmpty = function(obj) {
            return (obj === null || obj === undefined 
                || (Object.keys(obj).length === 0 && obj.constructor === Object));
        }

        $scope.isFileFromGoogleStorage = function (file) {
            return file.includes('https://storage.googleapis.com');
        };

        $scope.isPdf = function (url) {
            return url && url.toLowerCase().includes(".pdf");
        };

        $scope.isLRNotEditable = function (lrInfo) {
            return lrInfo.gobolt_business_app === true
                && (!$scope.isNullValue(lrInfo.lr_reciept)
                    || !$scope.isNullValue(lrInfo.pic_url));
        }

        $scope.showEwayBillPicture = function (e_way_bill_pic) {
            $scope.eWayBillPic = null;
            $rootScope.eWayBillPic = null;
            if (!e_way_bill_pic) {
                setTimeout(function() {
                    $('#e_way_bill_pic').modal('hide');
                    swal("oops!", "File not found.");
                }, 500);
                return;
            }

            if ($scope.isFileFromGoogleStorage(e_way_bill_pic)) {
                $rootScope.eWayBillPic = e_way_bill_pic;
                $scope.eWayBillPic = e_way_bill_pic;
                return;
            }

            if (pic) {
                AccountManagerServices.picLoadServerA(e_way_bill_pic).then(function (response) {

                    // console.log(response)
                    if (response.data.code.status === 200) {
                        $scope.eWayBillPic = response.data.url;
                        $rootScope.eWayBillPic = response.data.url;
                    } else {
                        $('#e_way_bill_pic').modal('hide');
                        swal("oops!", "File not found.");
                    }
                });
            } else {
                $rootScope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
                $scope.eWayBillPic = $location.protocol() + "://" + $location.host() + ":" + $location.port() + "/" + e_way_bill_pic
            }

        };

        /**********************************Driver Details Get**********8*************************************/

        $scope.driverDetailsAccount = function (orderAssign) {
            AccountManagerServices.driverDataAccount(orderAssign.id).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.driver = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };

        /****************************** Vehicle Data Get*******************************************************/
        $scope.vehicleDetailAccount = function (vehicleData) {
            AccountManagerServices.vehicleDataAccount(vehicleData.id).then(function (response) {
                if (response.data.code.status === 200) {
                    $scope.vehicle = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            }, function (error) {
                alert('Error Occurred')
            })
        };


        /**
         *Filter
         *Prafull
         */

        $scope.asyncRequestForFilter = function () {
            //Write here function for async request
            async.parallel([
                // function (callback) {
                //     // request no 1
                //     AccountManagerServices.getAllLrOngoingLists().then(function (response) {
                //
                //         if (response.data.results.code.status === 200) {
                //             $scope.lrName = response.data.results.data_list;
                //             callback();
                //         } else {
                //             alert(response.data.results.code.message)
                //         }
                //     }, function (err) {
                //         swal("Oops", 'No internet connection.', "error")
                //     });
                // },

                function (callback) {
                    //request no 2
                    AccountManagerServices.getAllCustomerLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.customerName = response.data.customer;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 3
                    AccountManagerServices.getAllLocationLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.locationNmae = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 4
                    AccountManagerServices.getAllBrokerList().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.brokerName = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                },

                function (callback) {
                    //request no 5
                    AccountManagerServices.getAllVehicleLists().then(function (response) {
                        if (response.data.code.status === 200) {
                            $scope.vehicleNo = response.data.data_list;
                            callback();
                        } else {
                            alert(response.data.code.message)
                        }
                    }, function (err) {
                        swal("Oops", 'No internet connection.', "error")
                    });
                }

            ])
        };

        $scope.asyncRequestForFilter();

        $scope.getLrNumbers = function (data) {
            if (data.length > 3) {
                var lr = {
                    'lr_num': data
                };
                AccountManagerServices.getLrNumSer(lr).then(function (response) {
                    console.log('response', response)
                    if (response.data.code.status === 200) {
                        $scope.lrName = response.data.lr;
                    } else {
                        swal("Cancelled", response.code.message, "error")
                    }
                })
            }
        };


        /**
         * functionName:getLocation
         * inputType:
         * outputType:
         * ownerName: Sushil
         * developedDate: 07/02/2018
         * testerName:
         * testDate:
         */
        $scope.getLocation = function (customerName) {
            //$scope.billModel.origin_location = '';
            //$scope.billModel.destination_location = '';
            AccountManagerServices.getLocationLists(customerName).then(function (response) {
                if (response.data.code.status === 200) {
                    //$scope.locations = response.data.location;
                    $scope.locationNmae = response.data.location;
                    //angular.forEach($scope.locationNmae, function (value, key) {
                    //    $scope.locationNmae[key].location_id = value.id;
                    //});
                    //console.log('new',$scope.locationNmae)
                } else {
                    alert(response.data.code.message)
                }
            }, function (error) {
                swal("Oops", 'No internet connection.', error)
            })
        };


        //$scope.distLocation = function (location) {
        //    angular.forEach($scope.locationNmae, function (value, key) {
        //        if (value.location_name == location.location_name)
        //            $scope.locationNmae.splice(key, 1);
        //    });
        //};


        $scope.pagination = {};
        $scope.filterData = {
            lr_no: '',
            customer: '',
            origin: '',
            destination: '',
            broker: '',
            date_vehicle_required: '',
            to_date_vehicle_required: '',
            vehicle_no: ''
        };


        $scope.ongoingTripFilter = function (count, filterData) {

            $scope.loading = true;
            this.filterData = filterData;
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.broker_id = '';
            $scope.customer_code = '';

            if (filterData != undefined) {
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id

            }
            $scope.filterDataObject = {
                customer: $scope.customer_code,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no,
                broker: $scope.broker_id,
            }
            console.log('$scope.filterDataObject', $scope.filterDataObject)
            //$scope.ongoingTripFilterPage = {'next': '', 'previous': '', 'count': ''};
            $scope.onGoingTrips = ''
            //$scope.ongoingTripFilterListing = function () {
            AccountManagerServices.ongoingTripFilterService(count, $scope.filterDataObject).then(function (response) {
                console.log('response', response);
                $scope.loading = false;
                if (count === undefined) {
                    $scope.pagination.current = 1;
                }
                if (response.data.results.code.status === 200) {

                    $scope.onGoingTrips = response.data.results.trip_data
                    $scope.wf = response.data.results.wf

                    //if ($scope.ongoingTripFilterPage.next) {
                    //    $scope.onGoingTrips = $scope.onGoingTrips.concat(response.data.results.trip_data)
                    //
                    //}
                    //else {
                    //    $scope.onGoingTrips = angular.extend(response.data.results.trip_data);
                    //
                    //}
                    //$scope.ongoingTripFilterPage.next = response.data.next;
                    //$scope.ongoingTripFilterPage.previous = response.data.previous;
                    $scope.ongoingTripPage.count = response.data.count;
                    //if ($scope.ongoingTripFilterPage.next != null) {
                    //    $scope.ongoingTripFilterListing()
                    //}
                } else {
                    swal(response.data.results.code.message)
                }
            });
            //};
            //    $scope.ongoingTripFilterListing();
        };


        $scope.clearFilterOngoing = function () {
            $scope.pagination.current = 1;
            //$scope.onGoingTrips = '';
            $scope.searchText = '';
            $scope.trip_listing();
            $scope.filterData = {
                lr_no: '',
                customer: '',
                origin: '',
                destination: '',
                broker: '',
                date_vehicle_required: '',
                to_date_vehicle_required: '',
                vehicle_no: ''
            };
        };

        /******************************* LR Edit for account *******************************************/

        $scope.getOwnLrDetails = function (orderAssign) {
            $scope.indexForLrEdit = $scope.onGoingTrips.indexOf(orderAssign)
            $rootScope.abc = $scope.indexForLrEdit;
            $rootScope.onGoingTrips = $scope.onGoingTrips


            var modalInstance = $modal.open({
                animation: true,
                templateUrl: '../static/apps/gobolt_opc/account_manager/template/trip/edit_lr_details.html',
                controller: [
                    '$scope', '$modalInstance',
                    function ($scope, $modalInstance) {
                        $scope.is_market = orderAssign.is_market;
                        $scope.onGoingTrips = $rootScope.onGoingTrips
                        console.log('$scope.onGoingTrips-->', $scope.onGoingTrips)
                        $scope.lrDataVehicle = {
                            'driver_photo_vehicle': '',
                            'seal_no': '',
                            'trip_id': ''
                        };
                        $scope.lrInfo = [{
                            lr_reciept: '',
                            pic_url: ''
                        }];
                        AccountManagerServices.getlrData(orderAssign.id).then(function (response) {
                            if (response.data.code.status == 200) {
                                $scope.lrInfo = response.data.data_list.lr;
                                $scope.lrDataVehicle.driver_photo_vehicle = response.data.data_list.driver_photo_vehicle;
                                $scope.lrDataVehicle.seal_no = response.data.data_list.seal_no;
                                $scope.lrDataVehicle.trip_id = response.data.data_list.trip_id;
                                $scope.lrDataVehicle.front_invoice = response.data.data_list.front_invoice;
                                $scope.lrDataVehicle.back_invoice = response.data.data_list.back_invoice;
                                $scope.lrDataVehicle.invoice_number = response.data.data_list.invoice_number;
                                $scope.lrDataVehicle.e_way_bill_data = response.data.data_list.e_way_bill;
                                console.log("$scope.lrDataVehicle", $scope.lrDataVehicle)
                            } else {
                                swal("Cancelled", response.code.message, "error")
                            }
                        }, function (err) {
                            swal("Oops", 'No internet connection.', "error")
                        });
                        $scope.lrInfoDetails = function (lrinfo, formType, data, lrEdit) {
                            $scope.lrEdit = lrEdit
                            if ($scope.lrEdit.$invalid == true && formType == 4) {

                            } else {
                                spinnerService.show('html5spinner');
                                $scope.lrInfoData = {
                                    'lrinfoData': lrinfo,
                                    'formType': formType,
                                    'dataVehicle': data
                                };
                            }

                        };

                        var wrapper = $(".input_fields_wrap");
                        //$scope.lrInfo = [{lr_reciept: '', pic_url: ''}];
                        $scope.lrAdditionalInfo = function () {
                            if ($scope.lrInfo.length >= 1) {
                                $scope.active = true
                            }
                            var newItemNo = $scope.lrInfo.length + 1;
                            $scope.lrInfo.push({
                                lr_reciept: '',
                                pic_url: ''
                            });
                        }

                        $scope.removeAddInfo = function (index) {
                            $scope.idx = 'remove_' + index
                            $scope.del_lr = 'remove_' + 1
                            if ($scope.idx === $scope.del_lr) {
                                $scope.active = false
                            }
                            //$scope.active = false
                            var lastItem = $scope.lrInfo.length - 1;
                            $scope.lrInfo.splice(lastItem);
                        };

                        $scope.cancel = function () {
                            $modalInstance.close();
                        };

                        $scope.uploadLrdetails = function (lrinfo, formType, data, lrEdit) {
                            console.log('$scope.indexForLrEdit1', $rootScope.abc)
                            $rootScope.updateLr = true
                            $scope.lrEdit = lrEdit

                            $scope.invoice_data = {
                                invoice_number: $scope.lrDataVehicle.invoice_number,
                                front_invoice: $scope.lrDataVehicle.front_invoice,
                                back_invoice: $scope.lrDataVehicle.back_invoice
                            }

                            $scope.e_way_bill_data = {
                                e_way_bill_number: $scope.lrDataVehicle.e_way_bill_data.e_way_bill_number,
                                e_way_bill: $scope.lrDataVehicle.e_way_bill_data.pic_url
                            }

                            if ($scope.lrEdit.$invalid == true) { } else {
                                spinnerService.show('html5spinner');
                                $scope.lrInfoData = {
                                    'lrinfoData': lrinfo,
                                    'formType': formType,
                                    'dataVehicle': data,
                                    'trip_id': $scope.lrDataVehicle.trip_id,
                                    'invoice_data': $scope.invoice_data,
                                    'e_way_bill_data': $scope.e_way_bill_data
                                };

                                AccountManagerServices.updateLR($scope.lrInfoData).then(function (response) {
                                    console.log('response-->', response)

                                    console.log('$scope.onGoingTrips', $scope.onGoingTrips)
                                    if (response.data.code.status === 200) {
                                        $timeout(function () {
                                            spinnerService.hide('html5spinner');
                                            swal("LR is sucessfully updated.", "", "success")
                                            $scope.onGoingTrips[$rootScope.abc].lr.lr_data = response.data.lr.lr_data;
                                            $scope.onGoingTrips[$rootScope.abc].invoice_data.front_invoice = response.data.invoice_data.front_invoice;
                                            $scope.onGoingTrips[$rootScope.abc].invoice_data.back_invoice = response.data.invoice_data.back_invoice;

                                            $('#uploadLrOngoing').modal('hide')
                                            $modalInstance.close();

                                        })
                                    } else {
                                        $scope.loading = false;
                                        swal("Oops!", response.data.code.message, "error");
                                        spinnerService.hide('html5spinner');
                                    }
                                },
                                    function (error) {
                                        alert('Error Occurred')
                                        $scope.loading = false;
                                        swal("Oops!", "Something went wrong.", "error");
                                    }
                                )
                            }
                        };

                    }
                ],
                size: 'lg',
            })


        }


        //Ashish
        $scope.trackTrip = function (trip) {
            $scope.track_wayPoints = [];
            $scope.track_lane = trip.lane;
            $scope.track_route = trip.route;
            $scope.driver_number = trip.order_data.driver_no;
            $scope.vehicle_number = trip.order_data.vehicle_no;
            if (trip.current_location) {
                $scope.lat = trip.current_location.lat;
                $scope.long = trip.current_location.long;
            }
            $scope.track_origin = trip.start_point.location_point.coordinates.coordinates[1] + ',' + trip.start_point.location_point.coordinates.coordinates[0];
            $scope.track_destination = trip.destination_point.location_point.coordinates.coordinates[1] + ',' + trip.destination_point.location_point.coordinates.coordinates[0];
            if ($scope.track_route.origin_start && $scope.track_lane.lane_name) {
                if (!trip.current_location) {
                    $scope.lat = '';
                    $scope.long = '';
                }
                $scope.customIcon = {
                    "scaledSize": [60, 60],
                    "url": "http://www.garminqatar.com/static.garmincdn.com/en/m/g/market-page-images/on-the-road/ic-map-marker.png"
                };
                $scope.track_lane.location.data.forEach(function (item) {
                    $scope.track_wayPoints.push({
                        location: {
                            lat: item.location_point.coordinates.coordinates[1],
                            lng: item.location_point.coordinates.coordinates[0]
                        },
                        stopover: true
                    })
                });
            }
        };

        $('#myModalTrack').on('show.bs.modal', function (e) {
            $timeout(function () {
                google.maps.event.trigger($scope.trackingMap, 'resize');
                $scope.trackingMap.setCenter({
                    lat: 20.5937,
                    lng: 78.9629
                });
                $scope.trackingMap.setZoom(4);
            });
        });

        $scope.sortColumn = '';
        $scope.reverseSort = false;
        $scope.sortData = function (column) {
            console.log('column', column)
            console.log('$scope.sortColumn', $scope.sortColumn)
            $scope.reverseSort = ($scope.sortColumn == column ? !$scope.reverseSort : false)
            $scope.sortColumn = column
        };

        $scope.getSortClass = function (column) {
            if ($scope.sortColumn == column) {
                //return $scope.reverseSort ? 'arrow-down':'arrow-up'
                return ('fa fa-chevron-' + (($scope.reverseSort) ? 'down' : 'up'));
            } else {
                return 'fa fa-sort'
            }
            //return '';
        };
        /**
         * CSV file download ongoing trip
         */

        $scope.onGoingExportData = function (filterData) {
            $scope.csvloading = true;
            $scope.vehicle_no = '';
            $scope.origin_name = '';
            $scope.destination_name = '';
            $scope.date_vehicle_required = '';
            $scope.to_date_vehicle_required = '';
            $scope.lr_no = '';
            $scope.broker_id = '';
            $scope.customer_code = '';

            if (filterData != undefined) {
                if (filterData.vehicle_number)
                    $scope.vehicle_no = filterData.vehicle_number.vehicle_registration_number;

                if (filterData.origin)
                    $scope.origin_name = filterData.origin.location_name;

                if (filterData.destination)
                    $scope.destination_name = filterData.destination.location_name;

                if (filterData.date_vehicle_required)
                    $scope.date_vehicle_required = filterData.date_vehicle_required;

                if (filterData.to_date_vehicle_required)
                    $scope.to_date_vehicle_required = filterData.to_date_vehicle_required;

                if (filterData.lr)
                    $scope.lr_no = filterData.lr.lr_reciept;

                if (filterData.broker)
                    $scope.broker_id = filterData.broker.id;

                if (filterData.customer)
                    $scope.customer_code = filterData.customer.id

            }
            $scope.filterDataObject = {
                customer: $scope.customer_code,
                vehicle_no: $scope.vehicle_no,
                origin: $scope.origin_name,
                destination: $scope.destination_name,
                from_date: $scope.date_vehicle_required,
                to_date: $scope.to_date_vehicle_required,
                lr: $scope.lr_no,
                broker: $scope.broker_id
            };

            AccountManagerServices.postExportData($scope.filterDataObject).then(function (response) {
                console.log('response', response);
                if (response.data.code.status === 200) {
                    $scope.onGoingExport = response.data.trip_data;
                    $scope.downloadOnGoingCsv = [];

                    angular.forEach($scope.onGoingExport, function (value, key) {
                        $scope.lr_no = "";
                        angular.forEach(value.lr.lr_data, function (value, key) {
                            $scope.lr_no = value.lr_reciept + ',' + $scope.lr_no
                        });

                        if (value.trip_end_date === null) {
                            $scope.trip_end_date = ''
                        } else {
                            $scope.trip_end_date = value.trip_end_date.split('T')[0];
                        }

                        if (value.trip_end_time === null) {
                            $scope.trip_end_time = ''
                        } else {
                            $scope.trip_end_time = value.trip_end_time.split('T')[1]

                        }

                        if (value.trip_start_date === null) {
                            $scope.start_date = ''
                        } else {
                            $scope.start_date = value.trip_start_date.split('T')[0]
                        }

                        if (value.trip_start_time === null) {

                            $scope.start_time = ''
                        } else {
                            $scope.start_time = $filter('date')(value.trip_start_time, 'HH:mm');
                        }
                        if (value.is_market === true) {
                            $scope.market = 'yes'
                        } else {
                            $scope.market = 'no'
                        }
                        if (value.lane.lane_code === undefined) {
                            $scope.route = ''
                        } else {
                            $scope.route = value.lane.lane_code
                        }
                        $scope.onGoingExportValue = {
                            "Trip Code": value.trip_code,
                            "Market": $scope.market,
                            "Lr No": $scope.lr_no,
                            "Customer": value.company_name + '<' + value.order_data.customer_code + '>',
                            "Origin": value.order_data.origin.location_name,
                            "Destination": value.order_data.destination.location_name,
                            "Driver Name": value.order_data.driver_name + '/' + value.order_data.driver_no,
                            "Vehicle No": value.order_data.vehicle_no,
                            "Route": $scope.route,
                            "Trip Status": value.trip_status.status_name,
                            "Placement Date": value.vehicle_placement_date,
                            "Placement Time": value.vehicle_placement_time,
                            "Start Date": $scope.start_date,
                            "Start Time": $scope.start_time,
                            "Reporting Date": $scope.trip_end_date,
                            "Reporting Time": $scope.trip_end_time,
                            "Buyer/hm": value.buyer_data.name + '/' + value.buyer_data.code
                        };
                        $scope.downloadOnGoingCsv.push($scope.onGoingExportValue)
                    });
                    var mystyle = {
                        headers: true,
                        column: {
                            style: {
                                Font: {
                                    Bold: "1"
                                }
                            }
                        }
                    };
                    alasql('SELECT * INTO XLS("Ongoing Trips.xls",?) FROM ?', [mystyle, $scope.downloadOnGoingCsv = $filter('orderBy')($scope.downloadOnGoingCsv, 'placement_date')]);
                    $scope.csvloading = false;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };

        /**
         * Update Info of Driver in trip
         */

        $scope.editDriverInfo = function (data) {
            $scope.driver_code = data.order_data.driver_code;
            $scope.updateDriverDataTrip = {
                'driver_code': data.order_data.driver_code,
                'trip_id': data.id
            };
            swal({
                title: "Are you sure?",
                text: "You want to update driver info.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, update it!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    AccountManagerServices.updateDriverInfoTrip($scope.updateDriverDataTrip).then(function (response) {
                        console.log('res', response)
                        if (response.code.status == 200) {
                            swal("Good Job!", "Driver Data Updated Successfully.", "success")
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                } else {
                    swal("Cancelled", "Driver details not updated:)", "error");
                }
            })
        };

        /**
         * Update Vehicle Market Data
         */

        $scope.editVehicleInfo = function (data) {
            $scope.updateVehicleDataTrip = {
                'trip_id': data.id
            }
            console.log('DATAAA--->', data)
            swal({
                title: "Are you sure?",
                text: "You want to update vehicle info.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, update it!",
                cancelButtonText: "No, cancel !",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    AccountManagerServices.updateVehicleInfoTrip($scope.updateVehicleDataTrip).then(function (response) {
                        console.log('res', response)
                        if (response.code.status == 200) {
                            swal("Good Job!", "Vehicle Data Updated Successfully.", "success")
                        } else {
                            swal("Cancelled", response.code.message, "error")
                        }
                    })
                } else {
                    swal("Cancelled", "Driver details not updated:)", "error");
                }
            })
        };

        /******************************Co-Driver Data Get*******************************************************/

        $scope.coDriverDetailsPlanning = function (orderAssign) {
            AccountManagerServices.coDriverDataPlanning(orderAssign.id).then(function (response) {
                if (response.data.code.status == 200) {
                    $scope.driver = response.data.data_list;
                } else {
                    swal("Cancelled", response.code.message, "error")
                }
            });
        };


    }
]);
/*********************************************************************************
 * ControllerName:lr.listing
 * TemplateName:
 * Urls:
 * ownerName: Sushil
 * developedDate: 28/07/2017
 *********************************************************************************/
app.controller('lr.listing', ['$scope', 'AccountManagerServices', function ($scope, AccountManagerServices) {


    $scope.$watch('online', function (newStatus) {
        if (newStatus === false) {
            swal("internet Connection Lost!")
        }
    });
    $scope.lr_submit = function (lrNumber) {
        $scope.showFinanceData = true;
        $scope.lr_no = lrNumber.lr_no
        AccountManagerServices.getLrDetailes($scope.lr_no).then(function (response) {
            if (response.data.code.status == 200) {
                $scope.lr_number = response.data.data_list;
            } else {
                swal("Cancelled", response.data.code.message, "error")
            }
        }, function (err) {
            alert('Error Occurred')
        })
    }

    $('body').on('hidden.bs.modal', '.modal', function () {
        $(this).removeData();
    });

}]);