<style>
    .contract-item {
        padding: 5px 0;
    }

    .contract-id {
        font-weight: bold;
        color: #333;
    }

    .contract-locations {
        margin-top: 5px;
    }



    .ui-select-choices-row .location-tag {
        display: inline-block;
        background-color: #428BCA;
        color: white;
        padding: 2px 8px;
        margin: 2px;
        border-radius: 3px;
        font-size: 12px;
    }

    .ui-select-container {
        width: 100%;
    }

    .ui-select-choices-row.active .location-tag {
        background-color: #fff;
        color: #333;
    }

    .locations {
        overflow-x: auto;
        white-space: nowrap;
    }

    /* Webkit (Chrome, Safari) */
    .locations:-webkit-scrollbar {
        height: 1px;
        width: 1px;
    }

    .locations::-webkit-scrollbar-thumb {
        background: #888;
    }

    .locations::-webkit-scrollbar-track {
        background: #F0F0F0;
    }
</style>
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <!-- Horizontal Form -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Indent Placement</h3>
                </div>
                <!-- form start -->
                <form name="indentForm" class="form-horizontal" novalidate="">
                    <div class="box-body col-md-6">
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Customer</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.customer.$invalid && (submitted || indentForm.customer.$dirty) }">

                                <ui-select name="customer" ng-model="data.customer"
                                           ng-click=""
                                           ng-change="getLocation(data.customer); fetch_ids(data.customer.customer_code, data.start_point.location_code, desMulti[desMulti.length - 1].destination_point.location_code);"
                                           theme="bootstrap"
                                           ng-required="true">
                                    <ui-select-match
                                            placeholder="Select customer">
                                        {{ $select.selected.company_name }}
                                    </ui-select-match>
                                    <ui-select-choices
                                            repeat="item in customerName | filter: $select.search track by $index">
                                        <div ng-bind-template="{{ item.company_name }}<{{ item.customer_code }}> "></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="indentForm.customer.$error.required && (submitted || indentForm.customer.$dirty)"
                                   class="help-block">Customer name is required.</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Start Point</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.start.$invalid && (submitted || indentForm.start.$dirty)}">
                                <ui-select name="start" ng-model="data.start_point"
                                           theme="bootstrap"
                                           ng-required="true" ng-click="checkSourceDestination(data.customer)"
                                           ng-change="des_loc_mul(data.start_point, $select); fetch_ids(data.customer.customer_code, data.start_point.location_code, desMulti[desMulti.length - 1].destination_point.location_code)">
                                    <ui-select-match
                                            placeholder="Select start point" allow-clear="true">
                                        {{$select.selected.location_name}}
                                    </ui-select-match>
                                    <ui-select-choices
                                            repeat="item in locations | filter: $select.search  track by $index">
                                        <div ng-bind-html="item.location_name | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="indentForm.start.$error.required && (submitted || indentForm.start.$dirty)"
                                   class="help-block">Start point is required.</p>
                            </div>
                        </div>
                        <div ng-repeat="stopageInfo in desMulti">

                            <div class="form-group" style="display: flex;align-items: center;">
                                <label ng-if="waypoint_len == $index"  for="inputPassword3" class="col-sm-5 control-label">Destination Point</label>
                                <label ng-if="(waypoint_len - $index) > 0" for="inputPassword3" class="col-sm-5 control-label"
                                    style="line-height: normal;padding-top: 0;">
                                    Waypoint
                                    <small ng-if="!stopageInfo.is_active" ng-class="{'red': true}" style="display: block;font-weight: 600;">Inactive</small>
                                </label>

                                <div ng-if="waypoint_len == $index" class="col-sm-7"
                                     ng-class="{ 'has-error' : indentForm.destination.$invalid && (submitted || indentForm.destination.$dirty) }">
                                    <ui-select name="destination" ng-model="stopageInfo.destination_point"
                                               theme="bootstrap"
                                               ng-required="true" ng-click="checkSourceDestination(data.customer)"
                                               ng-change="multiCheckAgain(stopageInfo.destination_point, $select); fetch_ids(data.customer.customer_code, data.start_point.location_code, desMulti[desMulti.length - 1].destination_point.location_code)">
                                        <ui-select-match
                                                placeholder="Select destination point" allow-clear="true">
                                            {{$select.selected.location_name}}
                                        </ui-select-match>
                                        <ui-select-choices
                                                repeat="item in des_locations_mul | filter: $select.search  track by $index">
                                            <div ng-bind-html="item.location_name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>

                                    <p ng-show="indentForm.destination.$error.required && (submitted || indentForm.destination.$dirty)"
                                       class="help-block">Destination point is required.</p>
                                </div>

                                <div ng-if="(waypoint_len - $index) > 0" class="col-sm-7"
                                     ng-class="{ 'has-error' : indentForm.destination.$invalid && (submitted || indentForm.destination.$dirty) }">
                                    <ui-select name="destination" ng-model="stopageInfo.destination_point"
                                               theme="bootstrap"
                                               ng-disabled="isDisabled"
                                               ng-required="true" ng-click="checkSourceDestination(data.customer)"
                                               ng-change="multiCheckAgain(stopageInfo.destination_point, $select); fetch_ids(data.customer.customer_code, data.start_point.location_code, desMulti[desMulti.length - 1].destination_point.location_code)">
                                        <ui-select-match
                                                placeholder="Select destination point" allow-clear="true">
                                            {{$select.selected.location_name}}
                                        </ui-select-match>
                                        <ui-select-choices
                                                repeat="item in des_locations_mul | filter: $select.search  track by $index">
                                            <div ng-bind-html="item.location_name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>

                                    <p ng-show="indentForm.destination.$error.required && (submitted || indentForm.destination.$dirty)"
                                       class="help-block">Destination point is required.</p>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c_id" class="col-sm-5 control-label">Contract ID</label>
                        
                            <div class="col-sm-7"
                                ng-class="{ 'has-error' : indentForm.c_id.$invalid && (submitted || indentForm.c_id.$dirty) }">
                        
                                <ui-select name="c_id" ng-model="data.contract_id" ng-change="getFormDetails(data.contract_id)"
                                    theme="bootstrap" ng-required="true">
                                    <ui-select-match allow-clear="true" placeholder="Select Contract ID">
                                        {{ $select.selected.contract_id ? $select.selected.contract_id : 'No Contract Found' }}
                                    </ui-select-match>
                                    <ui-select-choices repeat="contract in contract_ids | filter: $select.search">
                                        <div class="contract-option">
                                            <div ng-bind-html="(contract.contract_id ? contract.contract_id : 'No Contract Found') | highlight: $select.search"></div>
                                            <div class="locations" ng-if="contract.waypoints && contract.waypoints.length > 0">
                                                <span ng-repeat="location in contract.waypoints" class="location-tag">
                                                    {{location}}
                                                </span>
                                            </div>
                                        </div>
                                    </ui-select-choices>
                                </ui-select>
                        
                                <p ng-show="indentForm.c_id.$error.required && (submitted || indentForm.c_id.$dirty)" class="help-block">
                                    Contract ID is required.</p>
                            </div>
                        </div>

                        <div class="form-group" ng-if="data.contract_id">
                            <label for="inputPassword3" class="col-sm-5 control-label">Tat</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.tat.$invalid && (submitted || indentForm.tat.$dirty) }">
                                <input type="text" class="form-control" name="tat"
                                       ng-model="data.tat" placeholder="Tat" maxlength="20"
                                       ng-pattern="/^[0-9]+(\.[0-9]{1,10})?$/" min="1" max="1000" required/>

                                <p ng-show="indentForm.tat.$error.pattern" class="help-block">Please enter numbers only.</p>

                                <p ng-show="indentForm.tat.$error.min" class="help-block">Minimum number of
                                    tat hours must be greater than 0.</p>

                                <p ng-show="indentForm.tat.$error.max" class="help-block">Maximum number of
                                    tat should be 1000.</p>

                                <p ng-show="indentForm.tat.$error.required && (submitted || indentForm.tat.$dirty)"
                                   class="help-block">Tat is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Vehicle Type</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.vehicle_type.$invalid && (submitted || indentForm.vehicle_type.$dirty) }">

                                <ui-select name="vehicle_type" ng-model="data.vehicle_type"
                                           theme="bootstrap"
                                           ng-disabled="isDisabled"
                                           ng-required="true">
                                    <ui-select-match
                                            placeholder="Select vehicle type">
                                        {{$select.selected.name}}
                                    </ui-select-match>
                                    <ui-select-choices
                                            repeat="item in vehicleType | filter: $select.search track by $index">
                                        <div ng-bind-html="item.name | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="indentForm.vehicle_type.$error.required && (submitted || indentForm.vehicle_type.$dirty)"
                                   class="help-block">Vehicle type is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Peak Contract Rate</label>

                            <div class="col-sm-7">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" ng-model="data.is_Ad_hoc" value="false">
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div ng-if='data.is_Ad_hoc==true' class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Peak Rate</label>

                            <div class="col-sm-7">

                                <label>
                                    <input type="number" ng-model="data.ad_hoc_contract" min="0">
                                </label>

                            </div>
                        </div>

                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Separate Indent</label>

                            <div class="col-sm-7">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" ng-model="data.separate_indent" value="false">
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-body col-md-6">

                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Placement Type</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.placement_t.$invalid && (submitted || indentForm.placement_t.$dirty) }">
                                <ui-select name="placement_t" ng-model="data.placement_type"
                                           theme="bootstrap"
                                           ng-required="true">
                                    <ui-select-match
                                            placeholder="Scheduled">
                                        {{$select.selected}}
                                    </ui-select-match>
                                    <ui-select-choices
                                            repeat="item in indentType | filter: $select.search  track by $index">
                                        <div ng-bind-html="item | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>

                                <p ng-show="indentForm.placement_t.$error.required && (submitted || indentForm.placement_t.$dirty)"
                                   class="help-block">Placement Type is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-5 control-label">Date</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.date.$invalid && (submitted || indentForm.date.$dirty) }">
                                <input class="form-control" type="text" name="date" autocomplete="off"
                                       ng-model="data.date_vehicle_required" placeholder="Select Date" jqdatepicker=""
                                       required/>

                                <p ng-show="indentForm.date.$error.required && (submitted || indentForm.date.$dirty) "
                                   class="help-block">
                                    Date is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Time</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.time.$invalid && (submitted || indentForm.time.$dirty)  }">
                                <input class="form-control" name="time" type="text" time autocomplete="off"
                                       ng-model="data.time_vehicle_required" placeholder="HH:MM" required="true"/>

                                <p ng-show="indentForm.time.$error.required && (submitted || indentForm.time.$dirty)"
                                   class="help-block">
                                    Time is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Number of vehicle</label>

                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.no_of_vehicle.$invalid && (submitted || indentForm.no_of_vehicle.$dirty) }">
                                <input type="number" class="form-control" name="no_of_vehicle"
                                       ng-model="data.no_of_vehicle" placeholder="Number of vehicle" maxlength="20"
                                       ng-pattern="/^[0-9]*$/" min="1" max="10" required/>

                                <p ng-show="indentForm.no_of_vehicle.$error.pattern" class="help-block">Number of
                                    vehicle contains numbers only.</p>

                                <p ng-show="indentForm.no_of_vehicle.$error.min" class="help-block">Minimum number of
                                    vehicle should be 1.</p>

                                <p ng-show="indentForm.no_of_vehicle.$error.max" class="help-block">Maximum number of
                                    vehicles should be 10.</p>

                                <p ng-show="indentForm.no_of_vehicle.$error.required && (submitted || indentForm.no_of_vehicle.$dirty)"
                                   class="help-block">No of vehicle is required.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Vehicle Reference ID</label>
                            <div class="col-sm-7"
                                 ng-class="{ 'has-error' : indentForm.demand_reference_id.$invalid && (submitted || indentForm.demand_reference_id.$dirty) }">
                                <input type="text" class="form-control" name="demand_reference_id"
                                       ng-model="data.demand_reference_id" placeholder="Vehicle Reference ID. (,) Separate"
                                />

                                <p
                                    ng-show="indentForm.demand_reference_id.$error && (submitted || indentForm.demand_reference_id.$dirty)"
                                    class="help-block">
                                    <!-- Vehicle Reference ID Should be equal to number of vehicle required -->
                                </p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-5 control-label">Mark as repetitive</label>

                            <div class="col-sm-7">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox" style="position:relative">
                                        <label ng-if="isDisabledCheckBox">
                                            <input type="checkbox" ng-model="data.is_repetitive" value="false" ng-disabled="isDisabledCheckBox" />
                                            <span data-toggle="tooltip" data-placement="top" data-tooltip="Repetitive indents can not be created for past dates" style="height:20px;width:20px;display:inline-block;position:absolute;left:0px;top:11px;"></span>
                                        </label>     
                                        <label ng-if="!isDisabledCheckBox">
                                            <input type="checkbox" ng-model="data.is_repetitive" value="false" ng-disabled="isDisabledCheckBox" />
                                        </label>                              
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-footer ">
                        <button type="submit" class="btn btn-info pull-right apply-button" style="margin-right: 10px" ng-disabled=hit
                                ng-click="customerIndent(data, desMulti)">Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<div ng-include="'../static/apps/gobolt_admin/template/indent_mgmt/favorite_indent_list.html'"></div>
