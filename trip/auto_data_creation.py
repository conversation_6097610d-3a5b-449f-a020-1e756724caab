from trip.models import Trip, TripStatusMaster, UnscheduleStopage, TripLiveData
from django.contrib.auth.models import User
from trip.external_methods import get_status_master
from administrator.models import (
    LocationCusCitySequence,
    LocationHubCitySequence,
    LocationTollCitySequence,
    LocationRefuelCitySequence,
    TripSequence,
    MarketDriverSequence,
    LocationRouteCitySequence,
    LocationBorderCitySequence,
    LocationServiceStationCitySequence,
)
from django.db.models import Q
from trip.external_methods import return_dict_for_tracking_data, return_trip_id
from customer_care.models import Order
from mongoengine.queryset.visitor import Q as Qm
import datetime
import sys

# import itertools
import json
from django.core.serializers.json import DjangoJSONEncoder
from trip import global_objects
from customer_care.user_define_methods import increment_value, increment_value_m
from account_manager import code_veriable
from trip.py_mongo_service import pymongo_crud
from bson.objectid import ObjectId
from gobolt_business.external_methods import GoboltBusinessUpdate


def update_required_date_time(indent_id, r_datetime):
    trip = pymongo_crud.db["trip"]
    trip.update_many(
        {"order_data.indent_id": indent_id},
        {"$set": {"vehicle_required_date_time": r_datetime}},
    )


def market_driver_code():
    driver_code = None
    try:
        m_d_s_obj, current_var = increment_value_m(MarketDriverSequence)
        # m_d_s_obj.current_var = current_var
        # m_d_s_obj.save()
        driver_code_digits_default = "0000"
        driver_code = (
            code_veriable.driver_prefix
            + "|"
            + driver_code_digits_default[len(str(current_var)) :]
            + str(current_var)
        )

    except:
        pass
    return driver_code


########################################################################################################################
# Name - create_trip_code
#
########################################################################################################################


def create_trip_code(trip_type, customer_code, route_code, date_pass=None):
    trip_code = None
    try:
        if date_pass is None:
            date_data = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d")
        else:
            date_data = datetime.datetime.strftime(date_pass, "%Y-%m-%d")
        yy, mm, dd = (date_data[0:4], date_data[5:7], date_data[8:11])
        trip_s_obj, current_var = increment_value(TripSequence)
        # if 1 <= len(str(current_var)) <= 6:
        #     current_var = current_var
        # else:
        #     current_var = 1
        # trip_s_obj.current_var = current_var
        # trip_s_obj.save()
        max_indent_digit = str(code_veriable.max_digit_trip)
        trip_code = (
            yy
            + mm
            + dd
            + "|"
            + trip_type
            + "|"
            + customer_code
            + "|"
            + route_code
            + "|"
            + max_indent_digit[len(str(current_var)) :]
            + str(current_var)
        )

    except Exception as e:
        print(e)
        pass
    return trip_code


########################################################################################################################
# Name - create_trip
#
########################################################################################################################


def create_trip(
    order_id,
    order_data,
    bilty_boy,
    account_manager,
    tat,
    vehicle_required_date_time=None,
):
    trip_status_master = TripStatusMaster.objects
    status_master = get_status_master(trip_status_master, "New")
    result = False
    # try:
    if order_id and order_data is not None:
        Trip(
            order_id=order_id,
            order_data=order_data,
            trip_status=status_master,
            bilty_boy=bilty_boy,
            account_manager=account_manager,
            tat=tat,
            vehicle_required_date_time=vehicle_required_date_time,
        ).save()
        result = True
    # except Exception as e:
    #     print e
    #     pass
    return result


def change_contract_update_trip(orders_id, indent_obj, contract_obj):
    trip = pymongo_crud.db["trip"]
    query = {"order_id": orders_id}
    trip_obj = trip.find_one(query)
    if trip_obj:
        order_data = trip_obj["order_data"]
        order_data["multiple_stopage"] = indent_obj.multi_stopage
        order_data["contract_id"] = contract_obj.contract_id
        set_dict = dict(
            order_data=order_data,
            tat=contract_obj.tat
        )
        set_values = {"$set": set_dict}
        trip.update(query, set_values)
        live_data = TripLiveData.objects.filter(trip_id=str(trip_obj['_id']))
        if live_data:
            _obj = trip.find_one({"order_id": trip_obj['order_id']})
            t_data = return_dict_for_tracking_data(
                    _obj, Order.objects.get(id=trip_obj["order_id"]), None
                )
            t_data = json.dumps(t_data, cls=DjangoJSONEncoder)
            live_data.update(track_data=t_data)
            
    else:
        raise Exception("Trip data not found")


def create_trip_repeated_indent(
    order_id,
    order_data,
    bilty_boy,
    account_manager,
    tat,
    vehicle_required_date_time=None,
):
    trip = pymongo_crud.db["trip"]
    trip_status_master = pymongo_crud.db["trip_status_master"]
    status_master = trip_status_master.find_one({"status_name": "New"})
    status_master_1 = {"id": str(status_master['_id']), "status_name": str(status_master['status_name'])}
    result = False
    if order_id and order_data is not None:
        insert_trip_obj = Trip(
            order_id=order_id,
            order_data=order_data,
            trip_status=status_master_1,
            bilty_boy=bilty_boy,
            account_manager=account_manager,
            tat=tat,
            vehicle_required_date_time=vehicle_required_date_time
        )
        insert_trip_json_str = insert_trip_obj.to_json()
        insert_trip_dict = json.loads(insert_trip_json_str)
        insert_trip_dict['vehicle_required_date_time']=vehicle_required_date_time
        trip.insert_one(insert_trip_dict)
        result = True
    return result


# Create Code with cod


def create_code(name, code):
    combination_code = name + "<" + code + ">"
    return combination_code


########################################################################################################################
# Name - create_code_vehicle
#
########################################################################################################################
def create_code_vehicle(prefix, vehicle_no):
    vehicle_code = prefix + vehicle_no
    return vehicle_code


########################################################################################################################
# Name - lanes_internal
#
########################################################################################################################
def lanes_internal(*args):
    lanes_code = None
    route_code = args[1].split("-")
    prefix = route_code[0]
    suffix = route_code[1]
    # route_code should have only origin and destination
    for i in range(0, len(args[0])):
        if lanes_code is not None:
            lanes_code += "-" + args[0][i]
        else:
            lanes_code = prefix + "-" + args[0][i]
    return lanes_code + "-" + suffix


########################################################################################################################
# Name - create_code_lane
#
########################################################################################################################
def create_code_lane(lanes_array, route_code):
    location_codes = [
        lanes_array[i]["location_code"] for i in range(0, len(lanes_array))
    ]
    # Yogender 0112161744 - Added the case when lane locations are not there.
    if len(lanes_array):
        return lanes_internal(location_codes, route_code)
    else:
        return route_code


########################################################################################################################
# Name - create_code_route
#
########################################################################################################################
def create_code_route(f_location_code, s_location_code):
    route_code = f_location_code + "-" + s_location_code
    return route_code


########################################################################################################################
# Name - create_code_location
#
########################################################################################################################
def create_code_location(city_code, type_data, data_user, city_id):
    location_code = None
    if type_data == "S":
        data_obj = LocationServiceStationCitySequence.objects.filter(city_id=city_id)
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            data_obj.update(current_var=current_var)
            location_code = city_code + type_data + str(current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationServiceStationCitySequence(
                current_var=current_var, city_id=city_id
            ).save()

    elif type_data == "B":
        data_obj = LocationBorderCitySequence.objects.filter(city_id=city_id)
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            data_obj.update(current_var=current_var)
            location_code = city_code + type_data + str(current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationBorderCitySequence(current_var=current_var, city_id=city_id).save()
    elif type_data == "C":
        data_obj = LocationCusCitySequence.objects.filter(
            Q(customer_id=data_user["id"]) & Q(city_id=city_id)
        )
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            data_obj.update(current_var=current_var)
            location_code = city_code + type_data + str(current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationCusCitySequence(
                customer_id=data_user["id"], current_var=current_var, city_id=city_id
            ).save()
    elif type_data == "H":
        data_obj = LocationHubCitySequence.objects.filter(city_id=city_id)
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            location_code = city_code + type_data + str(current_var)
            data_obj.update(current_var=current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationHubCitySequence(city_id=city_id, current_var=current_var).save()
            # data_obj.save()
    elif type_data == "T":
        data_obj = LocationTollCitySequence.objects.filter(city_id=city_id)
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            location_code = city_code + type_data + str(current_var)
            data_obj.update(current_var=current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationTollCitySequence(city_id=city_id, current_var=current_var).save()
    elif type_data == "R":
        data_obj = LocationRefuelCitySequence.objects.filter(city_id=city_id)
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            location_code = city_code + type_data + str(current_var)
            data_obj.update(current_var=current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationRefuelCitySequence(city_id=city_id, current_var=current_var).save()
    elif type_data == "L":
        data_obj = LocationRouteCitySequence.objects.filter(Q(city_id=city_id))
        if len(data_obj):
            current_var = data_obj[0].current_var
            inc_var = data_obj[0].inc_var
            current_var += inc_var
            data_obj.update(current_var=current_var)
            location_code = city_code + type_data + str(current_var)
        else:
            current_var = 1
            location_code = city_code + type_data + str(current_var)
            LocationRouteCitySequence(current_var=current_var, city_id=city_id).save()
        print("---->")
    return location_code


# Getting User ID of current user
# Not completed
def get_current_user_status(user_info):
    try:
        user_data = User.objects.filter(username=user_info)
    except Exception as e:
        print(e)
    return user_data[0].id


# Getting customer based on login user


def create_live_tracking_data():
    trip = pymongo_crud.db["trip"]
    status_master = pymongo_crud.db["trip_status_master"]
    status_master_1 = status_master.find_one({"status_name": "In Transit"})
    status_master_2 = status_master.find_one({"status_name": "Vehicle Reported"})
    b = {
        "$or": [
            {"trip_status.id": str(status_master_1["_id"])},
            {"trip_status.id": str(status_master_2["_id"])},
        ]
    }
    try:
        t_obj = trip.find(b).sort(
            [("vehicle_placement_date", -1), ("vehicle_placement_time", -1)]
        )
        for i in t_obj:

            t_data = return_dict_for_tracking_data(
                i, Order.objects.get(id=i["order_id"]), None
            )
            delay_status_backend = t_data.get("delay_status_backend")

            i_query = {"_id": ObjectId(i["_id"])}
            i_set_dict = {
                "delay_status_backend": delay_status_backend
            }
            set_values = {"$set": i_set_dict}
            trip.update(i_query, set_values)

            trip_id = t_data["id"]
            t_data = json.dumps(t_data, cls=DjangoJSONEncoder)
            live_data = TripLiveData.objects.filter(trip_id=trip_id)
            if live_data.count() > 1:
                TripLiveData.objects.filter(id=live_data[0].id).delete()
                live_data = TripLiveData.objects.filter(trip_id=trip_id)
            if live_data:
                live_data.update(track_data=t_data)
            else:
                TripLiveData(track_data=t_data, trip_id=trip_id).save()

            # update gobolt business app details
            GoboltBusinessUpdate().eta_final_delay_duration_update(i, delay_status_backend)

        t_obj = trip.find(b)
        r = pymongo_crud.db["trip_live_data"].delete_many(
            {"trip_id": {"$nin": [str(i["_id"]) for i in t_obj]}}
        )
        print("====> Deleted ", r.deleted_count)
    except Exception as e:
        print(
            "Exception is :- \n~",
            e.args[0],
            "\noccurred at line number:",
            sys.exc_info()[2].tb_lineno,
        )

    t_obj = trip.find(b)
    r = pymongo_crud.db["trip_live_data"].delete_many(
        {"trip_id": {"$nin": [str(i["_id"]) for i in t_obj]}}
    )
    print("====> Deleted ", r.deleted_count)
